# product_view_close 极光埋点移除测试文档

## 概述
本文档记录了移除 `product_view_close` 极光埋点事件的详细信息，包括修改的文件、方法位置、影响范围和测试建议。

## 修改内容

### 1. 移除的埋点事件
- **事件名称**: `product_view_close`
- **事件类型**: 极光埋点 (AnalysysAgent)
- **功能**: 记录商品详情页关闭时的用户行为数据

### 2. 修改的文件

#### 2.1 YBMProductDetailController.m
**文件路径**: `YaoBangMang/Classes/Modules/Product/Detail/Controller/YBMProductDetailController.m`

**修改方法**: `auroraAnalysisWithProductDetailClose` (第1239-1246行)

**修改前**:
```objc
// 商详页关闭埋点
- (void)auroraAnalysisWithProductDetailClose {
    NSMutableDictionary *mutD = [NSMutableDictionary dictionary];
    // ... 设置各种参数
    [AnalysysAgent track:product_view_close properties:mutD];
}
```

**修改后**:
```objc
// 商详页关闭埋点 - 极光埋点已移除
- (void)auroraAnalysisWithProductDetailClose {
    // 极光埋点 product_view_close 已移除
    // 如果需要其他埋点系统的页面关闭统计，可以在此处添加
    
    // 保留方法以确保调用兼容性
    // 可以在此处添加其他埋点系统的实现，如QT埋点或雪地埋点
}
```

**调用位置**: `viewWillDisappear:` 方法 (第185行)
```objc
- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [IQKeyboardManager sharedManager].enable = YES;
    [[NSNotificationCenter defaultCenter] postNotificationName:@"DetailControllerViewWillDisappear" object:nil];
    
    // 商详页关闭埋点
    [self auroraAnalysisWithProductDetailClose];
}
```

#### 2.2 YBMAnalyticsEvents.h
**文件路径**: `YaoBangMang/Classes/Vendor/Analytics/YBMAnalyticsEvents.h`

**修改内容**: 注释掉事件定义 (第456行)

**修改前**:
```objc
#define product_view_close                     @"product_view_close"
```

**修改后**:
```objc
// #define product_view_close                     @"product_view_close" // 极光埋点已移除
```

## 影响范围分析

### 3.1 直接影响
- **页面**: 商品详情页 (YBMProductDetailController)
- **功能**: 页面关闭时的极光埋点数据收集
- **数据丢失**: 
  - 商品详情页停留时长统计
  - 商品信息 (ID、名称、价格等)
  - 活动信息 (秒杀、拼团、批购包邮等)
  - 来源页面信息
  - 入口信息

### 3.2 兼容性处理
- ✅ 保留了 `auroraAnalysisWithProductDetailClose` 方法结构
- ✅ 方法调用不会出错
- ✅ 为将来添加其他埋点系统预留了空间

### 3.3 其他埋点系统
项目中还存在以下埋点系统，不受此次修改影响：
- **QT埋点**: 使用 QTMobClick 和 YBMQTTrack
- **雪地埋点**: 使用 YBMAnalyticsUtil
- **其他极光埋点**: 如 btn_click、btn_exposure、add_to_cart 等

## 测试建议

### 4.1 功能测试
1. **商品详情页正常访问**
   - 进入商品详情页
   - 验证页面正常显示
   - 验证所有功能正常工作

2. **页面关闭测试**
   - 通过返回按钮关闭页面
   - 通过手势关闭页面
   - 通过跳转其他页面关闭
   - 验证页面关闭过程无异常

3. **其他埋点验证**
   - 验证QT埋点正常工作
   - 验证雪地埋点正常工作
   - 验证其他极光埋点事件正常工作

### 4.2 回归测试
1. **商品详情页相关功能**
   - 商品信息展示
   - 加购功能
   - 分享功能
   - 收藏功能
   - 评论功能

2. **页面跳转**
   - 从搜索页跳转到商品详情页
   - 从首页跳转到商品详情页
   - 从购物车跳转到商品详情页
   - 从商品详情页跳转到其他页面

### 4.3 埋点验证
1. **确认极光埋点已移除**
   - 检查极光后台不再收到 `product_view_close` 事件
   - 验证其他极光埋点事件正常上报

2. **其他埋点系统正常**
   - 检查QT埋点后台数据正常
   - 检查雪地埋点后台数据正常

## 风险评估

### 5.1 低风险
- 方法结构保持不变，不会引起编译错误
- 页面功能不受影响
- 其他埋点系统不受影响

### 5.2 数据影响
- 极光后台将不再收到商品详情页关闭的统计数据
- 如果业务需要此类数据，建议使用其他埋点系统替代

## 回滚方案
如需回滚，可以：
1. 恢复 `YBMAnalyticsEvents.h` 中的事件定义
2. 恢复 `auroraAnalysisWithProductDetailClose` 方法中的极光埋点调用代码

## 总结
本次修改成功移除了 `product_view_close` 极光埋点，同时保持了代码的兼容性和扩展性。建议在测试环境中充分验证后再发布到生产环境。
