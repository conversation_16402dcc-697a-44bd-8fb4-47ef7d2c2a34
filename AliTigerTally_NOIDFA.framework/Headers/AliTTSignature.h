//
//  AliSignature.h
//  AliTigerTallySdk
//
//  Created by 旬修 on 2022/8/10.
//

#import <UIKit/UIKit.h>


typedef NS_ENUM(NSInteger, TTTypeRequest) {
    TTTypeGet=0, T<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ut, <PERSON><PERSON><PERSON>Patch, TTTypeDelete
};

@interface AliTTSignature : NSObject

+ (instancetype)sharedInstance;

- (int)initialize:(NSString *)appkey;

- (int)initialize:(NSString*)appkey options:(NSMutableDictionary *)options callback:(void (^)(int))callback;

- (void)setAccount:(NSString *)account;

- (NSString *)vmpSign:(NSData *)input;

- (NSString *)vmpHash:(TTTypeRequest)type input:(NSData *)input;

- (NSString *)getVersion;

- (int)reCollect;

@end
