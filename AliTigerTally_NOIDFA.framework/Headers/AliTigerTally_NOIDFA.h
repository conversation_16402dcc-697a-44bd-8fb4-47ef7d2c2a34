//
//  AliTigerTally_NOIDFA.h
//  AliTigerTally_NOIDFA
//
//  Created by fcj on 2021/7/2.
//

#import <Foundation/Foundation.h>
#import <AliTigerTally_NOIDFA/AliTigerTally.h>
#import <AliTigerTally_NOIDFA/TTCaptcha.h>


//! Project version number for AliTigerTally_NOIDFA.
FOUNDATION_EXPORT double AliTigerTally_NOIDFAVersionNumber;

//! Project version string for AliTigerTally_NOIDFA.
FOUNDATION_EXPORT const unsigned char AliTigerTally_NOIDFAVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <AliTigerTally_NOIDFA/PublicHeader.h>

