# 极光埋点移除测试文档

## 概述
本文档记录了移除 `action_Search_GroupPurchaseFreeShopping` 极光埋点事件的详细信息，包括修改位置、影响范围和测试建议。

## 修改详情

### 1. 修改文件
**文件路径：** `YaoBangMang/Classes/Me/Coupon/Detail/View/FilterView/YBMCouponFilterVC.m`

### 2. 修改位置
**行号：** 第 244-246 行  
**方法：** `submitData` 方法  
**类名：** `YBMCouponFilterVC`

### 3. 修改内容
**修改前：**
```objective-c
if(obj.isCheck){
    // 埋点
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    [YBMAnalyticsUtil track:@"action_Search_GroupPurchaseFreeShopping" properties:dic];
    
    NSMutableDictionary *mutD = [NSMutableDictionary dictionary];
    [AnalysysAgent track:@"action_Search_GroupPurchaseFreeShopping" properties:mutD];
}
```

**修改后：**
```objective-c
if(obj.isCheck){
    // 埋点 - 保留雪地埋点，移除极光埋点
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    [YBMAnalyticsUtil track:@"action_Search_GroupPurchaseFreeShopping" properties:dic];
    
    // 极光埋点已移除 - action_Search_GroupPurchaseFreeShopping
    // NSMutableDictionary *mutD = [NSMutableDictionary dictionary];
    // [AnalysysAgent track:@"action_Search_GroupPurchaseFreeShopping" properties:mutD];
}
```

## 功能说明

### 页面位置
- **模块：** 优惠券模块
- **页面：** 优惠券筛选页面 (YBMCouponFilterVC)
- **功能：** 筛选条件选择

### 触发条件
1. 用户进入优惠券筛选页面
2. 选择"拼团包邮"筛选条件
3. 点击"确定"按钮提交筛选条件
4. 当 `isGroupBuyingOrWholesale` 为 `YES` 时触发埋点

### 业务逻辑
- 该埋点用于统计用户在搜索优惠券时选择"拼团包邮"筛选条件的行为
- 原本同时发送雪地埋点和极光埋点
- 修改后只保留雪地埋点，移除极光埋点

## 影响范围

### 1. 直接影响
- **埋点数据：** 极光平台将不再接收到 `action_Search_GroupPurchaseFreeShopping` 事件数据
- **雪地埋点：** 继续正常工作，数据不受影响
- **业务功能：** 筛选功能完全不受影响

### 2. 间接影响
- **数据分析：** 基于极光平台该事件的数据分析将停止更新
- **报表统计：** 相关报表中的极光数据将不再增长
- **A/B测试：** 如果有基于该事件的A/B测试需要调整

### 3. 兼容性
- **向前兼容：** 修改不影响现有功能
- **向后兼容：** 雪地埋点保持原有格式和参数

## 测试建议

### 1. 功能测试
**测试步骤：**
1. 启动应用并登录
2. 进入优惠券相关页面
3. 打开筛选页面
4. 选择"拼团包邮"选项
5. 点击"确定"按钮
6. 验证筛选功能是否正常工作

**预期结果：**
- 筛选功能正常工作
- 页面无异常报错
- 筛选结果正确显示

### 2. 埋点测试
**雪地埋点验证：**
1. 开启调试模式
2. 执行上述功能测试步骤
3. 查看控制台日志
4. 确认 `YBMAnalyticsUtil.track` 调用正常
5. 验证事件参数正确

**极光埋点验证：**
1. 确认控制台不再输出 `AnalysysAgent.track` 相关日志
2. 验证极光平台不再接收该事件数据

### 3. 回归测试
**相关功能测试：**
- 优惠券列表页面
- 优惠券详情页面
- 其他筛选条件功能
- 搜索功能

### 4. 性能测试
- 验证移除埋点后页面响应时间
- 确认内存使用无异常

## 风险评估

### 低风险
- 代码修改量小，只是注释掉极光埋点调用
- 保留了雪地埋点，数据收集不完全中断
- 业务逻辑完全不受影响

### 注意事项
- 需要通知数据分析团队该变更
- 相关报表和监控需要调整
- 如果后续需要恢复，只需取消注释即可

## 验证清单

- [ ] 代码编译无错误
- [ ] 功能测试通过
- [ ] 雪地埋点正常工作
- [ ] 极光埋点已停止发送
- [ ] 相关页面无异常
- [ ] 性能无明显影响
- [ ] 通知相关团队

## 联系信息
如有问题，请联系：
- 开发团队：负责代码修改和技术支持
- 数据团队：负责埋点数据分析和报表调整
- 测试团队：负责功能验证和回归测试
