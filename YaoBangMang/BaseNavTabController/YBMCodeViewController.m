//
//  YBMCodeViewController.m
//  YaoBangMang
//
//  Created by xyy on 2025/1/9.
//  Copyright © 2025 XiaoYaoYao.Ltd. All rights reserved.
//

/**
 
 1.业务分块，主要分成如下几块，按顺序编写，如下：
 #pragma mark - 生命周期方法（生命周期方法中，可以按照生命周期的顺序写，建议dealloc方法放在最上面）
 #pragma mark - Net（网络加载、setModel方法等 可以写在这里）
 #pragma mark - Action（该文件的私有方法）
 #pragma mark - UITableView代理
 #pragma mark - 具体业务代理，如cell代理，view代理等
 #pragma mark - Lazy
 #pragma mark - 埋点
 1.1.对相同的内容归类在一起，比如通知，可以抽离方法出来，所有的通知监听写在一个方法里；
 
 2.属性
 2.1.属性的命名：驼峰命名法
 2.2.属性定义：@property +空格+ (nonatomic, +空格+ strong) +空格+ NSObject +空格+ *dataModel;
 2.3.属性定义的说明：a.属性的修饰符，nonatomic放在最前面，后面紧跟逗号，加空格，再写第二个修饰符；b.括号后面加空格，写属性的类型，再加空格，写属性名；
 
 3.".m"文件注释
 3.1.属性注释，在定义的属性上一行添加注释，注释以 “///” 标明，后面跟一个空格，再写属性含义。如：/// 按钮
 3.2.方法注释：以三斜线“//”开头，后面跟空格，最后说明方法的用途。如：// testButton的按钮点击事件
 
 4.方法：
 4.1.方法的定义
 // 方法注释
 -空格(void)方法名:(ParamType空格*)param空格{
 
 }
 4.2.方法说明
 4.2.1.方法中尽量不要有不必要的空行；
 
 
 5.懒加载：
 5.1.懒加载的书写，以button为例。
 5.1.1.在括号中，写btn，在最后给 _testButton 赋值；
 5.1.2.button的点击事件还是以testButton开头，testButtonClick：
 - (UIButton *)testButton {
     if (!_testButton) {
         UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
         btn = [UIButton buttonWithType:UIButtonTypeCustom];
         [btn setImage:[UIImage imageNamed:@"nav_close_gray"] forState:UIControlStateNormal];
         [btn.titleLabel setFont:[UIFont boldSystemFontOfSize:22]];
         [btn setTitleColor:ColorFromHex(0xFC534A) forState:UIControlStateNormal];
         [btn addTarget:self action:@selector(testButtonClick:) forControlEvents:UIControlEventTouchUpInside];
         _testButton = btn;
     }
     return _testButton;
 }
 
 6.路由跳转参数拼接:
 禁止用[NSString stringWithFormat:@"ybmpage://commonh5activity?umkey=1111hd1&url=%@?merchantId=%@&ybm_title=消息中心&head_menu=0",str,[XYYUserData ShareUserData].merchantId]这种方法获取带参数的url
 统一用XYYRouterManager类的-(NSString *)returnUrlstr:(NSString *)linkUlr params:(NSDictionary *)params;参考returnUrlStr方法
 
 7.日志打印：(以下两种日志打印都可以在日志回捞系统中进行回捞)
 信息日志打印用YBMLog （已经区分了debug和release）
 报错日志打印用YBMErrorLog （已经区分了debug和release）
 */

#import "YBMCodeViewController.h"
#import "XYYWebViewVC.h"
#import "YBMHomeV3ViewModel.h"
//#import "YBMPendingOrderPopCell.h"

@interface YBMCodeViewController ()<UITableViewDelegate, UITableViewDataSource>

/// 列表
@property (nonatomic, strong) UITableView *tableView;
/// 测试按钮
@property (nonatomic, strong) UIButton *testButton;
/// 测试label
@property (nonatomic, strong) UILabel *testLabel;
/// 测试字符串
@property (nonatomic, copy) NSString *testString;
/// 测试字典
@property (nonatomic, copy) NSDictionary *testDict;
/// 加载page的页数
@property (nonatomic, assign) NSInteger pageNow;
/// 数据源
@property (nonatomic, strong) NSMutableArray *dataArr;

@property (nonatomic, strong) YBMHomeV3ViewModel *viewV3Model;

@end

@implementation YBMCodeViewController

- (void)dealloc {
    // 页面销毁了
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    // 默认加载第一页
    self.pageNow = 1;
    
    [self configSubviews];
    // 注册通知
    [self addNotification];
    // 加载网络数据
    [self loadNetData:YES];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
}

- (void)configSubviews {
    self.view.backgroundColor = [UIColor clearColor];
    
    [self.view addSubview:self.tableView];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.view.mas_bottom).offset(5);
        make.left.right.mas_equalTo(self.view);
        make.bottom.mas_equalTo(self.view).offset(-100);
    }];
}

#pragma mark - Net

- (void)setDataModel:(NSObject *)dataModel {
    _dataModel = dataModel;
    
    // 业务逻辑
}

- (NSString *)returnUrlStr{
    NSMutableDictionary *paramDic = [NSMutableDictionary dictionary];
    // 从首页跳过去的
    paramDic[@"referrer"] = @"YBMHomeV3Controller";
    paramDic[@"referrerTitle"] = @"首页";
    paramDic[@"referrerModule"] = @"首页-浮窗";;
    NSString *realUrl = [[XYYRouterManager ShareRouterManager] returnUrlstr:@"ybmpage://commonh5activity" params:paramDic];
    return realUrl;
}

- (void)loadNetData:(BOOL)isRefreshing {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"pageNum"] = isRefreshing ? @"1" : @(self.pageNow);
    params[@"pageSize"] = isRefreshing ? @"18" : @"10";
    
    @weakify(self);
    [self.viewV3Model requestFeedListWithParams:params refresh:isRefreshing complete:^(BOOL success, id _Nullable response, NSError * _Nullable error) {
        @strongify(self);
        if (success && response) {
            YBMFeedListData *data = response;
            if (isRefreshing) {
                self.dataArr = [NSMutableArray arrayWithArray:data.list];
                [self.tableView.mj_footer resetNoMoreData];
            } else {
                [self.dataArr addObjectsFromArray:data.list];
                if (data.isEnd || data.list.count == 0) {
                    [self.tableView.mj_footer endRefreshingWithNoMoreData];
                } else {
                    [self.tableView.mj_footer endRefreshing];
                }
            }
            if (data.pageNum > self.pageNow) {
                self.pageNow = data.pageNum;
            }

            [self.tableView reloadData];
        }
    }];
}

#pragma mark - Action

- (void)addNotification {
    // 程序进入前台通知
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(testNotification:) name:@"applicationWillEnterForeground" object:nil];
}

- (void)testNotification:(NSNotificationCenter *)notifi {
    
}

// testButton点击事件
- (void)testButtonClick:(UIButton *)sender {
    
    if (self.delegate && [self.delegate respondsToSelector:@selector(ybm_CodeViewController:clickWithModel:)]) {
        [self.delegate ybm_CodeViewController:self clickWithModel:self.dataModel];
    }
    
    // 点击埋点
    [self auroraAnalysisTabClickEvent];
}

#pragma mark - UITableViewDelegate & UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 0;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 100;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
//    YBMPendingOrderPopCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass([YBMPendingOrderPopCell class])];
//    cell.delegate = self;
//    return cell;
    return nil;
}

#pragma mark - YBMPendingOrderPopCellDelegate

// 顺手买一件
//- (void)ybmPendingOrderPopCell:(YBMPendingOrderPopCell *)cell changeRandomlyItemModel:(YBMRandomlyBuyItemModel *)randomlyModel newCount:(NSInteger)newCount oldCount:(NSInteger)oldCount {
//    
//}

#pragma mark - Lazy

- (UITableView *)tableView {
    if (!_tableView) {
        UITableView *tabV = [[UITableView alloc] init];
        tabV.delegate = self;
        tabV.dataSource = self;
        tabV.estimatedRowHeight = 100;
        tabV.estimatedSectionHeaderHeight = 0;
        tabV.separatorStyle = UITableViewCellSeparatorStyleNone;
        @weakify(self);
        tabV.mj_header = [YBMRefreshHeader headerWithRefreshingBlock:^{
            @strongify(self);
            [self loadNetData:YES];
        }];
        tabV.mj_footer = [YBMRefreshFooter footerWithRefreshingBlock:^{
            @strongify(self);
            [self loadNetData:NO];
        }];
        // 注册cell
//        [tabV registerClass:[YBMPendingOrderPopCell class] forCellReuseIdentifier:NSStringFromClass([YBMPendingOrderPopCell class])];
        _tableView = tabV;
    }
    return _tableView;
}

- (UIButton *)testButton {
    if (!_testButton) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        btn = [UIButton buttonWithType:UIButtonTypeCustom];
        [btn setImage:[UIImage imageNamed:@"nav_close_gray"] forState:UIControlStateNormal];
        [btn.titleLabel setFont:[UIFont boldSystemFontOfSize:22]];
        [btn setTitleColor:ColorFromHex(0xFC534A) forState:UIControlStateNormal];
        [btn addTarget:self action:@selector(testButtonClick:) forControlEvents:UIControlEventTouchUpInside];
        _testButton = btn;
    }
    return _testButton;
}

- (UILabel *)testLabel {
    if (!_testLabel) {
        UILabel *lab = [[UILabel alloc] init];
        lab.textColor = [UIColor colorWithHexString:@"#292933"];
        lab.font = [UIFont fontWithName:FontTypePingFangMe size:16];
        lab.text = @"人气好店";
        _testLabel = lab;
    }
    return _testLabel;
}

- (YBMHomeV3ViewModel *)viewV3Model {
    if (!_viewV3Model) {
        _viewV3Model = [[YBMHomeV3ViewModel alloc] init];
    }
    return _viewV3Model;
}

- (NSMutableArray *)dataArr {
    if (!_dataArr) {
        _dataArr = [NSMutableArray array];
    }
    return _dataArr;
}

#pragma mark - 埋点

- (void)auroraAnalysisTabClickEvent {
    NSMutableDictionary *mutD = [NSMutableDictionary dictionary];
    mutD[@"account_id"] = XYYUserData.ShareUserData.accountId;
    mutD[@"merchant_id"] = [NSString stringWithFormat:@"%@", [XYYUserData ShareUserData].merchantId];
    // 极光埋点已移除 - app_tab_order_click
    // [AnalysysAgent track:kApp_tab_order_click properties:mutD];
}

@end
