//
//  YBMTabBarController.m
//  YaoBangMang
//
//  Created by admin on 2025/1/13.
//  Copyright © 2025 XiaoYaoYao.Ltd. All rights reserved.
//

#import "YBMTabBarController.h"
#import "YBMOrderStyleRequest.h"
#import "YBMShoppingCartAdapter.h"
#import "XYYNavigationController.h"
#import "YBMHomeV3Controller.h"
#import "YBMShopListManagerVC.h"
#import "YBMNewMeVC.h"
#import "XYYAllShopViewController.h"
#import "YBMNotificationKey.h"
#import "XYYShoppingNumberManage.h"
#import "XYYStringTools.h"
#import "YBMMyCouponSearchController.h"
#import "YBMTabbarDataManger.h"
#import "YBMOfftenBuyORSearchVC.h"

@interface YBMTabBarController () <YBMTabbarDataMangerDelegate,UITabBarControllerDelegate>
/// 记录第一个tabbar是否可滑刀顶部
@property (nonatomic, assign) BOOL isFirstScroll;
///首页火箭button
@property (nonatomic, strong) UIButton *rocketButton;
///记录最后一次切换的tabcontroller 埋点用
@property (nonatomic, weak) UIViewController *lastViewController;

/// 首页选中图片
@property (nonatomic, strong) UIImageView *homeHoverImageView;

@end

@implementation YBMTabBarController

//类加载进内容会调用,只调用一次
+ (void)load {
    [[XYYRouterManager ShareRouterManager] mapClass:@{@"XYYTabBarController":@{@"className":@"YBMTabBarController"}}];
    //获取全局UITabBarItem外观
    UITabBarItem *item = [UITabBarItem appearanceWhenContainedInInstancesOfClasses:@[self]];
    
    /**
     *  设置文字颜色,选中状态
        通过设置文本属性
        创建字典描述文本属性
     */
    //设置正常状态下字体
    NSMutableDictionary *textDictNor = [NSMutableDictionary dictionary];
    
    textDictNor[NSFontAttributeName] = [UIFont systemFontOfSize:10];
    
    textDictNor[NSForegroundColorAttributeName] = ColorText_9494A6;
    
    [item setTitleTextAttributes:textDictNor forState:UIControlStateNormal];
    
    //设置选中状态下的颜色
    NSMutableDictionary *textDitc = [NSMutableDictionary dictionary];
    
    textDitc[NSForegroundColorAttributeName] = XYYLightgreenColor;
    
    [item setTitleTextAttributes:textDitc forState:UIControlStateSelected];
    
    // 修改tabbaritemtitle文字高度
    [item setTitlePositionAdjustment:UIOffsetMake(0, 0)];
    
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self getOrderStyle];
    [self setUpNotificationsMethod];
    // 获取购物车商品数量
    [XYYShoppingNumberManage setUpHttp];
    
    [YBMTabbarDataManger shareDataManger].delegate = self;
    
    // 设置首页选中样式图片
    [self setupHomeHoverImageView];
    //初始化基础tabbarcontroller子控制器
    [self loadTheOriginalStyle];
    [self addRocketButtonAction];
}
-(void)setSelectedIndex:(NSUInteger)selectedIndex{
    [super setSelectedIndex:selectedIndex];
    if (self.selectedIndex == 0) {
        [self showOrHiddenRocket:self.isFirstScroll];
    } else {
        [self showOrHiddenRocket:NO];
    }
}
- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
      self.tabBar.translucent = NO;
}
- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
      self.tabBar.translucent = YES;
}
#pragma mark - 通知等属性方法
-(void)bottomNavigationBarIconProcessing:(NSNotification *)notification {
    [[YBMTabbarDataManger shareDataManger] dealWithTabbarNewData:notification.object];
}


- (void)setUpNotificationsMethod {
    // 创建跳转我的界面通知中心
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(pushMeViewController:) name:@"pushMe" object:nil];
    
    // 创建监听商品数量的通知中心
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(setUpShoppingBadgeValue:) name:@"setUpShoppingNumber" object:nil];
    
    // 创建监听首页的通知中心
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(pushHomeViewController:) name:@"pushHome" object:nil];
    
    // 设置代理,监听tabBar上按钮的点击
    self.delegate = self;
    
    _lastViewController = self.childViewControllers.firstObject;
        
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(changeRocket:) name:kRocketButtonAnimationNotity object:nil];
    
    //底部导航栏图标处理(新版本)
   [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(bottomNavigationBarIconProcessing:) name:kRefreshTabBarImageKey object:nil];
    // 创建监听订单待支付数量
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(setUpWaitPayBadgeValue:) name:@"setUpWaitPayNumber" object:nil];
}


-(void)setUpWaitPayBadgeValue:(NSNotification *)noti {
    NSNumber *badge = noti.userInfo[@"waitPayNum"];
    for (UITabBarItem *item in self.tabBar.items) {
        if ([item.title isEqualToString:@"订单"]) {
            if (badge.intValue != 0) {
                [item setBadgeValue:badge.stringValue];
            }
        }
    }
}
- (void)rocketButtonClick {
    [[NSNotificationCenter defaultCenter] postNotificationName:@"kScrollTopNotification" object:nil];
}

- (void)changeRocket:(NSNotification *)noti {
    NSString *change = noti.object[@"change"];
    BOOL isShow =  [change isEqualToString:@"up"];
    [self showOrHiddenRocket:isShow];
    if (isShow) {
        self.isFirstScroll = YES;
    } else {
        self.isFirstScroll = NO;
    }
}


- (void)showOrHiddenRocket:(BOOL)isShow {
    UITabBarItem *item = [self.tabBar.items firstObject];
    UIControl *itemButton = [item valueForKey:@"view"];
    if (isShow) {
        [self hiddenHomeHoverImage];
        [self translatonAnimation:itemButton.center isUp:YES];
        itemButton.hidden = YES;
    } else {
        CGPoint point = CGPointMake(itemButton.center.x, CGRectGetMaxY(self.tabBar.frame));
        [self translatonAnimation:point isUp:NO];
        itemButton.hidden = NO;
        if (self.selectedIndex == 0) {
            [self showHomeHoverImage];
        } else {
            [self hiddenHomeHoverImage];
        }
    }
}

- (void)setupHomeHoverImageView {
    [self.tabBar addSubview:self.homeHoverImageView];
    [self.tabBar bringSubviewToFront:self.homeHoverImageView];
    /// 默认展示
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.25 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self showHomeHoverImage];
    });
}

- (void)showHomeHoverImage {
    UITabBarItem *item = [self.tabBar.items firstObject];
    UIControl *itemButton = [item valueForKey:@"view"];
    //YBMLog(@"首页icon的center: %@",NSStringFromCGPoint(itemButton.center));
    self.homeHoverImageView.center = itemButton.center;
    itemButton.hidden = YES;
    self.homeHoverImageView.hidden = NO;
}

- (void)hiddenHomeHoverImage {
    UITabBarItem *item = [self.tabBar.items firstObject];
    UIControl *itemButton = [item valueForKey:@"view"];
    itemButton.hidden = NO;
    self.homeHoverImageView.hidden = YES;
}


- (void)addRocketButtonAction {
    [self.tabBar addSubview:self.rocketButton];
    UITabBarItem *item = [self.tabBar.items firstObject];
    UIControl *itemButton = [item valueForKey:@"view"];
    self.rocketButton.xyy_y = self.tabBar.height;//CGPointMake(itemW * 0.5 , self.tabBar.height *0.5);
    self.rocketButton.xyy_centerX = itemButton.center.x;
    [self.tabBar bringSubviewToFront:self.rocketButton];
    self.rocketButton.alpha = 0.0;
}


- (void)translatonAnimation:(CGPoint)location isUp:(BOOL)isUp {
    self.rocketButton.center = location;
    if (!isUp) {
        self.rocketButton.alpha = 0.0;
    } else {
        self.rocketButton.alpha = 1.0;
    }
}

#pragma mark - 接收商品数量
- (void)setUpShoppingBadgeValue:(NSNotification *)badgeValueNoti {
    NSString *numberStr = badgeValueNoti.object;
    NSString *dealString = nil;
    if (numberStr.integerValue == 0) {
        dealString = nil;
    }else if (numberStr.integerValue < 100) {
        dealString = numberStr;
    }else{
        dealString = @"99+";
    }

    for (XYYNavigationController *nav in self.viewControllers) {
        UIViewController *vc = nav.viewControllers.firstObject;
        if ([vc isKindOfClass:YBMShoppingCartAdapter.getShoppingCartClass]) {
            nav.tabBarItem.badgeValue = dealString;
        }
    }
}

#pragma mark - UITabBarControllerDelegate
//点击tabBar按钮调用
- (BOOL)tabBarController:(UITabBarController *)tabBarController shouldSelectViewController:(UIViewController *)viewController{
    if (![XYYStringTools isLoad]) {
        // 从首页去其他tab才报点击埋点
        // 获取当前选中的索引（即原来的 Tab）
        NSInteger fromIndex = tabBarController.selectedIndex;
        // 获取即将选择的 Tab 的索引
        NSInteger toIndex = [tabBarController.viewControllers indexOfObject:viewController];
        if (fromIndex != toIndex) {
            NSDictionary *tabDict = @{
                @0: @"link-newhome_text-",//首页
                @1: @"link-oftenBuySearch_text-",//常购常搜
                @2: @"link-shoppingCart_text-",//购物车
                @3: @"link-orderList_text-",//订单
                @4: @"link-me_text-"//我的
            };
            
            NSString *scmD = [NSString stringWithFormat:@"%@%@", tabDict[@(toIndex)], viewController.tabBarItem.title];
            // tab曝光
            NSDictionary *dict = @{
                @"title": scmD,
                @"fromIndex": @(fromIndex),
                @"toIndex": @(toIndex),
            };
            [[NSNotificationCenter defaultCenter] postNotificationName:@"tabBarDidSelect" object:dict];
            
        }
    }
    return YES;
}

- (void)tabBarController:(UITabBarController *)tabBarController didSelectViewController:(UIViewController *)viewController {
    if([XYYStringTools isLoad]){
        // 跳转登录控制界面
        UIViewController *loginVc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:@"ybmpage://YBMLoginController"];
        XYYNavigationController *navVC = [[XYYNavigationController alloc] initWithRootViewController:loginVc];
        XYYWindow.rootViewController = navVC;
        return;
    }
    
    _lastViewController = viewController;
    
    
    if ([viewController.tabBarItem.title isEqualToString:@"店铺列表"]) {
        // 雪地埋点
        NSMutableDictionary * dictionary = [NSMutableDictionary dictionary];
        [YBMAnalyticsUtil track:action_shoplist_click properties:dictionary];
    }else if ([viewController.tabBarItem.title isEqualToString:@"购物车"]){
        /// 雪地埋点 购物车-点击
        [YBMAnalyticsUtil track:action_shopping_cart_click];
    }else if ([viewController.tabBarItem.title isEqualToString:@"我的"]){
        /// 雪地埋点 购物车-点击
        [YBMAnalyticsUtil track:action_me_click];
    } else if ([viewController.tabBarItem.title isEqualToString:@"订单"]) {
        // 极光埋点
        [self auroraAnalysisTabClickEvent];
        [self auroraAnalysisOrderListPageExposure];
    }
    
    
/// 下面的代码为了解决"个人中心--优惠券--立即使用，去采购单，进入采购单，点击我的，返回了券对应的商品列表页"的bug，因为此时的导航栈中有三个控制，导致点击"我的"的时候加载的是"可用商品"的控制器
    XYYNavigationController *navTemp = (XYYNavigationController *)viewController;
    NSMutableArray *arrayNav = [NSMutableArray arrayWithArray:navTemp.childViewControllers];
    if (arrayNav.count == 3 && ([arrayNav.lastObject isKindOfClass:[YBMMyCouponSearchController class]]) && [arrayNav.firstObject isKindOfClass:[YBMNewMeVC class]]) {
        [arrayNav removeLastObject];
        [arrayNav removeObjectAtIndex:1];
        [navTemp setViewControllers:arrayNav];
    } else if (arrayNav.count == 3 && ([arrayNav.lastObject isKindOfClass:[YBMMyCouponSearchController class]]) && [arrayNav.firstObject isKindOfClass:[YBMHomeV3Controller class]]) {
        // 解决优惠券跳转购物车后点击首页页面错误的问题
        [arrayNav removeLastObject];
        [arrayNav removeObjectAtIndex:1];
        [navTemp setViewControllers:arrayNav];
    } else {
        NSString *key = [[NSUserDefaults standardUserDefaults] objectForKey:@"widgetHome"];
        if (!kStringIsEmpty(key)) {
            if (arrayNav.count) {
                UIViewController *vc = arrayNav.firstObject;
                [arrayNav removeAllObjects];
                [arrayNav addObject:vc];
                [navTemp setViewControllers:arrayNav];
                [[NSUserDefaults standardUserDefaults] removeObjectForKey:@"widgetHome"];
            }
        }
    }
    
    if (self.selectedIndex == 0) {
        [self showOrHiddenRocket:self.isFirstScroll];
    } else {
        [self showOrHiddenRocket:NO];
    }
}

#pragma mark YBMTabbarDataMangerDelegate
//处理完后台返回的数据之后进行数据回显
- (void)ybm_TabbarDataMangerDidFinishLoadWithArray:(NSArray <YBMBottomTabModel *>*)array {
    // 处理iOS13 tabbarItem在任意子页面push之后选中颜色变统一蓝色问题
    if (@available(iOS 13.0, *)) {
        self.tabBar.unselectedItemTintColor = ColorGreen_00B377;
    }
    
    if (self.childViewControllers.count >= array.count) {
        for (YBMBottomTabModel *model in array) {
            NSInteger index = [array indexOfObject:model];
            XYYNavigationController * navi = self.childViewControllers[index];
            navi.tabBarItem.title = model.text;
            if (model.realImage) {
                navi.tabBarItem.image = [model.realImage imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
            }
            if (model.realSelectImage) {
                navi.tabBarItem.selectedImage = [model.realSelectImage imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
            }
    
            if (index == 0) {
                // 8.0版本设置首页选中样式为大图
                if (model.realSelectImage) {
                    self.homeHoverImageView.image = [model.realSelectImage imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
                }
            }
            [navi.tabBarItem setTitleTextAttributes:@{NSForegroundColorAttributeName :ColorGreen_00B377} forState:UIControlStateSelected];
            [navi.tabBarItem setTitleTextAttributes:@{NSForegroundColorAttributeName :ColorText_9494A6} forState:UIControlStateNormal];
        }
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.selectedIndex == 0) {
            UITabBarItem *item = [self.tabBar.items firstObject];
            UIControl *itemButton = [item valueForKey:@"view"];
            itemButton.hidden = YES;
        }
    });

    [XYYShoppingNumberManage setUpCommodityTypeNumberData];
}

#pragma mark - 收到通知更改tabbarcontroller的selectedIndex
- (void)pushHomeViewController:(NSNotification *)homeNoti {
    self.selectedIndex = YBMTabbarTypeHome;
}

- (void)pushMeViewController:(NSNotification *)meNoti {
    self.selectedIndex = YBMTabbarTypeMe;//商品详情页右上角-更多->我的tab
    [self showOrHiddenRocket:NO];
}


///选择使用哪种购物车页面
- (void)getOrderStyle {
    YBMOrderStyleRequest *rq = [[YBMOrderStyleRequest alloc]init];
    rq.addParams(@{@"scene" : @"getCart"}).request(^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
        YBMOrderStyleRootModel *rootModel = response;
        [[NSUserDefaults standardUserDefaults]setInteger:rootModel.data.styleTemplate.integerValue forKey:@"styleTemplate"];
        [[NSUserDefaults standardUserDefaults] synchronize];
        
        XYYNavigationController *cartNav;
        cartNav = self.childViewControllers[YBMTabbarTypeCart];//购物车
        [cartNav setViewControllers:@[YBMShoppingCartAdapter.getShoppingCartInstantiationVC] animated:NO];
        
        //确定用哪个版本购物车页面后，注册路由
        NSString *classStr = NSStringFromClass(YBMShoppingCartAdapter.getShoppingCartClass);
        [[XYYRouterManager ShareRouterManager] mapClass:@{@"cartactivity":@{@"className": classStr}}];
        
    });
}


- (void)loadTheOriginalStyle {
    //改写底部导航细线
    [self.tabBar setBackgroundImage:[UIImage new]];
    [self.tabBar setShadowImage:[UIImage imageNamed:@"home_tab_dividingLine"]];
    //    self.tabBar.layer.shadowColor = [UIColor colorWithHexString:@"#E5E5E5"].CGColor;
    //    self.tabBar.layer.shadowOffset = CGSizeMake(0, -0.1);
    //    self.tabBar.layer.shadowOpacity = 1;//阴影透明度，默认0
    XYYNavigationController * homePageNav = [[XYYNavigationController alloc] initWithRootViewController:[[YBMHomeV3Controller alloc]init]];
    homePageNav.tabBarItem.title = @"首页";
    homePageNav.tabBarItem.image = [UIImage imageWithOriginalRenderingMode:@"home_tab_shouye_icon_nor"];
    homePageNav.tabBarItem.selectedImage = [UIImage imageWithOriginalRenderingMode:@"home_tab_shouye_icon_s"];
    [self addChildViewController:homePageNav];
     
//    XYYNavigationController * shopListNav = [[XYYNavigationController alloc] initWithRootViewController:[[YBMShopListManagerVC alloc] init]];
//    shopListNav.tabBarItem.title = @"店铺列表";
//    shopListNav.tabBarItem.image = [UIImage imageWithOriginalRenderingMode:@"home_tab_shop_icon_nor"];
//    shopListNav.tabBarItem.selectedImage = [UIImage imageWithOriginalRenderingMode:@"home_tab_shop_icon_s"];
//    [self addChildViewController:shopListNav];
    
    
    YBMOfftenBuyORSearchVC *guessVc = [[YBMOfftenBuyORSearchVC alloc]init];
    guessVc.title = @"常购常搜";
    XYYNavigationController *guessLNav = [[XYYNavigationController alloc] initWithRootViewController:guessVc];
    guessLNav.tabBarItem.title = @"常购常搜";
    guessLNav.tabBarItem.image = [UIImage imageWithOriginalRenderingMode:@"home_tab_changgouchangsou_icon_nor"];
    guessLNav.tabBarItem.selectedImage = [UIImage imageWithOriginalRenderingMode:@"home_tab_changgouchangsou_icon_s"];
    [self addChildViewController:guessLNav];
    
    
    UIViewController *vc = YBMShoppingCartAdapter.getShoppingCartInstantiationVC;
    XYYNavigationController * planNav = [[XYYNavigationController alloc] initWithRootViewController:vc];
    planNav.tabBarItem.title = @"购物车";
    planNav.tabBarItem.image = [UIImage imageWithOriginalRenderingMode:@"home_tab_gouwuche_icon_nor"];
    planNav.tabBarItem.selectedImage = [UIImage imageWithOriginalRenderingMode:@"home_tab_gouwuche_icon_s"];
    [self addChildViewController:planNav];
    

    
    
    XYYNavigationController * orderNav = [[XYYNavigationController alloc] initWithRootViewController:[[XYYAllShopViewController alloc] init]];
    orderNav.tabBarItem.title = @"订单";
    orderNav.tabBarItem.image = [UIImage imageWithOriginalRenderingMode:@"home_tab_wodedingdan_icon_nor"];
    orderNav.tabBarItem.selectedImage = [UIImage imageWithOriginalRenderingMode:@"home_tab_wodedingdan_icon_s"];
    [self addChildViewController:orderNav];
    
    XYYNavigationController * meNav = [[XYYNavigationController alloc] initWithRootViewController:[[YBMNewMeVC alloc] init]];
    meNav.tabBarItem.title = @"我的";
    meNav.tabBarItem.image = [UIImage imageWithOriginalRenderingMode:@"home_tab_wode_icon_nor"];
    meNav.tabBarItem.selectedImage = [UIImage imageWithOriginalRenderingMode:@"home_tab_wode_icon_s"];
    [self addChildViewController:meNav];
}


#pragma mark - 懒加载
- (UIImageView *)homeHoverImageView {
    if (!_homeHoverImageView) {
        UIImageView *imgV = [[UIImageView alloc] init];
        UIImage * image = [UIImage imageWithOriginalRenderingMode:@"homev2_horer_icon"];
        imgV.image = image;
        imgV.size = CGSizeMake(40, 40);
        imgV.hidden = YES;
        imgV.contentMode = UIViewContentModeScaleAspectFill;
        _homeHoverImageView = imgV;
    }
    return _homeHoverImageView;
}

- (UIButton *)rocketButton {
    if (!_rocketButton) {
        _rocketButton = [[UIButton alloc] init];
        UIImage *image = [UIImage imageNamed:@"tabbar_rocket"];
        [_rocketButton setImage:image forState:UIControlStateNormal];
        _rocketButton.size = CGSizeMake(image.size.width *1.1, image.size.height *1.1);
        [_rocketButton addTarget:self action:@selector(rocketButtonClick) forControlEvents:UIControlEventTouchUpInside];
    }
    return _rocketButton;
}

#pragma mark - 移除通知
- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - 埋点
/// “订单tab”点击事件的埋点
- (void)auroraAnalysisTabClickEvent {
    NSMutableDictionary *mutD = [NSMutableDictionary dictionary];
    mutD[@"account_id"] = XYYUserData.ShareUserData.accountId;
    mutD[@"merchant_id"] = [NSString stringWithFormat:@"%@",[XYYUserData ShareUserData].merchantId];
    // 极光埋点已移除 - app_tab_order_click
    // [AnalysysAgent track:kApp_tab_order_click properties:mutD];
}

/// “我的订单页面”曝光
- (void)auroraAnalysisOrderListPageExposure {
    NSMutableDictionary *mutD = [NSMutableDictionary dictionary];
    mutD[@"account_id"] = XYYUserData.ShareUserData.accountId;
    mutD[@"merchant_id"] = [NSString stringWithFormat:@"%@", [XYYUserData ShareUserData].merchantId];
    mutD[@"jgspid"] = jgspid_orderlist_tab_exposure;
    // 极光埋点已移除 - app_orderListPage_view
    // [AnalysysAgent track:kApp_orderListPage_view properties:mutD];
}

@end
