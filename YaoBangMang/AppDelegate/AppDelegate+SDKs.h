//
//  AppDelegate+SDKs.h
//  YaoBangMang
//
//  Created by 骆阳光 on 2020/1/20.
//  Copyright © 2020 XiaoYaoYao.Ltd. All rights reserved.
//

#import "AppDelegate.h"

NS_ASSUME_NONNULL_BEGIN

@interface AppDelegate (SDKs)<JPUSHRegisterDelegate>

/// 启动百度定位SDK
- (void)startLocationKit;

/// 初始化Countly
- (void)countlyInit;

/// 启动埋点SDK
- (void)startAnalytics:(NSDictionary *)launchOptions;

/// 集成Bugly
- (void)initBugly;

/// 集成极光推送
- (void)initJPUSHService:(NSDictionary *)launchOptions;
/// 集成极光埋点
- (void)initJiGuangTrack:(NSDictionary *)launchOptions;

- (void)handleNotificationActionWithInfo:(NSDictionary *)info;

/// 支付宝分享
- (void)regiseterAliShare;
/// 配置平安贷SDK
- (void)configPinganSDK;

/// 防火墙
- (void)initAlitigerTally;


//QT埋点初始化
- (void)initQTTrack;

@end

NS_ASSUME_NONNULL_END
