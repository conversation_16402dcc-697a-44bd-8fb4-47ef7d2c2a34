//
//  AppDelegate+SDKs.m
//  YaoBangMang
//
//  Created by 骆阳光 on 2020/1/20.
//  Copyright © 2020 XiaoYaoYao.Ltd. All rights reserved.
//

#import "AppDelegate+SDKs.h"
#import "YBMTabBarController.h"
#import "YBMLocationManager.h"
#import "APOpenAPI.h"
#import "XYYCountly.h"
#import <KYB_SDK/KYB_SDK.h>
#import "AppConfigue.h"
#import <AliTigerTally_NOIDFA/AliTigerTally.h> 
#import <BuglyPro/Bugly.h>
#import <BuglyPro/BuglyConfig.h>
#import <BuglyPro/BuglyDefine.h>

#ifdef DEBUG

#define BuglyappID @"3b287e5444"
#define BaiduKey @"7XSbAHp5Wjhxmof9BOcqOjZ6ijXH3PHg"
#define <PERSON><PERSON><PERSON><PERSON><PERSON> @"2021002102614273"

#else

#define BuglyappID @"68f53efd3e"
#define Baidu<PERSON>ey @"KDtxX94qmvTB3LTq9xtiGz0CQ5wmEC3t"
#define AliappKey @"2021002102614273"
#endif

#define YBMAnalyticsKey @"8b5e1b0f250a436e8c6af9871354bfba"

#define JPUSHKey @"808b05619bc76f8b083c1f8a"

#define CountlyTestAppkey @"4d9f1a953fd03aa75b39faae81651427c86a0be1"
#define CountlyAppKey @"ae93b99720f77a028dbb14beec3243bc7b14c39d"

//阿里防火墙
#define AliTigerTallyKey @"ByCrFhHb0ZEEM39ORIstHOTffw4AniVCvxHaQeDU34g2dOXX3XhiAdY5E-5GeB4sMY5PD9NU3h3dNezoY6828zow5Ykw45DG0JPfZNLUBoUsGkoKO7DgKk8Jux-13xl2nGhGhQE5OJ4GezPH0wTeY92w9UE20zP42CCFUakZfA8="


/******************QT埋点相关**************/
//QT埋点域名
static NSString *const kQTDomain = @"https://qt.ybm100.com";
//QT埋点测试环境AppKey
static NSString *const kQTDevAppKey = @"9nwai6g2w93ifqwszy8mnkya";
//QT埋点生产环境AppKey
static NSString *const kQTProdAppKey = @"u6sg824toq8vty569ja4np2b";

static NSString *const kQTChannel = @"App Store";
/*****************end**************/


@implementation AppDelegate (SDKs)

- (void)startLocationKit {
    [[YBMLocationManager shared] startWithKey:BaiduKey];
}

#pragma mark - 启动埋点SDK

- (void)startAnalytics:(NSDictionary *)launchOptions {
    [YBMAnalyticsUtil startAnalyticsWithAppKey:YBMAnalyticsKey launchOptions:launchOptions];
}

- (void)initBugly {
#ifdef DEBUG
#else
    // appid & appKey, 平台生成
    NSString *appID = @"13616c04de";
    NSString *appKey = @"94f38b4f-48a8-49aa-bbad-2bbc51e74b13";
    
    // 启用模块，根据需要自定义设置
    NSArray<NSString *> *moduleNames = RM_MODULE_ALL;
    BuglyConfig* config = [[BuglyConfig alloc] initWithAppId:appID appKey:appKey];
    // 可选配置。设置自定义日志上报的级别，默认不上报自定义日志
    //    config.reportLogLevel = BuglyLogLevelInfo;
    config.buildConfig  = BuglyBuildConfigDebug;
    // 可选配置。设置用户id
    config.userIdentifier = XYYUserData.ShareUserData.merchantId;
    
    [Bugly start:moduleNames config:config];
#endif
//    NSLog(@"bugly版本好%@",[Bugly sdkVersion]);
    
    
    // 使用 SDK 提供的默认模块
//    TDOSLoggerProxy *loggerProxy = [TDOSLoggerProxy defaultProxy];
//    TDMMKVFactory *kvFactory = [TDMMKVFactoryImpl sharedInstance];
//    TDLogFilePackerImp *filePacker = [[TDLogFilePackerImp alloc] init];
//
//    // 创建一个 TDIAGDepends 实例子
//    TDIAGDepends *depends = [TDIAGDepends dependsWithLogImp:loggerProxy
//                                               kvFactoryImp:kvFactory
//                                           andFilePackerImp:filePacker];
//    
//    // 生成捞日志模块配置实例，并初始化
//    TDLogSDKConfig *config = [TDLogSDKConfig configWithAppId:appID
//                                                      appKey:appKey
//                                                  dataSource:self
//                                                      depends:depends];
//    
//    // !!!: 设置 server host type 为 TDLogServerHostTypeBuglyPro
//    config.serverHostType = TDLogServerHostTypeBuglyPro;
//    
//    
//    // 开启频率控制（默认不限制），传空表示使用SDK默认频控策略：2次/5min，也可传入自定义策略；被限制后自动上报接口会回调失败。
//    //    举例：10min内最多只允许3次上报（采用令牌桶算法）
//    //    TDLogFrequencyControlStrategy *strategy = [TDLogFrequencyControlStrategy new];
//    //    strategy.times = 3;
//    //    strategy.timeInterval = 10 * 60;
//    // 注意，该接口要求TDLogSDKDataSource必须实现whitelistForAutoUploadTags协议，提供自动上报tag白名单，避免影响必要上报。
//    [config setFrequencyLimitStatusForAutoUpload:YES
//                       withCustomControlStrategy:nil]; // 推荐设置，确保不频繁自动上报
//    // 开启流量控制（默认不限制），传0表示禁止上报，传入负值代表不限制，被限制后自动上报接口会回调失败。
//    // 注意，该接口要求TDLogSDKDataSource必须实现whitelistForAutoUploadTags协议，提供自动上报tag白名单，避免影响必要上报。
//    [config setTrafficQuota24hLimitForAutoUpload:(200 * 1024 * 1024)
//                                          xgQuota:(50 * 1024 * 1024)]; // 可选，设置自动上报流量限额
//
//
//    [[TDLogSDK sharedInstance] startWithConfig:config];
}

- (void)initJPUSHService:(NSDictionary *)launchOptions {
    BOOL isPushProduction = YES;
#ifdef DEBUG
    isPushProduction = NO;
#endif
    JPUSHRegisterEntity * entity = [[JPUSHRegisterEntity alloc] init];
    entity.types = JPAuthorizationOptionAlert|JPAuthorizationOptionBadge|JPAuthorizationOptionSound;
    [JPUSHService registerForRemoteNotificationConfig:entity delegate:self];
    [JPUSHService setupWithOption:launchOptions appKey:JPUSHKey channel:@"appStore" apsForProduction:isPushProduction advertisingIdentifier:nil];
}

- (void)initJiGuangTrack:(NSDictionary *)launchOptions{
    //  启动方式监测
    [AnalysysAgent monitorAppDelegate:self launchOptions:launchOptions];
    //  AnalysysAgent 配置信息
    //  设置key，77a52s552c892bn442v721为样例数据，请根据实际情况替换相应内容
    // 设置渠道默认App Store
    //AnalysysConfig.channel = @"App Store";
    //  使用配置初始化SDK
    //开启DeBug模式，并在控制台输出日志
#ifdef DEBUG
    AnalysysConfig.appKey = @"e6f27194adb12753";
    [AnalysysAgent setDebugMode:AnalysysDebugButTrack];
#else
    AnalysysConfig.appKey = @"a775ff9e4e84bc9a";
//    //关闭Debug模式，并关闭控制台输入日志
    [AnalysysAgent setDebugMode:AnalysysDebugOff];
#endif
    AnalysysConfig.autoPageViewDuration = YES;;
    [AnalysysAgent startWithConfig:AnalysysConfig];
    //  设置上传地址，http://example.com为您上报地址
    [AnalysysAgent setUploadURL:@"http://jgmd.ybm100.com"];
        NSDate *date = [NSDate date];
        NSDateFormatter *dateFor = [[NSDateFormatter alloc] init];
        dateFor.dateFormat = @"yyyy-MM-dd 00:00:00";
        NSString *dateStr = [dateFor stringFromDate:date];
    NSMutableDictionary *mutProperties = [NSMutableDictionary dictionary];
    mutProperties[@"$channel"] = @"App Store";
    mutProperties[@"$platform"] = @"iOS";
    mutProperties[@"account_id"] = XYYUserData.ShareUserData.accountId;
    mutProperties[@"merchant_id"] = [NSString stringWithFormat:@"%@",[XYYUserData ShareUserData].merchantId];
    mutProperties[@"merchant_name"] = [XYYUserData ShareUserData].shopName;
    mutProperties[@"businessType"] = [XYYUserData ShareUserData].businessType;
    mutProperties[@"businessTypeName"] = [XYYUserData ShareUserData].businessTypeName;
    mutProperties[@"province"] = [XYYUserData ShareUserData].provinceName;
    mutProperties[@"up_date"] = dateStr;
    mutProperties[@"project_name"] = @"药帮忙";
    [AnalysysAgent registerSuperProperties:[mutProperties copy]];
    [AnalysysAgent unRegisterSuperProperty:kSearch_sort_strategy_id];
//    [[NSUserDefaults standardUserDefaults] removeObjectForKey:kSearch_sort_strategy_id];
    if (XYYUserData.ShareUserData.merchantId) {
        [AnalysysAgent alias:XYYUserData.ShareUserData.merchantId];
    }
 
}

#pragma mark - 极光推送

// iOS 10 Support处于前台时接受到消息
- (void)jpushNotificationCenter:(UNUserNotificationCenter *)center willPresentNotification:(UNNotification *)notification withCompletionHandler:(void (^)(NSInteger))completionHandler {
    // Required
    NSDictionary * userInfo = notification.request.content.userInfo;
    if([notification.request.trigger isKindOfClass:[UNPushNotificationTrigger class]]) {
        [JPUSHService handleRemoteNotification:userInfo];
    }
    completionHandler(UNNotificationPresentationOptionAlert); // 需要执行这个方法，选择是否提醒用户，有Badge、Sound、Alert三种类型可以选择设置
}

// iOS 10 Support点击处理事件
- (void)jpushNotificationCenter:(UNUserNotificationCenter *)center didReceiveNotificationResponse:(UNNotificationResponse *)response withCompletionHandler:(void (^)(void))completionHandler {
    [[UIApplication sharedApplication] setApplicationIconBadgeNumber:0];
    [JPUSHService resetBadge];
    // Required
    NSDictionary * userInfo = response.notification.request.content.userInfo;
    if([response.notification.request.trigger isKindOfClass:[UNPushNotificationTrigger class]]) {
        [JPUSHService handleRemoteNotification:userInfo];
    }
    // 最开始的判断方式
    /**
     {
     pageUrl = "ybmpage://commonh5activity?umkey=rktop&cache=0&url=https://new-app.test.ybm100.com/static/xyyvue/dist/#/newpro?ybm_title=新品上架",
     _j_business = 1,
     _j_uid = ***********,
     _j_msgid = 47287847293938816,
     aps =     {
     alert =     {
     title = "重楼",
     body = "重楼重楼重楼重楼重楼重楼",
     },
     badge = 1,
     sound = "sound.caf",
     },
     }
     */
    [self handleNotificationActionWithInfo:userInfo];
    completionHandler();  // 系统要求执行这个方法
}

- (void)handleNotificationActionWithInfo:(NSDictionary *)info{
    // 雪地埋点 action_Push_Click 参数 消息ID：pushMsgId+注册ID：registrationId
    NSString *messageId = info[@"pushMsgId"] ? : @"";
    NSString *deviceToken =   [[NSUserDefaults standardUserDefaults] objectForKey:@"deviceTokenString"];
    NSMutableDictionary *snowDic = [NSMutableDictionary dictionary];
    snowDic[@"pushMsgId"] = messageId;
    snowDic[@"registrationId"] = deviceToken;
    [YBMAnalyticsUtil track:action_Push_Click properties:snowDic];
    
    if (info[@"pageUrl"] != nil) {
        UIViewController* rootViewController = [UIApplication sharedApplication].delegate.window.rootViewController;
        if ([rootViewController isKindOfClass:[YBMTabBarController class]]) {
            UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:info[@"pageUrl"]];
            if (vc != nil) {
                UITabBarController *tab = (UITabBarController *)rootViewController;
                UINavigationController *nav = tab.selectedViewController;
                [nav pushViewController:vc animated:YES];
            }
        }
    }
}

///MARK: - 注册支付宝分享
- (void)regiseterAliShare
{
    if (![APOpenAPI registerApp:AliappKey]) {
        YBMLog(@"支付宝注册失败");
    }
}


#pragma mark - countly初始化
- (void)countlyInit {
    NSString * userId = @"unlogin";
    NSString * account = @"unlogin";
    XYYUserData * userData = [XYYUserData ShareUserData];
    if (userData.isLogin) {
        account = userData.phoneNum;
        userId = [NSString stringWithFormat:@"%@", userData.merchantId];
    }
    
    XYYCountly.instance
#if DEBUG
    .setAppKey(CountlyTestAppkey)
    .setIsProduct(NO)
    .setThreshold(1)
    .setOpenLogPrint(NO)
#else
    .setAppKey(CountlyAppKey)
    .setIsProduct(YES)
    .setThreshold(10)
    .setOpenLogPrint(NO)
#endif
    .setDeviceId(AppConfigue.shared.deviceID)
    .setUserData(account, userId)
    .start();
}

#pragma mark - 平安贷
- (void)configPinganSDK{
    //设置SDK环境，具体参照平安老师指引设置
#if DEBUG
    //stage 或 线上环境
    if ([AppConfigue.shared.chooseKey isEqualToString:YBMStageEnv] ||
        [AppConfigue.shared.chooseKey isEqualToString:YBMOnlineEnv]) {
        AppConfigue.shared.pinganApplyUrl = @"https://b.pingan.com.cn/kyb/apply/index.html#/";//申请贷款
        AppConfigue.shared.pinganPayUrl = @"https://cloudpayweb.orangebank.com.cn/h5/";//支付
        [KYBBaseRootConfigModel sharedBaseRootConfigModel].initEnvironment = KYBSDKServerEnvironmentRelease;
    }else{//测试环境
        AppConfigue.shared.pinganApplyUrl = @"https://test-b-fat.pingan.com.cn/kyb7/apply/index.html#/";//申请贷款
        AppConfigue.shared.pinganPayUrl = @"https://cloudpayweb-fat7.orangebank.com.cn/h5/";//支付
        [KYBBaseRootConfigModel sharedBaseRootConfigModel].initEnvironment = KYBSDKServerEnvironmentTestFAT;
    }
#else
    AppConfigue.shared.pinganApplyUrl = @"https://b.pingan.com.cn/kyb/apply/index.html#/";//申请贷款
    AppConfigue.shared.pinganPayUrl = @"https://cloudpayweb.orangebank.com.cn/h5/";//支付
    [KYBBaseRootConfigModel sharedBaseRootConfigModel].initEnvironment = KYBSDKServerEnvironmentRelease;
#endif
        
//    [KYBBaseRootConfigModel sharedBaseRootConfigModel].debugBundleID = @"com.jybd.supplier"; //该包名已注册，可直接使用，临时用

    //开启认证 (如果需要使用融资业务，必须开启)
    [KYBBaseRootConfigModel sharedBaseRootConfigModel].authSwitch = YES;
    
    //根据需要，需要微信功能就设置为自己应用的微信ID
    [KYBBaseRootConfigModel sharedBaseRootConfigModel].wxAppKey = @"wx5766ec723a326dff";
    [KYBBaseRootConfigModel sharedBaseRootConfigModel].wxUniversalLink = @"https://app-v4.ybm100.com/y/wxsdk/";
    
    //初始化SDK配置
    [[KYBBaseRootConfig sharedBaseRootConfig] initialConfig:[KYBBaseRootConfigModel sharedBaseRootConfigModel]];
}

#pragma mark - 防火墙
- (void)initAlitigerTally{
    //游客身份可以暂时先不setAccount，直接初始化；登录以后调用setAccount和重新初始化
    [[AliTigerTally sharedInstance] setAccount:@""];
    
    // appkey代表阿里云客户平台分配的认证密钥
    // 一次初始化采集，代表一次设备信息采集，可以根据业务的不同，重新调用函数initialize初始化采集
    if (0 == [[AliTigerTally sharedInstance] initialize:AliTigerTallyKey]){
        NSLog(@"初始化成功");
     } else {
         NSLog(@"初始化失败");
    }
//    [NSThread sleepForTimeInterval:2.0];
}


//QT埋点初始化
- (void)initQTTrack{
     [YBMQTTrack sharedInstance];//初始化
    
     //开发者需要显式的调用此函数，日志系统才能工作
     [UMCommonLogManager setUpUMCommonLogManager];
    
//     BOOL autoTrackEnabled = [[NSUserDefaults standardUserDefaults] boolForKey:kAutoQTTrackEnabled];
//     [QTMobClick setAutoPageEnabled:autoTrackEnabled];  // 开启页面自动采集
//     [QTMobClick setAutoEventEnabled:autoTrackEnabled]; // 开启事件自动采集
     // 开启全埋点
     [QTMobClick setAutoPageEnabled:NO];  // 开启页面自动采集
     [QTMobClick setAutoEventEnabled:NO]; // 开启事件自动采集
    
     [QTConfigure setCustomDomain:kQTDomain standbyDomain:nil];//设置域名
    /*****************initWithAppkey要放在开启全埋点代码下面，不然采集不到 坑===********************/
#ifdef DEBUG //测试
     [QTConfigure initWithAppkey:kQTDevAppKey channel:kQTChannel];
     [QTConfigure setLogEnabled:YES];
#else //线上
     [QTConfigure initWithAppkey:kQTProdAppKey channel:kQTChannel];
     [QTConfigure setLogEnabled:NO];
    #endif
 
}


@end
