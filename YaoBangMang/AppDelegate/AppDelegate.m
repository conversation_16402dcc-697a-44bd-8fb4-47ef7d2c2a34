//
//  AppDelegate.m
//  YaoBangMang
//
//  Created by <PERSON>'s <PERSON> on 16/9/2.
//  Copyright © 2016年 XiaoYaoYao.Ltd. All rights reserved.
//
#import "AppDelegate.h"
#import <AlipaySDK/AlipaySDK.h>
#import "MBProgressHUD.h"
#import "UPPaymentControl.h"
#import <WXApi.h>
#import "WWKApi.h"//企业微信分享
#import "InterfaceManager.h"
#import "XYYShoppingNumberManage.h"
#import <StoreKit/StoreKit.h>
#import "UIAlertView+block.h"
#import "UIImage+GradientColor.h"
#import "IQKeyboardManager.h"
#import "YBMInfoHolder.h"
#import "YBMServerTimeManager.h"
#import "AppDelegate+SDKs.h"
#import "AppDelegate+Router.h"
#import "AppDelegate+XHLaunchAd.h"
#import "AppDelegate+UI.h"
//#import <FlutterPluginRegistrant/GeneratedPluginRegistrant.h>
#import <AppTrackingTransparency/AppTrackingTransparency.h>
#import "SafeObject.h"
#import "YBMHttpClient.h"

@interface AppDelegate () <SKStoreProductViewControllerDelegate, WWKApiDelegate>

@property (nonatomic ,strong) NSDate *backDate;

@end

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    //开启防崩溃
    [NSArray openSafeProtector];
    [NSMutableArray openSafeProtector];
    [NSDictionary openSafeProtector];
    [NSMutableDictionary openSafeProtector];
  
    // 集成Bugly
    [self initBugly];
    
    // 启动埋点SDK
    [self startAnalytics:launchOptions];
    
    // 清除SaaS渠道来源信息
    [self removeSaasChanel];

    // Countly初始化
    [self countlyInit];
    
    // 防火墙
    [self initAlitigerTally];
    // 设置网络请求组件代理
    [[YBMInfoHolder shareInstance] setupNetworkEngine];
    
    // 请求服务器时间并保存与本地时间差
    [[YBMServerTimeManager shareInstance] requestServerTime];
    
    // 请求服务器时间并保存与本地时间差
    [[InterfaceManager ShareInterfaceManager] getServerTimeFive];
    
    // 设置window
    [self setupWindow:application];
    
    // 设置隐私弹框
    [self setupPrivateWindow];
    
    //微信分享
    [WXApi registerApp:@"wx5766ec723a326dff" universalLink:@"https://app-v4.ybm100.com/y/wxsdk/"];
    
    //企业微信分享
#ifdef DEBUG
    [WWKApi registerApp:@"wwauth97630ccc40bc5795000054" corpId:@"ww97630ccc40bc5795" agentId:@"1000054"];
#else
    [WWKApi registerApp:@"wwauth97630ccc40bc5795000055" corpId:@"ww97630ccc40bc5795" agentId:@"1000055"];
#endif
    
    // APP内引导页方法
    [XYYShoppingNumberManage backHasLaunched];
    
    // 设置键盘
    [IQKeyboardManager sharedManager].shouldResignOnTouchOutside = YES;
    
    // 集成极光推送
    [self initJPUSHService: launchOptions];
    // 集成极光埋点
    [self initJiGuangTrack:launchOptions];
    
    //如果能获取到用户信息，则上报首次启动
    if([[XYYUserData ShareUserData] isLogin]){
        NSMutableDictionary *snowDic = [NSMutableDictionary dictionary];
        snowDic[@"merchantId"] = [[XYYUserData ShareUserData] merchantId];
        snowDic[@"appVersion"] = [[[NSBundle mainBundle] infoDictionary] valueForKey:@"CFBundleShortVersionString"];
        [YBMAnalyticsUtil track:action_app_startup properties:snowDic];
    }
    
    // 启动百度定位SDK
    [self startLocationKit];
    
    // 支付宝分享
    [self regiseterAliShare];
    
    // 检查版本更新
    [self checkAppVersionUpdate];
    
    // 获取网络权限状态
    [self networkStatus];

    // 配置平安贷SDK
    [self configPinganSDK];

    //极光埋点
    [self initJiGuangTrack:launchOptions];
    
    //QT埋点
    [self initQTTrack];
    UNUserNotificationCenter *currentRequest =  [UNUserNotificationCenter currentNotificationCenter];
    [currentRequest requestAuthorizationWithOptions:UNAuthorizationOptionAlert completionHandler:^(BOOL granted, NSError * _Nullable error) {
        
    }];
    
    if (@available(iOS 10.0, *)) {
        // 当前系统是 iOS 10.0 及以上，执行这里的代码
        [AppDelegate hookOldOpenUrl:AppDelegate.class];
    }
    return YES;
}

#pragma mark - iOS18 openURL弃用导致微信、支付宝分享无响应的问题
- (BOOL)g_openURL:(NSURL*)url{
    [UIApplication.sharedApplication openURL:url options:nil completionHandler:nil];
    return YES;
}

+ (void)hookOldOpenUrl:(Class)targetCls{
    Class cls = [UIApplication class];
    if (cls) {
        Method originalMethod =class_getInstanceMethod(cls, @selector(openURL:));
        Method swizzledMethod =class_getInstanceMethod(targetCls, @selector(g_openURL:));
        if (!originalMethod || !swizzledMethod) {
            return;
        }
        IMP originalIMP = method_getImplementation(originalMethod);
        IMP swizzledIMP = method_getImplementation(swizzledMethod);
        const char *originalType = method_getTypeEncoding(originalMethod);
        const char *swizzledType = method_getTypeEncoding(swizzledMethod);
        class_replaceMethod(cls,@selector(openURL:),swizzledIMP,swizzledType);
        class_replaceMethod(cls,@selector(g_openURL:),originalIMP,originalType);
    }
}

#pragma mark - 适配iOS14之后 Apple要求的透明IDFA权限请求
- (void)requestTracking {
    if (@available(iOS 14.0, *)) {
        [ATTrackingManager requestTrackingAuthorizationWithCompletionHandler:^(ATTrackingManagerAuthorizationStatus status) {
                
        }];
    }
}

- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<NSString*, id> *)options {
 
#ifdef DEBUG
    // 测试环境
    if ([QTMobClick handleUrl:url]) {
            return YES;
        }
#endif
  
    return [self applicationExternalJump:url];
}

//app已经进入后台
- (void)applicationDidEnterBackground:(UIApplication *)application {
    [[NSUserDefaults standardUserDefaults] synchronize];
    _backDate = [NSDate date];
    
    // 防止YBMGCDTimer进入后台倒计时暂停
    UIApplication *app = [UIApplication sharedApplication];
    __block UIBackgroundTaskIdentifier bgTask;
    bgTask = [app beginBackgroundTaskWithExpirationHandler:^{
        dispatch_async(dispatch_get_main_queue(), ^{
            if (bgTask != UIBackgroundTaskInvalid) {
                [app endBackgroundTask:bgTask];
                bgTask = UIBackgroundTaskInvalid;
            }
        });
    }];
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        [NSThread sleepForTimeInterval:100];
        dispatch_async(dispatch_get_main_queue(), ^{
            if (bgTask != UIBackgroundTaskInvalid) {
                [app endBackgroundTask:bgTask];
                bgTask = UIBackgroundTaskInvalid;
            }
        });
    });
    
}

//APP将要进入前台
- (void)applicationWillEnterForeground:(UIApplication *)application {
    if (_backDate != nil) {
        NSTimeInterval begin = [_backDate timeIntervalSince1970];
        NSTimeInterval now = [[NSDate date] timeIntervalSince1970];
        // 一个小时之后再次启动显示广告页
        if ((now-begin) > 60*60) {
            [self handelAD];
        } else {
            _backDate = [NSDate date];
        }
    }
    [application setApplicationIconBadgeNumber:0];
    [JPUSHService resetBadge];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"applicationWillEnterForeground" object:nil];
    
    // 大转盘分享完毕需要刷新页面
    NSString *temp = [[NSUserDefaults standardUserDefaults] objectForKey:@"kWechatShareBigTurntableKey"];
    if (!kStringIsEmpty(temp)) {
        [[NSNotificationCenter defaultCenter] postNotificationName:@"kWechatShareBigTurntableNotifier" object:nil];
    }
}

//APP已经进入前台
- (void)applicationDidBecomeActive:(UIApplication *)application {
//    [self handYCode];
    //进入前台，更新秒杀时间
    [InterfaceManager getInternetTime:^(BOOL isSuccess, NSString *msg, id data) {
        [[NSNotificationCenter defaultCenter] postNotificationName:@"spikeCellReload"object:data];
    }];
    // 刷新大转盘页面
    [[NSNotificationCenter defaultCenter] postNotificationName:@"kRefreshTheBigTurntableNotifier" object:nil];
    // 适配iOS14之后 Apple要求的透明IDFA权限请求
    [self requestTracking];
    
    //根据token查询账号ID：accountId
    if(!XYYUserData.ShareUserData.accountId.length && XYYUserData.ShareUserData.token.length){
        NSMutableDictionary *dic = [[NSMutableDictionary alloc]init];
        [XYYHttpApi postRequestWithUrl:API_Interface(@"/app/getAccountId") Param:dic IsNeedSign:YES SuccessBlock:^(NSDictionary *resultData) {
            if(resultData){
                NSNumber *accountId = resultData[@"accountId"];
                XYYUserData.ShareUserData.accountId = accountId.stringValue;
            }
        } FailureBlock:^(NSDictionary *resultData) {
            
        }];
    }
}

//APP将要退出
- (void)applicationWillTerminate:(UIApplication *)application {
    _backDate = nil;
    // 应用杀死 清除SaaS渠道来源信息
    [self removeSaasChanel];
}

/// 清除SaaS渠道来源信息
- (void)removeSaasChanel {
    [[NSUserDefaults standardUserDefaults] removeObjectForKey:kAppOpenChannel];
    [[NSUserDefaults standardUserDefaults] removeObjectForKey:kAppOpenRegisterSource];
    [[NSUserDefaults standardUserDefaults] removeObjectForKey:kSaasOrganSign];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

- (void)productViewControllerDidFinish:(SKStoreProductViewController *)viewController {
    [self.window.rootViewController dismissViewControllerAnimated:YES completion:nil];
}

- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken {
    /// Required - 注册 DeviceToken
    [JPUSHService registerDeviceToken:deviceToken];
    [JPUSHService registrationIDCompletionHandler:^(int resCode, NSString *registrationID) {
        [[NSUserDefaults standardUserDefaults] setObject:registrationID forKey:@"deviceTokenString"];
        [self registeXyyPushServiceRemoteNotificationsWithDeviceToken:deviceToken];
    }];
    [[UIApplication sharedApplication] setApplicationIconBadgeNumber:0];
    [JPUSHService resetBadge];
   
}


- (void)registeXyyPushServiceRemoteNotificationsWithDeviceToken:(NSData *)deviceToken
{
    NSMutableDictionary * params = [NSMutableDictionary dictionary];
    params[@"deviceId"] = [UIDevice currentDevice].identifierForVendor.UUIDString;
    params[@"appVersion"] = [[[NSBundle mainBundle] infoDictionary] valueForKey:@"CFBundleShortVersionString"];
    params[@"appId"] = @"ybm";
    params[@"platformType"] = @"ios";
    params[@"phoneType"] =  [YBMHttpClient getCurrentDeviceModel];
    params[@"brandType"] = @"apple";
    params[@"manufacturerToken"] = [self getDeviceTokenStr:deviceToken];
    params[@"channelType"] = @"ios";
    params[@"jpushToken"] =  [[NSUserDefaults standardUserDefaults] objectForKey:@"deviceTokenString"];
    
    [XYYHttpApi postRequestWithUrl:API_Interface(@"/app/registerPushtoken") Param:params IsNeedSign:YES SuccessBlock:^(NSDictionary *resultData) {
        NSString *pushToken = resultData[@"pushToken"];
        [[NSUserDefaults standardUserDefaults] setObject:pushToken forKey:@"appleTokenString"];
        if (pushToken && [[XYYUserData ShareUserData] isLogin]) {
            [self registeRelativeAccountPushServiceRemoteNotificationsWithPushToken:pushToken];
        }
    } FailureBlock:^(NSDictionary *resultData) {
       
    }];
}
- (void)registeRelativeAccountPushServiceRemoteNotificationsWithPushToken:(NSString *)pushToken
{
    NSMutableDictionary * params = [NSMutableDictionary dictionary];
    params[@"appId"] = @"ybm";
    params[@"ecCustomerCode"] = [[XYYUserData ShareUserData] merchantId];
    params[@"merchantId"] = [[XYYUserData ShareUserData] merchantId];
    params[@"pushToken"] = pushToken;
    [XYYHttpApi postRequestWithUrl:API_Interface(@"/app/pushTokenBindMember") Param:params IsNeedSign:YES SuccessBlock:^(NSDictionary *resultData) {
        
    } FailureBlock:^(NSDictionary *resultData) {
       
    }];
}

-(NSString *)getDeviceTokenStr:(NSData *)deviceToken{
    if (deviceToken) {
        NSMutableString *hexString = [NSMutableString stringWithCapacity:deviceToken.length*2];
        const unsigned char *bytes = deviceToken.bytes;
        for (NSInteger i = 0; i < deviceToken.length; i++) {
            [hexString appendFormat:@"%02x", bytes[i]];
        }
        return [hexString copy];
    }else {
        return @"";
    }
}

- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error {
    // 注册APNS失败
    // 自行处理
}

//iOS7及以上接受到通知
- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler {
    [application setApplicationIconBadgeNumber:0];
    [JPUSHService resetBadge];
    
    // Required, iOS 7 Support
    [JPUSHService handleRemoteNotification:userInfo];
    completionHandler(UIBackgroundFetchResultNewData);
    
    if (application.applicationState == UIApplicationStateActive) {
    } else {
        // 最开始的判断方式
        [self handleNotificationActionWithInfo:userInfo];
    }
}

// Universal Links
- (BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity restorationHandler:(void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler{
    if ([userActivity.activityType isEqualToString:NSUserActivityTypeBrowsingWeb]) {
        NSURL *webpageUrl = userActivity.webpageURL;
        if ([webpageUrl.absoluteString hasPrefix:@"https://app-v4.ybm100.com/y/wxsdk"]) {
            [WXApi handleOpenUniversalLink:userActivity delegate:self];
        }else if([webpageUrl.absoluteString containsString:@"app.test.ybm100.com/x/path.html"] || [webpageUrl.absoluteString containsString:@"app.ybm100.com/x/path.html"]){
            NSDictionary *param = [XYYRouterManager parseURLParameters:webpageUrl];
            NSString *urlString = [param objectForKey:@"url"];
            return  [self applicationExternalJump:[NSURL URLWithString:urlString]];
        }
    }
    return YES;
}

//企业微信分享：重写AppDelegate的handleOpenURL和openURL方法，第三方iOS App需要在这两个方法中调用SDK的handleOpenURL接口方法，让SDK有机会在第三方App内部处理SSO结果返回给第三方iOS App后的后续处理工作
- (BOOL)application:(UIApplication *)application openURL:(NSURL *)url sourceApplication:(nullable NSString *)sourceApplication annotation:(id)annotation {
    return [self handleOpenURL:url sourceApplication:sourceApplication];
}

- (BOOL)handleOpenURL:(NSURL *)url sourceApplication:(NSString *)sourceApplication {
    return [WWKApi handleOpenURL:url delegate:self];
}

#pragma mark - WWKApiDelegate 企业微信分享
// 如果第三方程序向企业微信发送了sendReq的请求，那么onResp会被回调。sendReq请求调用后，会切到企业微信终端程序界面。
- (void)onResp:(WWKBaseResp *)resp {
}
// onReq是企业微信终端向第三方程序发起请求，要求第三方程序响应。第三方程序响应完后必须调用sendRsp返回。在调用sendRsp返回时，会切回到企业微信终端程序界面。
- (void) onReq:(WWKBaseReq*)req {
}

@end
