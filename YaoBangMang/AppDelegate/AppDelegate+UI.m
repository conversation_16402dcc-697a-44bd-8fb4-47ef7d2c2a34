//
//  AppDelegate+UI.m
//  YaoBangMang
//
//  Created by 骆阳光 on 2020/1/20.
//  Copyright © 2020 XiaoYaoYao.Ltd. All rights reserved.
//

#import "AppDelegate+UI.h"
#import "YBMCommonManager.h"
#import "XYYVersionUpdateView.h"
#import "XYYShowShareCodeView.h"
#import "AppDelegate+XHLaunchAd.h"
#import "XYYNavigationController.h"
#import "YBMTabBarController.h"
#import "YBMConstKey.h"
#import "YBMPrivateWindowController.h"
#import <CanaryAppUpdate.h>
//#import <CoreTelephony/CTCellularData.h>

@implementation AppDelegate (UI)

- (void)setupWindow:(UIApplication *)application {
    /// 适配iOS15 下TableView增加的sectionHeaderTopPadding属性
    [self fixTableViewPlainStyleHeaderTopInset];
    // 显示状态栏
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    [application setStatusBarHidden:NO withAnimation:UIStatusBarAnimationFade];
    
    // 创建窗口
    self.window = [[UIWindow alloc]initWithFrame:[UIScreen mainScreen].bounds];
    self.window.backgroundColor = [UIColor colorWithHexString:@"#FFFFFF"];
    //只针对新的引导图必须显示 key需要更换
    
    if (![[NSUserDefaults standardUserDefaults] boolForKey:@"firstLaunch_305"]) {
        //新app 必须显示
        [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"firstLaunch_305"];
        UIViewController * GuidePageViewController = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:@"ybmpage://GuidePageViewController"];
        self.window.rootViewController = GuidePageViewController;
    } else {
        [self handelAD];
        if (![XYYUserData ShareUserData].isLogin) {//没有登录
            UIViewController *loginVc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:@"ybmpage://YBMLoginController"];
            XYYNavigationController *navVC = [[XYYNavigationController alloc] initWithRootViewController:loginVc];
            self.window.rootViewController = navVC;
        } else {
            // 已登录 创建窗口根控制器为TabBarController
            YBMTabBarController *tabBar = [[YBMTabBarController alloc]init];
            self.window.rootViewController = tabBar;
        }
    }
    
    // 显示窗口
    [self.window makeKeyAndVisible];
}

- (void)fixTableViewPlainStyleHeaderTopInset {
#ifndef __IPHONE_15_0
#define __IPHONE_15_0 150000
#endif
    
#if __IPHONE_OS_VERSION_MAX_ALLOWED >= __IPHONE_15_0
    if (@available(iOS 15.0, *)) {
        UITableView * app = [UITableView appearance];
        app.sectionHeaderTopPadding = 0;
    }
#endif
}

/// 设置隐私弹框
- (void)setupPrivateWindow {
    if (![NSUserDefaults.standardUserDefaults boolForKey:kYBMPrivateWindowKey]) {
        YBMPrivateWindowController *vc = [[YBMPrivateWindowController alloc] init];
        XYYNavigationController *nav = [[XYYNavigationController alloc] initWithRootViewController:vc];
        nav.modalPresentationStyle = UIModalPresentationOverFullScreen;
        [self.ybm_viewController presentViewController:nav animated:NO completion:nil];
    }
}

#pragma mark - 药口令

//处理剪切板上的药口令，app到前台时就需要掉用
- (void)handYCode {
    UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
    //没有登录或者没有粘贴字符串
    if (![XYYUserData ShareUserData].isLogin ||pasteboard.string.length== 0) {
        return;
    }
    if ([pasteboard.string rangeOfString:@"药帮忙"].length>0) {
        if ([pasteboard.string isEqualToString:[InterfaceManager ShareInterfaceManager].shareKey]) {
            [InterfaceManager ShareInterfaceManager].shareKey = nil;
            pasteboard.string = @"";
            return;
        }
        [XYYShowShareCodeView startTestShareCode:pasteboard.string];
        pasteboard.string = @"";
    }
}

#pragma mark - 检查版本更新

- (void)checkAppVersionUpdate {
    [self checkAppVersionForCanary];
#if DEBUG
    return;
#endif
    
#pragma clang diagnostic ignored "-Wunreachable-code"
    @weakify(self)
    [YBMCommonManager.shared requestVersionUpdateDataComplete:^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
        @strongify(self)
        if (success) {
            XYYVersionUpdateView * updataView = [XYYVersionUpdateView getVersionUpdateView];
            updataView.updateModel = response;
            [self.window addSubview: updataView];
        }
    }];
}

// 新版更新
- (void)checkAppVersionForCanary{
#if DEBUG
    [CanaryAppUpdate instance].isProduct = NO;
    [CanaryAppUpdate instance].appTenantId = @"100019";
#else
    [CanaryAppUpdate instance].isProduct = YES;
    [CanaryAppUpdate instance].appTenantId = @"100020";
#endif
    [CanaryAppUpdate instance].appChannel = @"AppStore";
    
    if (XYYUserData.ShareUserData.isLogin) {
        [CanaryAppUpdate instance].appUserId = XYYUserData.ShareUserData.merchantId;
    }
    
    [CanaryAppUpdate instance].monitorListener = ^(NSString * _Nonnull eventName, NSDictionary * _Nullable eventParams) {
        [YBMAnalyticsUtil track:eventName properties:eventParams];
    };
    
    [[CanaryAppUpdate instance] startUpdate];
}

#pragma mark - 获取网络权限状态
///CTCellularData在iOS9之前是私有类，权限设置是iOS10开始的，所以App Store审核没有问题
- (void)networkStatus{
    AFNetworkReachabilityManager *manager = [AFNetworkReachabilityManager sharedManager];
    [manager startMonitoring];//开始监听
    [manager setReachabilityStatusChangeBlock:^(AFNetworkReachabilityStatus status) {
        if (status == AFNetworkReachabilityStatusUnknown || status ==
            AFNetworkReachabilityStatusNotReachable) {
            [self alertGotoSettings];
        }
    }];
    
//    if (![AFNetworkReachabilityManager sharedManager].isReachable) {
//        [self alertGotoSettings];
//    }
    //应用启动后，检测应用中是否有联网权限，iOS9.0之后才能用
//    CTCellularData *cellularData = [[CTCellularData alloc]init];
//    cellularData.cellularDataRestrictionDidUpdateNotifier =  ^(CTCellularDataRestrictedState state){
//        if (![AFNetworkReachabilityManager sharedManager].isReachable && state == kCTCellularDataRestricted) {
//            [self alertGotoSettings];
//        }
//        //获取联网状态
//        switch (state) {
//            case kCTCellularDataRestricted://关闭
//                NSLog(@"蜂窝数据受限制");
//                break;
//            case kCTCellularDataNotRestricted:{//WALN 与 蜂窝移动网
//                NSLog(@"蜂窝数据不受限制");
//                break;
//            case kCTCellularDataRestrictedStateUnknown:
//                NSLog(@"蜂窝数据限制状态未知");
//                break;
//            default:
//                break;
//            };
//        };
//    };
}

//检查网络，看是否需要跳转到设置页面
- (void)alertGotoSettings{
    dispatch_async(dispatch_get_main_queue(), ^{
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"网络连接失败" message:@"检测到网络权限可能未开启，您可以在“设置”中检查蜂窝移动网络" preferredStyle:UIAlertControllerStyleAlert];
        [alert addAction:[UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:nil]];
        [alert addAction:[UIAlertAction actionWithTitle:@"设置" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            if (SystemVersion >= 10.0) {
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString] options:@{} completionHandler:nil];
            } else {
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString]];
            }
        }]];
        [[UIApplication sharedApplication].keyWindow.rootViewController presentViewController:alert animated:YES completion:nil];
    });
}

@end
