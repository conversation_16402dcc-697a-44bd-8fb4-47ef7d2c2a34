//
//  AppDelegate+XHLaunchAd.m
//  YaoBangMang
//
//  Created by 骆阳光 on 2020/1/20.
//  Copyright © 2020 XiaoYaoYao.Ltd. All rights reserved.
//

#import "AppDelegate+XHLaunchAd.h"
#import "YBMCommonManager.h"
#import "XHLaunchAd.h"
#import "XHLaunchAdView.h"
#import "XYYWebViewVC.h"

@implementation AppDelegate (XHLaunchAd)

- (BOOL)xhLaunchAd:(XHLaunchAd *)launchAd clickAtOpenModel:(id)openModel clickPoint:(CGPoint)clickPoint {
    CGRect buttonFrame = CGRectMake(50, XYYScreenH *2 /3, XYYScreenW - 100, XYYScreenH / 3  - 30);//隐私合规，非按钮区域点击无效果
    if(!CGRectContainsPoint(buttonFrame, clickPoint)){
        return NO;
    }
    
    YBMLaunchAdModel *model = (YBMLaunchAdModel *)openModel;
    
    //点击埋点
    [self trackClick:model];
    [self trackLaunchAdClick:model];
    
    if ([XYYUserData ShareUserData].isLogin) {//没有登录
        NSMutableDictionary *paramDic = [NSMutableDictionary dictionary];
        paramDic[@"referrer"] = @"XHLaunchAd";
        paramDic[@"referrerTitle"] = @"开屏广告";
        paramDic[@"entrance"] =  @"开屏广告";
        paramDic[@"referrerModule"] = @"开屏广告";
        NSString *realUrl = [[XYYRouterManager ShareRouterManager] returnUrlstr:model.jumpUrl params:paramDic];
        UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:realUrl];
        if ([vc isKindOfClass:[XYYWebViewVC class]]) {
            XYYWebViewVC *web = (XYYWebViewVC *)vc;
            web.jgspid = model.jgSpid?:@"";
            web.titleStr = nil;
        }
        UITabBarController *tab = (UITabBarController *)self.window.rootViewController;
        UINavigationController *nav = tab.selectedViewController;
        [nav pushViewController:vc animated:YES];
    }
    return YES;
}

- (void)xhLaunchAd:(XHLaunchAd *)launchAd clickSkipButton:(UIButton *)skipButton {
    /// 雪地埋点 - 开屏广告点击 跳过
    [YBMAnalyticsUtil track:@"action_OpenScreen_Ad" properties:@{@"actionType":@"2"}];
}

- (void)handelAD {
    [XHLaunchAd setWaitDataDuration:2.0];
    @weakify(self)
    [YBMCommonManager.shared requestLaunchAdDataComplete:^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
        @strongify(self)
        if (success && response) {
            YBMLaunchAdModel *model = response;
            [self showLaunchAd:model];
            if (model.isNewTheme) {
                [[AppConfigue shared] setIsNewTheme:@"1"];
            } else {
                [[AppConfigue shared] setIsNewTheme:@"0"];
            }
        }
    }];
}

- (void)showLaunchAd:(YBMLaunchAdModel *)model {
    if (!model.isdisplay) {
        return;
    }
    // 验证是否是后台再次打开
    [self verificationAdStatus];
    //配置广告数据
    XHLaunchImageAdConfiguration *imageAdconfiguration = [XHLaunchImageAdConfiguration new];
    //广告停留时间
    imageAdconfiguration.duration = 4;
    //广告frame
    imageAdconfiguration.frame = CGRectMake(0, 0, XYYScreenW, XYYScreenH);
    //广告图片URLString/或本地图片名(.jpg/.gif请带上后缀)
    
    imageAdconfiguration.imageNameOrURLString = [NSString stringWithFormat:@"%@", isIPhoneXSeries ? model.startImageOther : model.startImage];
    if(model.startImageOther == nil){
        imageAdconfiguration.imageNameOrURLString  = model.startImage;
    }
    //缓存机制(仅对网络图片有效)
    //为告展示效果更好,可设置为XHLaunchAdImageCacheInBackground,先缓存,下次显示
    imageAdconfiguration.imageOption = XHLaunchAdImageRefreshCached;
    //图片填充模式
    imageAdconfiguration.contentMode = UIViewContentModeScaleToFill;
    //广告点击打开链接
    imageAdconfiguration.openModel = model;
    //广告显示完成动画
    imageAdconfiguration.showFinishAnimate = ShowFinishAnimateFadein;
    //广告显示完成动画时间
    imageAdconfiguration.showFinishAnimateTime = 0.8;
    //跳过按钮类型
    imageAdconfiguration.skipButtonType = SkipTypeTimeText;
    //后台返回时,是否显示广告
    imageAdconfiguration.showEnterForeground = NO;
    //显示开屏广告
    self.ad = [XHLaunchAd imageAdWithImageAdConfiguration:imageAdconfiguration delegate:self];

    //曝光埋点
    [self trackExposure:model];
    [self trackLaunchAdExposure:model];
    [self trackLaunchAdSubModuleExposure:model];
}

/// 验证启动广告是否需要重新加载 用于用户退到后台再启动显示逻辑
- (void)verificationAdStatus {
    if (self.ad) {
        @try {
            UIWindow *window = [self.ad valueForKey:@"window"];
            if (!window) {
    #pragma clang diagnostic ignored"-Wundeclared-selector"
                if ([self.ad respondsToSelector:@selector(setupLaunchAd)]) {
                    [self.ad performSelector:@selector(setupLaunchAd)];
                }
            }
        } @catch (NSException *exception) {
        } @finally {
        }
    }
}

#pragma mark - 埋点
- (void)trackClick:(YBMLaunchAdModel *)model{
    /// 雪地埋点 - 开屏广告点击 点击跳转
    [YBMAnalyticsUtil track:@"action_OpenScreen_Ad" properties:@{@"actionType":@"1",@"link":model.jumpUrl?:@""}];
}

- (void)trackExposure:(YBMLaunchAdModel *)model{
    /// 雪地埋点 - 开屏广告页面曝光
    [YBMAnalyticsUtil track:@"page_OpenScreen_Ad"];
}

//开屏页面广告 曝光
- (void)trackLaunchAdExposure:(YBMLaunchAdModel *)model{
    if (!model) return;
    YBMCMSTrackDataModel *trackData = model.trackData.copy;
    
    YBMQTSpmModel *spmEntity = trackData.spmEntity.copy;
    spmEntity.spmC = @"0";
    spmEntity.spmD = @"0";
    trackData.spmEntity = spmEntity;
    
    NSString *spm = [YBMQTTrack createSpmWithModel:trackData page:@"startPage"];
    NSMutableDictionary *qtDic = NSMutableDictionary.new;
    qtDic[@"spm_cnt"] = spm;
    [YBMQTTrack track:kQTPageExposure attributes:qtDic];
}

//开屏广告组件 曝光
- (void)trackLaunchAdSubModuleExposure:(YBMLaunchAdModel *)model{
    if (!model) return;
    YBMCMSTrackDataModel *trackData = model.trackData.copy;
    YBMQTSpmModel *spmEntity = trackData.spmEntity.copy;
    spmEntity.spmD = @"0";
    trackData.spmEntity = spmEntity;
    
    NSString *spm = [YBMQTTrack createSpmWithModel:trackData page:@"startPage"];
    NSMutableDictionary *qtDic = NSMutableDictionary.new;
    qtDic[@"spm_cnt"] = spm;
    [YBMQTTrack track:kQTPageComponentExposure attributes:qtDic];
}

//开屏广告组件 点击
- (void)trackLaunchAdClick:(YBMLaunchAdModel *)model{
    if (!model) return;
    NSString *spm = [YBMQTTrack createSpmWithModel:model.trackData page:@"startPage"];
    NSString *scm = [YBMQTTrack createSpmOrScmWithCmsModel:model.trackData trackType:YBMTrackTypeSCM];
    NSMutableDictionary *qtDic = NSMutableDictionary.new;
    qtDic[@"spm_cnt"] = spm;
    qtDic[@"scm_cnt"] = scm;
    [YBMQTTrack track:kQTActionSubModuleClick attributes:qtDic];
}

@end
