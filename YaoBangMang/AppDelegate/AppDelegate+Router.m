//
//  AppDelegate+Router.m
//  YaoBangMang
//
//  Created by 骆阳光 on 2020/1/20.
//  Copyright © 2020 XiaoYaoYao.Ltd. All rights reserved.
//

#import "AppDelegate+Router.h"
#import "YBMScanningController.h"
#import "UPPaymentControl.h"
#import <AlipaySDK/AlipaySDK.h>
#import "NSString+Parameter.h"
#import "NSString+Extension.h"
#import "YBMPayWechatAppletsModel.h"

@implementation AppDelegate (Router)

/// 处理外部跳转
- (BOOL)applicationExternalJump:(NSURL *)url {
    // 判断是否登录
    if (![XYYUserData ShareUserData].isLogin) {
        // 如果没登录 且路由是/register
        if ([url.scheme isEqualToString:@"ybm100"] && [url.host isEqualToString:@"ybmmarket20.com"]) {
            if ([url.path isEqualToString:@"/register"]) {
                [self handleRegisterRoute:url];
            }
        }
        return NO;
    }
    
    // 登录状态下 注册路径 直接返回NO
    if ([url.path isEqualToString:@"/register"]) {
        return NO;
    }
    
    // 登录状态下 且路由不是/register时
    if ([url.scheme isEqualToString:@"ybm100"] && [url.host isEqualToString:@"ybmmarket20.com"]) {
        if ([url.path isEqualToString:@"/widget"]) {
            /// 3D touch 小组件
            return [self handleOpenFromWidgetWithUrl:url];
        } else if ([url.path isEqualToString:@"/commonh5activity"]) {
            /// js跳H5页面
            return [self handleOpenFromJSToH5WithUrl:url];
        } else {
            /// js跳原生页面
            return [self handleOpenFromJSToNativeWithUrl:url];
        }
    } else {
        if ([[url absoluteString] containsString:@"yaobangmang://"]) {
            /// 之前跳转旧逻辑
            return [self handleOpenFromOtherWithUrl:url];
        } else if ([url.scheme isEqualToString:@"ybmAlipay"]) {
            return [self handleOpenFromAlipayWithUrl:url];
        } else if ([url.scheme isEqualToString:@"wx5766ec723a326dff"]) {
            return [self handleOpenFromWXpayWithUrl:url];
        } else if ([url.scheme isEqualToString:@"ybmUnionpay"]){
            // 银联
            [[UPPaymentControl defaultControl] handlePaymentResult:url completeBlock:^(NSString *code, NSDictionary *data) {
                // 返回状态通知界面提示
                [[NSNotificationCenter defaultCenter] postNotificationName:@"unionPayState" object:code];
            }];
        } else {
            return [APOpenAPI handleOpenURL:url delegate:self];
        }
    }
    
    return YES;
}

/// 小组件 3Dtouch跳转
- (BOOL)handleOpenFromWidgetWithUrl:(NSURL *)url {
    if ([url.query isEqualToString:@"home"]) {
        [YBMAnalyticsUtil track:@"YBMWidget_click_home"];
        [[NSUserDefaults standardUserDefaults] setObject:@"ybm" forKey:@"widgetHome"];
        [[NSNotificationCenter defaultCenter] postNotificationName:@"pushHome" object:nil];
        UITabBarController *tab = (UITabBarController *)self.window.rootViewController;
        UINavigationController *nav = tab.selectedViewController;
        [nav popToRootViewControllerAnimated:NO];
        return YES;
    } else if ([url.query isEqualToString:@"scan"]) {
        [YBMAnalyticsUtil track:@"YBMWidget_click_scan"];
        YBMScanningController *scanVc = [YBMScanningController new];
        scanVc.type = YBMPRODUCTDETAIL;
        [self pushToVc:scanVc];
        return YES;
    } else if ([url.query isEqualToString:@"message"]) {
        [YBMAnalyticsUtil track:@"YBMWidget_click_message"];
        NSString *head = API_Interface(@"/static/xyyvue/dist");
        NSString *str = [NSString stringWithFormat:@"%@/#messagecenter",head];
        NSString *strRouter = [NSString stringWithFormat:@"ybmpage://commonh5activity?umkey=1111hd1&url=%@?merchantId=%@&ybm_title=消息中心&head_menu=0",str,[XYYUserData ShareUserData].merchantId];
        UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:strRouter];
        [self pushToVc:vc];
        return YES;
    } else if ([url.query isEqualToString:@"order"]) {
        [YBMAnalyticsUtil track:@"YBMWidget_click_order"];
        NSString *url = [NSString stringWithFormat:@"ybmpage://myorderlist?order_state=%@&goToStatus=4",@"0"];
        UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:url];
        [self pushToVc:vc];
        return YES;
    }
    return NO;
}

- (BOOL)handleOpenFromJSToH5WithUrl:(NSURL *)url {
    NSData *data = [[NSData alloc]initWithBase64EncodedString:url.query options:0];
    NSString *query = url.query;
    if (data){
        query = [[NSString alloc]initWithData:data encoding:NSUTF8StringEncoding];
    }
    if (!kStringIsEmpty(query) &&
        ([query containsString:@"app-v4.ybm100.com"] ||
         [query containsString:@"new-app.stage.ybm100.com"] ||
         [query containsString:@"new-app.test.ybm100.com"] ||
         [query containsString:@"app.ybm100.com"] ||
         [query containsString:@"app.test.ybm100.com"] ||
         [query containsString:@"app.stage.ybm100.com"])) {
        UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:[NSString stringWithFormat:@"ybmpage://commonh5activity?%@",query]];
        if (vc != nil) {
            [self.ybm_viewController.navigationController pushViewController:vc animated:YES];
            return YES;
        }
    }
    if(!kStringIsEmpty(query) &&
       ([query containsString:@"app-v4.ybm100.com"])){
        [YBMAnalyticsUtil track:launchYbmApp_FreeproductDetail_success properties:@{}];
    }
    return NO;
}

- (BOOL)handleOpenFromJSToNativeWithUrl:(NSURL *)url {
    if (!kStringIsEmpty(url.query)) {
        NSData *data = [[NSData alloc]initWithBase64EncodedString:url.query options:0];
        NSString *query = url.query;
        if (data){
            query = [[NSString alloc]initWithData:data encoding:NSUTF8StringEncoding];
        }
        if (!kStringIsEmpty(query)) {
            NSString *urlString = [NSString stringWithFormat:@"ybmpage:/%@?%@", url.path ,query];
            NSDictionary *paramDic = [urlString parameterWithURLString:urlString];
            
            //移动端用户点击分享链接唤起药帮忙APP 埋点
            NSMutableDictionary *dic = [[NSMutableDictionary alloc]init];
            dic[@"type"] = paramDic[@"type"];
            dic[@"user_id"] = paramDic[@"user_id"];
            dic[@"skuId"] = paramDic[@"skuId"];
            [YBMAnalyticsUtil track:@"action_Sharedlink_Click" properties:dic];
            
            // 如果是SaaS过来的路由
            if ([paramDic.allKeys containsObject:@"saasOrderSourcePath"]) {
                return [self handleSaasOpenUrlString:urlString paramDic:paramDic];
            } else {
                UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:urlString];
                if (vc != nil) {
                    [self.ybm_viewController.navigationController pushViewController:vc animated:YES];
                    return YES;
                }
            }
        }
    }
    return NO;
}

/// 之前外部跳转原生旧逻辑
- (BOOL)handleOpenFromOtherWithUrl:(NSURL *)url {
    NSString *urlString = [[url absoluteString] stringByReplacingOccurrencesOfString:@"yaobangmang://" withString:@""];
    if ([urlString containsString:@"ybmpage//"]) {
        urlString = [urlString stringByReplacingOccurrencesOfString:@"ybmpage//" withString:@"ybmpage://"];
    }
    UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:[NSString stringWithFormat:@"%@",urlString]];
    if (vc != nil) {
        [self.ybm_viewController.navigationController pushViewController:vc animated:YES];
        return YES;
    }
    return NO;
}

- (BOOL)handleOpenFromAlipayWithUrl:(NSURL *)url {
    // 支付跳转支付宝钱包进行支付，处理支付结果
    [[AlipaySDK defaultService] processOrderWithPaymentResult:url standbyCallback:^(NSDictionary *resultDic) {
        
        /* 9000 订单支付成功
         8000 正在处理中
         4000 订单支付失败
         6001 用户中途取消
         6002 网络连接出错 */
        NSString *orderState = resultDic[@"resultStatus"];
        
        // 支付提示
        [[NSNotificationCenter defaultCenter] postNotificationName:@"aliPayState" object:orderState];
    }];
    return YES;
}

- (BOOL)handleOpenFromWXpayWithUrl:(NSURL *)url {
    return [WXApi handleOpenURL:url delegate:self];
}

/// 处理SaaS过来的路由
/// @param url URL
- (BOOL)handleSaasOpenUrlString:(NSString *)urlString paramDic:(NSDictionary *)paramDic {
    // 判断是否含有merchantId
    if ([paramDic.allKeys containsObject:@"merchantId"]) {
        NSString *merchantId = [NSString stringWithFormat:@"%@",paramDic[@"merchantId"]];
        if (merchantId.integerValue == [XYYUserData ShareUserData].merchantId.integerValue) {
            //是的话 且商户ID和本地存储的一致 则记录来源 否则不做处理
            [[NSUserDefaults standardUserDefaults] setObject:paramDic[@"saasOrderSourcePath"] forKey:kAppOpenChannel];
            [[NSUserDefaults standardUserDefaults] synchronize];
            // 信息一致 则跳转
            UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:urlString];
            if (vc != nil) {
                [self.ybm_viewController.navigationController pushViewController:vc animated:YES];
                return YES;
            }
        } else {
            // 跳首页
            [self.ybm_viewController.navigationController popToRootViewControllerAnimated:YES];
            dispatch_async(dispatch_get_main_queue(), ^{
                NSInteger index = YBMTabbarTypeHome;
                UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:[NSString stringWithFormat:@"ybmpage://maintab?tab=%zd",index]];
                [AppConfigue pushVC:vc];
            });
            return YES;
        }
        
    } else {
        // 是SaaS过来的 但不包含merchantId 直接跳首页
        [self.ybm_viewController.navigationController popToRootViewControllerAnimated:YES];
        dispatch_async(dispatch_get_main_queue(), ^{
            NSInteger index = YBMTabbarTypeHome;
            UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:[NSString stringWithFormat:@"ybmpage://maintab?tab=%zd",index]];
            [AppConfigue pushVC:vc];
        });
        return YES;
    }
    return NO;
}

/// 注册路由
- (BOOL)handleRegisterRoute:(NSURL *)url {
    // 去注册页面
    // 有参数的 解析参数
    if (kStringIsEmpty(url.query) == NO) {
        NSData *data = [[NSData alloc]initWithBase64EncodedString:url.query options:0];
        NSString *query = url.query;
        if (data){
            query = [[NSString alloc]initWithData:data encoding:NSUTF8StringEncoding];
        }
        NSString *urlString = [NSString stringWithFormat:@"ybmpage:/%@?%@", url.path ,query];
        if (kStringIsEmpty(query) == NO) {
            NSDictionary *paramDic = [urlString parameterWithURLString:urlString];
            if ([paramDic.allKeys containsObject:@"registerSource"]) {
                // SaaS跳转过来的注册
                [[NSUserDefaults standardUserDefaults] setObject:paramDic[@"registerSource"] forKey:kAppOpenRegisterSource];
            }
            if ([paramDic.allKeys containsObject:@"organSign"]) {
                // SaaS 机构ID
                [[NSUserDefaults standardUserDefaults] setObject:paramDic[@"organSign"] forKey:kSaasOrganSign];
            }
            
            UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:urlString];
            if (vc != nil) {
                [self.ybm_viewController.navigationController pushViewController:vc animated:YES];
                return YES;
            }
        }
    } else {
        // 没参数的 直接跳
        UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:[NSString stringWithFormat:@"ybmpage:/%@?", url.path]];
        if (vc != nil) {
            [self.ybm_viewController.navigationController pushViewController:vc animated:YES];
            return YES;
        }
    }
    return YES;
}

- (void)pushToVc:(UIViewController *)vc {
    if (vc == nil) return;
    UITabBarController *tab = (UITabBarController *)self.window.rootViewController;
    UINavigationController *nav = tab.selectedViewController;
    if(self.ybm_viewController.presentingViewController) {
        [self.ybm_viewController dismissViewControllerAnimated:NO completion:nil];
    } else {
        [nav popToRootViewControllerAnimated:NO];
    }
    [nav pushViewController:vc animated:YES];
}

// 微信分享回调
- (void)onReq:(BaseReq *)req {
    if ([req isKindOfClass:LaunchFromWXReq.class]) {
        LaunchFromWXReq *result = (LaunchFromWXReq *)req;
        if (result.message.messageExt.length > 0) {
            UIViewController *vc = [XYYRouterManager.ShareRouterManager mapVCFromStrUlr:result.message.messageExt];
            if (vc) {
                /// 微信公众号 启动APP  无法跳转问题 。 做延迟500毫秒处理
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(500 * NSEC_PER_MSEC)), dispatch_get_main_queue(), ^{
                    [AppConfigue pushVC:vc];
                });
            }
        }
    }
}

- (void)onResp:(BaseResp*)resp {
    if ([resp isKindOfClass:[SendMessageToWXResp class]]) {
        
        // 大转盘分享完毕需要刷新页面
        NSString *temp = [[NSUserDefaults standardUserDefaults] objectForKey:@"kWechatShareBigTurntableKey"];
        if (!kStringIsEmpty(temp)) {
            [[NSNotificationCenter defaultCenter] postNotificationName:@"kWechatShareBigTurntableNotifier" object:nil];
        }
        
    } else if ([resp isKindOfClass:[PayResp class]]) {
        
        // 支付成功跳转订单完成界面
        NSString *weChatState = [NSString stringWithFormat:@"%d",resp.errCode];
        [[NSNotificationCenter defaultCenter] postNotificationName:@"weChatPayState" object:weChatState];
        
        if (resp.errCode != WXSuccess) {
            NSString *errorMsg = [NSString stringWithFormat:@"errcode:%d", resp.errCode];
            switch (resp.errCode) {
                case WXErrCodeUserCancel:
                    errorMsg = @"已取消支付";
                    break;
                case WXErrCodeSentFail:
                    errorMsg = @"支付结果：发送失败！";
                    break;
                case WXErrCodeAuthDeny:
                    errorMsg = @"支付结果：授权失败！";
                    break;
                default:
                    errorMsg = @"支付结果：微信不支持！";
                    break;
            }
            //当前是 待确认订单页 就不弹框
            Class cls = NSClassFromString(@"XYYPendingOrderViewController");
            Class cls1 = self.ybm_viewController.class;
            if ([cls1 isEqual:cls]) {//待确认订单页
                
            }else{//收银台
                MBShowTextNoIcon(errorMsg);
            }
        }
    } else if ([resp isKindOfClass:[WXLaunchMiniProgramResp class]]) {
        WXLaunchMiniProgramResp *miniProgramResp = (WXLaunchMiniProgramResp *)resp;
        NSString *string = miniProgramResp.extMsg;
        YBMPayWechatAppletsModel *m = [YBMPayWechatAppletsModel mj_objectWithKeyValues:string];
        if (m.success) {
            [[NSNotificationCenter defaultCenter] postNotificationName:@"weChatPayState" object:@"0"];
        } else {
            MBShowTextNoIcon(m.msg);
        }
    } else if ([resp isKindOfClass:[APBaseResp class]]){
        if (resp.errStr.length > 0) {
            MBShowTextNoIcon(resp.errStr);
        }
    }
}

@end
