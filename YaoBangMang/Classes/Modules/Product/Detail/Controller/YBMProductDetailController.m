//
//  YBMProductDetailController.m
//  YaoBangMang
//
//  Created by luoyangguang on 2020/4/19.
//  Copyright © 2020 XiaoYaoYao.Ltd. All rights reserved.
//

#import "YBMProductDetailController.h"
#import "YBMBaseViewController.h"
#import "YBMProductDetailView.h"
#import "YBMProductInstructionView.h"
#import "YBMProductNavigationBarView.h"
#import "YBMProductDetailViewModel.h"
#import "YBMProductDetailModel.h"
#import "XYYBrowsePhoto.h"
#import "YBMProductAddBuyView.h"
#import "YBMAlertView.h"
#import "XHPopMenuItem.h"
#import "XHPopMenu.h"
#import "XYYShareView.h"
#import "YBMVerticalButton.h"
#import "YBMProductDetailDefaultView.h"
#import "XYYWebViewVC.h"
#import "PurchaseCarAnimationTool.h"
#import "YBMProductPriorityBuyView.h"
#import "IQKeyboardManager.h"
#import "YBMProductTipsView.h"
#import "YBMSearchAnalysisHeader.h"
#import "YBMProductAssembleShareRootModel.h"
#import "YBMProductDetailRequest.h"
#import "YBMWechatShareView.h"
#import "YBMHomeV3Controller.h"
#import "YBMSearchVC.h"
#import "Utility.h"
#import "YBMControlPriceModel.h"

@interface YBMProductDetailController ()
<
YBMProductNavigationBarViewDelegate,
YBMProductDetailViewDelegate,
YBMProductInstructionViewDelegate,
YBMProductAddBuyViewDelegate,
ANSAutoPageTracker
>

/// 商品视图
@property (nonatomic, strong) YBMProductDetailView *productView;

/// 说明书视图
@property (nonatomic, strong) YBMProductInstructionView *instructionView;

/// 切换商品说明书导航视图
@property (nonatomic, strong) YBMProductNavigationBarView *navigationBarView;

/// 底部加购视图
@property (nonatomic, strong) YBMProductAddBuyView *addBuyView;

/// 无商品占位图（timer）
@property (nonatomic, strong) YBMProductDetailDefaultView *defaultView;

/// 智鹿吸顶框
@property (nonatomic, strong) YBMProductPriorityBuyView *priorityBuyView;

/// 秒杀温馨提示
@property (nonatomic, strong) YBMProductTipsView *tipsView;

/// 收藏菜单
@property(nonatomic, strong) XHPopMenuItem* collectMenuItem;

/// 客服入口按钮
@property (nonatomic, strong) UIButton * serviceButton;

/// 分享按钮
@property (nonatomic, strong) UIButton *collectButton;

@property (nonatomic, strong) UIButton *moreButton;

@property (nonatomic, strong) YBMProductDetailViewModel *viewModel;

@property (nonatomic, strong) YBMProductDetailModel *detailModel;

///标记我的-推荐来源的参数
@property (nonatomic, copy) NSString *source;
//用来接受h5跳过来的链路
@property (nonatomic, copy) NSString *jgEntrance;
///记录我的-推荐商品曝光/点击位置
@property (nonatomic, copy) NSString *index;
@property (nonatomic, assign) BOOL isMainProductVirtualSupplier;
@property (nonatomic,strong)NSDate *enterViewDate;
@property (nonatomic, copy) NSString *activityEntrance;
@property (nonatomic, copy) NSString *referrerModule;
@property (nonatomic, copy) NSString *keyword;
@property (nonatomic, copy) NSString *positionTypeName;
@property (nonatomic,strong)NSNumber *positionType;
@property (nonatomic,  copy)NSString *searchSortStrategyCode;

//详情页是否已经上报
@property (nonatomic,  assign) BOOL detailHasTrack;
// 是否隐藏过HUD
@property (nonatomic, assign) BOOL isHideHud;

@end

@implementation YBMProductDetailController

+ (void)load {
    NSDictionary *param = @{
        @"productdetail": @{@"className":@"YBMProductDetailController",
                            @"product_id":@"productID",
                            @"isScan":@"isScan",
                            @"product_no":@"snCode",
                            @"spType":@"sptype",
                            @"spId":@"spid",
                            @"sId":@"sid",
                            @"jgspid":@"jgspid",
                            @"sptype":@"sptype",
                            @"spid":@"spid",
                            @"sid":@"sid",
                            
                            @"pageType":@"pageType",
                            @"pageId":@"pageId",
                            @"pageName":@"pageName",
                            @"componentPosition":@"componentPosition",
                            @"componentName":@"componentName",
                            @"componentTitle":@"componentTitle",
                            
                            @"pageNo":@"pageNo",
                            @"pageSize":@"pageSize",
                            @"totalPage":@"totalPage",
                            @"totalCount":@"totalCount",
                            
                            @"nsid":@"nsid",
                            @"sdata":@"sdata",
                            
                            @"source":@"source",
                            @"index":@"index",
                            @"isMainProductVirtualSupplier":@"isMainProductVirtualSupplier",
                            @"feedIndex":@"feedIndex",
                            @"isFromHome":@"isFromHome",
                            @"page_id":@"page_id",
                            @"entrance":@"entrance",
                            @"activityEntrance":@"activityEntrance",
                            @"jgEntrance":@"jgEntrance",
                            @"referrerModule":@"referrerModule",
                            @"operationId":@"operationId",
                            @"rank":@"rank",
                            @"operationRank":@"operationRank",
                            @"keyword":@"keyword",
                            @"positionTypeName":@"positionTypeName",
                            @"positionType":@"positionType",
                            @"showRecPurchase":@"showRecPurchase",
                            @"showRecPurchaseType":@"showRecPurchaseType",
                            }};
    [[XYYRouterManager ShareRouterManager] mapClass: param];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupControls];
    [self setupNavigationBar];
    [self setupNotification];
    [self setupData];
    [self initEntrance];
    [self updateControls];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [IQKeyboardManager sharedManager].enable = NO;
  
}

- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"DetailControllerViewDidAppear" object:nil];//视图显示，详情页视频如果暂停播放，就继续播放
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [IQKeyboardManager sharedManager].enable = YES;
    [[NSNotificationCenter defaultCenter] postNotificationName:@"DetailControllerViewWillDisappear" object:nil];//将要消失，详情页视频如果正在播放，就暂停播放
    
    // 商详页关闭埋点
    [self auroraAnalysisWithProductDetailClose];
}

#pragma mark - 数据请求处理

- (void)setupData {
    NSMutableDictionary *detailParams = [NSMutableDictionary dictionary];
    detailParams[@"id"] = self.productID;
    if(self.isMainProductVirtualSupplier){
        detailParams[@"isMainProductVirtualSupplier"] = @"true";
    }
    detailParams[@"code"] = self.snCode;
    // 埋点需求
    detailParams[@"source"] = self.source;
    detailParams[@"index"] = self.index;
    detailParams[@"sptype"] = self.sptype;
    detailParams[@"spid"] = self.spid;
    detailParams[@"sid"] = self.sid;
    
    // 埋点检测 sptype
    [YBMAnalyticsCheckUtil checkSpTypeField:self.sptype andParams:detailParams andIsFromTrack:YES];
    
    NSMutableDictionary *instructionParams = [NSMutableDictionary dictionary];
    instructionParams[@"id"] = self.productID;
    
    NSMutableDictionary *bottomRecommedParams = [NSMutableDictionary dictionary];
    bottomRecommedParams[@"pageType"] = @(YBMAnalyticsPageTypeProductDetail);
    bottomRecommedParams[@"offset"] = @"1";
    bottomRecommedParams[@"limit"] = @"10";
    bottomRecommedParams[@"csuId"] = self.productID;
    bottomRecommedParams[@"recomSite"] = @"1";

    self.isHideHud = false;
    [self.view showSpinner];
    @weakify(self)
    BOOL isKA = [XYYUserData ShareUserData].isKa;
    NSInteger type = 0;
    if (isKA) {
        /// KA用户
        type = 1;
    } else {
        type = 0;
    }
    [self.viewModel requestDataWithDetailParams:detailParams instruction:instructionParams bottomRecommed:bottomRecommedParams orderFrom:type complete:^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
        @strongify(self)
        if (!self.isHideHud) {
            self.isHideHud = true;
            [self.view hideSpinner];
        }
        YBMLog(@"===ZB===:%@",response);
        if (success) {
            self.detailModel = response;
            [self updateControls];
            // 雪地埋点
//            [self snowAnalysisAction:self.detailModel];

            // QT埋点页面曝光
            [self QTTrackPageExposureWithModel:self.detailModel];
        } else {
            [self noProductMessage];
        }
    }];
    [self.viewModel requestShoppingCartNumberWithParams:@{} complete:^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
        @strongify(self)
        if (success) {
            [self updateShoppingCartNumber: response];
        }
    }];
}

/// 刷新控件
- (void)updateControls {
    self.productView.detailModel = self.detailModel;
    self.instructionView.detailModel = self.detailModel;
    
    // v620 埋点
    _detailModel.addBuyModel.sptype = self.sptype;
    _detailModel.addBuyModel.spid = self.spid;
    _detailModel.addBuyModel.sid = self.sid;
    _detailModel.addBuyModel.nsid = self.nsid;
    _detailModel.addBuyModel.sdata = self.sdata;
    _detailModel.addBuyModel.stepPriceStatus = _detailModel.detail.actPt.stepPriceStatus;
    _detailModel.addBuyModel.nearEffect = _detailModel.detail.rows.effectStr;
    _detailModel.addBuyModel.pid = _detailModel.detail.rows.pid.stringValue;
    _detailModel.addBuyModel.fob = _detailModel.detail.rows.fob;
    _detailModel.addBuyModel.operationId = self.operationId;
    _detailModel.addBuyModel.operationRank = self.operationRank;
    _detailModel.addBuyModel.rank = self.rank;
    if (self.isFromHome) {
        _detailModel.addBuyModel.page_id = self.page_id;
        _detailModel.addBuyModel.moduleType = @"7";
        _detailModel.addBuyModel.offset = [NSString stringWithFormat:@"%ld",self.feedIndex];
    }
    _detailModel.addBuyModel.detailM = _detailModel.detail;
    _detailModel.addBuyModel.keyword = self.keyword;
    _detailModel.addBuyModel.positionType = self.positionType;
    _detailModel.addBuyModel.positionTypeName = self.positionTypeName;
    if (self.detailModel) {
        self.productDetailTrackDict = [self getTrackCommonParams:self.detailModel];//埋点参数
    }
    self.productDetailMdDataDict = [self getMdDataDict];//埋点mddata字典
    self.addBuyView.addBuyModel = _detailModel.addBuyModel;
    // 判断详情页是否有“整单包邮”标签
    if (_detailModel.detail.rows.tags.orderFreeShippingTag) {
        self.addBuyView.freeShippingFlag = YES;
    } else {
        self.addBuyView.freeShippingFlag = NO;
    }
    
    /// 智鹿需求
    if (_detailModel.detail.rows.distanceEndTime.integerValue > 0) {
        _priorityBuyView.hidden = NO;
        _priorityBuyView.distanceEndTime = _detailModel.detail.rows.distanceEndTime.integerValue/1000;
        [_productView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(self.priorityBuyView.mas_bottom);
            make.leading.trailing.mas_equalTo(self.view);
            make.bottom.mas_equalTo(self.addBuyView.mas_top);
        }];
//        [_instructionView mas_remakeConstraints:^(MASConstraintMaker *make) {
//            make.top.mas_equalTo(self.priorityBuyView.mas_bottom);
//            make.leading.trailing.mas_equalTo(self.view);
//            make.bottom.mas_equalTo(self.addBuyView.mas_top);
//        }];
    } else {
        _priorityBuyView.hidden = YES;
        [_productView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.leading.trailing.mas_equalTo(self.view);
            make.bottom.mas_equalTo(self.addBuyView.mas_top);
        }];
//        [_instructionView mas_remakeConstraints:^(MASConstraintMaker *make) {
//            make.top.leading.trailing.mas_equalTo(self.view);
//            make.bottom.mas_equalTo(self.addBuyView.mas_top);
//        }];
    }
    
    /// 秒杀温馨提示
    if (_detailModel.detail.rows.activityType.integerValue == 99 || (_detailModel.detail.rows.nearEffectiveFlag.integerValue == 2 || _detailModel.detail.rows.nearEffectiveFlag.integerValue == 1)) {
        
        CGFloat h = 45.f;
        if (_detailModel.detail.rows.nearEffectiveFlag.integerValue == 2 || _detailModel.detail.rows.nearEffectiveFlag.integerValue == 1) h = 30.f;
        
        [self.tipsView removeFromSuperview];
        self.tipsView = nil;
        [self.view addSubview:self.tipsView];
        [self.tipsView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.trailing.offset(0);
            make.height.offset(h);
            make.bottom.mas_equalTo(self.addBuyView.mas_top).offset(0);
        }];
        
        YBMProductTipsViewModel *model = [[YBMProductTipsViewModel alloc] init];
        model.activityType = _detailModel.detail.rows.activityType;
        model.nearEffectiveFlag = _detailModel.detail.rows.nearEffectiveFlag;
        self.tipsView.model = model;
        
        /// upload layout
        [_productView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.leading.trailing.mas_equalTo(self.view);
            make.bottom.mas_equalTo(self.tipsView.mas_top);
        }];
        
//        [_instructionView mas_remakeConstraints:^(MASConstraintMaker *make) {
//            make.top.leading.trailing.mas_equalTo(self.view);
//            make.bottom.mas_equalTo(self.tipsView.mas_top);
//        }];
    }
}

/// 无商品信息
- (void)noProductMessage {
    self.defaultView.hidden = NO;
    [self.defaultView startTimer];
    self.addBuyView.hidden = YES;
    self.productView.hidden = YES;
    self.instructionView.hidden = YES;
    self.navigationBarView.hidden = YES;
    self.moreButton.hidden = YES;
    self.collectButton.hidden = YES;
    self.tipsView.hidden = YES;
}

- (void)updateShoppingCartNumber:(NSNumber *)num {
    self.addBuyView.shopButton.badgeValue = [NSString stringWithFormat:@"%@",num];
}

#pragma mark - 按钮点击事件
/// 点击导航上客服按钮
- (void)serviceButtonAnction:(UIButton *)sender {
    [self ybm_productAddBuyServiceAction: self.addBuyView];
}

/// 点击收藏
- (void)collectButtonAction{
    @weakify(self)
    if (_detailModel.ID) {
        if (self.detailModel.favoriteStatus.integerValue == 1) {
            NSString *message;
            if (_detailModel.businessType.integerValue == 1) {
                message = @"取消收藏后，该商品有货将不会继续通知您，确认取消收藏吗?";
            } else if (_detailModel.businessType.integerValue == 2) {
                message = @"取消收藏后，该商品降价将不会继续通知您，确认取消收藏吗?";
            }
            if (message) {
                YBMAlertView *alert = [[YBMAlertView alloc] initWithTitle:@"提示" message:message cancelButtonTitle:@"取消" otherButtonTitle:@"确定"];
                alert.completionBlock = ^(NSInteger index) {
                    if (index == 1) {
                        [self.viewModel requestCancelAttentionWithParams:@{@"skuId":self.detailModel.ID} complete:^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
                            @strongify(self)
                            if (success) {
                                [self cancelCollect];
                                self.collectButton.selected = NO;
                            }
                        }];
                    }
                };
                [alert show];
            } else {
                [self.viewModel requestCancelAttentionWithParams:@{@"skuId":_detailModel.ID} complete:^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
                    @strongify(self)
                    if (success) {
                        [self cancelCollect];
                        self.collectButton.selected = NO;
                    }
                }];
            }
        } else {
            [self.viewModel requestAttentionWithParams:@{@"skuId":_detailModel.ID} complete:^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
                @strongify(self)
                if (success) {
                    [self collect];
                    MBShowSuccessMessage(@"收藏成功");
                    self.collectButton.selected = YES;
                }
            }];
        }
    }
}

- (void)collect {
//    self.collectButton.selected = YES;
    self.detailModel.favoriteStatus = @1;
    [self changeAttention];
}

- (void)cancelCollect {
//    self.collectButton.selected = NO;
    self.detailModel.favoriteStatus = @2;
    [self changeAttention];
}

/// 更多按钮点击
- (void)moreButtonAction:(UIButton *)button {
    
    //显示PopView
    //1.数据
    XHPopMenuItem *item1 = [[XHPopMenuItem alloc] initWithImage:[UIImage imageNamed:@"hlye_icon"] title:@"首页" itemType:XHPopMenuItemTypeHome];
    XHPopMenuItem *item2 = [[XHPopMenuItem alloc] initWithImage:[UIImage imageNamed:@"hlsousuo_icons"] title:@"搜索" itemType:XHPopMenuItemTypeSearch];
    XHPopMenuItem *item3 = [[XHPopMenuItem alloc] initWithImage:[UIImage imageNamed:@"hlxiaoxihezi_icons"] title:@"消息" itemType:XHPopMenuItemTypeMessage];
    XHPopMenuItem *item4 = [[XHPopMenuItem alloc] initWithImage:[UIImage imageNamed:@"hlwode_icons"] title:@"我的" itemType:XHPopMenuItemTypeMine];
    
    // 刷新收藏状态
    NSString* collectTitle = @"已收藏";
    UIImage *collectImage = [UIImage imageNamed:@"collect_menu_icon"];
    if (_detailModel.favoriteStatus) {
        NSNumber *favoriteStatus = _detailModel.favoriteStatus;
        if (favoriteStatus.intValue != 1) {
            collectTitle = @"收藏";
            collectImage = [UIImage imageNamed:@"collect_menu_icon"];
        }
    }
    
    XHPopMenuItem *item5 = [[XHPopMenuItem alloc] initWithImage:collectImage title:collectTitle itemType:XHPopMenuItemTypeCollect];
    
    //2.创建PopMenu
    NSArray *itemArr = [NSArray array];
    // 赠品时隐藏收藏功能
    if (self.detailModel.detail.rows.isGive.intValue == 1 || self.detailModel.detail.rows.isGive.intValue == 4) {
        itemArr = @[item1, item2, item3, item4];
    } else {
        itemArr = @[item1, item2, item3, item4, item5];
    }
    
    XHPopMenu * menu = [[XHPopMenu alloc] initWithMenus:itemArr mid:NO];
    //监听点击事件
    @weakify(self)
    menu.popMenuDidSlectedCompled = ^(NSInteger index,XHPopMenuItem *item){
        @strongify(self)
        switch (item.itemType) {
            case XHPopMenuItemTypeHome:
            {
                // 跳转到首页
                [[NSNotificationCenter defaultCenter] postNotificationName:@"pushHome" object:nil];
                [self.navigationController popToRootViewControllerAnimated:YES];
            }
                break;
            case XHPopMenuItemTypeSearch:
            {
                //跳转到搜索界面XYYNewSearchVC
                // 雪地埋点 行为-详情页-搜索框
                [YBMAnalyticsUtil track:kaction_CommodityDetails_Search];
                UIViewController *searchvc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:@"ybmpage://V2SearchVC"];
                [self.navigationController pushViewController:searchvc animated:YES];
            }
                break;
            case XHPopMenuItemTypeMessage:
            {
                NSString *head = API_Interface(@"/static/xyyvue/dist");
                NSString *str = [NSString stringWithFormat:@"%@/#messagecenter",head];
                NSString *strRouter = [NSString stringWithFormat:@"ybmpage://commonh5activity?umkey=1111hd1&url=%@?merchantId=%@&ybm_title=消息中心&head_menu=0",str,[XYYUserData ShareUserData].merchantId];
                UIViewController *messageVC = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:strRouter];
                //调转到消息中心
                if (messageVC != nil) {
                    [self.navigationController pushViewController:messageVC animated:YES];
                }
            }
                break;
            case XHPopMenuItemTypeMine:
            {
                //调转到我的界面
                [[NSNotificationCenter defaultCenter] postNotificationName:@"pushMe" object:nil];
                // 返回根控制器
                [self.navigationController popToRootViewControllerAnimated:YES];
            }
                break;
            case XHPopMenuItemTypeCollect:
            {
                [self collectButtonAction];
                /*
                XYYShareView *shareView = [[XYYShareView alloc] initWithFrame:CGRectMake(0, 0, XYYScreenW, XYYScreenH)];
                
                shareView.productID = [NSString stringWithFormat:@"%@",self.detailModel.ID];
                [shareView show];
                [shareView getCode];
                 */
            }
                break;
            default:
                break;
        }
    };
    if (isIPhoneXSeries) {
        [menu showMenuAtPoint:CGPointMake(0, 24)];
    } else {
        [menu showMenuAtPoint:CGPointMake(0, 0)];
    }
}

#pragma mark - 分享

- (void)shareActionFailed{
    UIAlertController* alert = [UIAlertController alertControllerWithTitle:nil message:@"商品详情获取失败" preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction* cancel = [UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleDefault handler:nil];
    [alert addAction:cancel];
    [self presentViewController:alert animated:YES completion:nil];
}

- (void)shareProductDetailAction:(UIButton*)button {
    if (self.detailModel == nil || self.detailModel.detail == nil || self.productID == nil ){
        [self shareActionFailed];
    } else {
        NSMutableDictionary * param = [NSMutableDictionary dictionary];
        param[@"skuId"] = self.productID;
        if(self.detailModel.detail.actPt && self.detailModel.detail.actPt.marketingId){
            param[@"actId"] = self.detailModel.detail.actPt.marketingId.stringValue;
        }else if(self.detailModel.detail.actPgby && self.detailModel.detail.actPgby.marketingId){
            param[@"actId"] = self.detailModel.detail.actPgby.marketingId.stringValue;
        }  else{
            param[@"actId"] = @"0";
        }
        
        [XYYWindow showSpinner];
        @weakify(self)
        YBMAssembleShareRequest *request = [YBMAssembleShareRequest new];
        request.addParams(param).request(^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
            @strongify(self)
            [XYYWindow hideSpinner];
            if(success){
                YBMProductAssembleShareRootModel* shareModel = response;
                NSString * title = shareModel.data.title;// [NSString stringWithFormat:@"%@ %@", self.detailModel.detail.rows.showName, self.detailModel.detail.rows.spec];
                NSString *merchantId = [[XYYUserData ShareUserData] merchantId];
                NSString *links = [NSString stringWithFormat:@"%@&merchantId=%@", shareModel.data.shareUrl, merchantId];
                NSString *imageURLString = [[XYYSmallImagUrl stringByAppendingString:shareModel.data.imageUrl] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
                NSData *logoData = [NSData dataWithContentsOfURL:[NSURL URLWithString:imageURLString]];

                XYYWechatShareModel *model = [[XYYWechatShareModel alloc] init];
                model.logoImage = [UIImage imageWithData:logoData];
                model.pic = @"shop_detail_logoNor";
                model.title = title;
                model.title_pyq = title;
                model.links = links;
                model.iconUrl = imageURLString;
                model.desc = @"刚刚在药帮忙看到一个不错的商品，赶快来看看吧";
                if ([self.detailModel.detail.isWholesale isEqualToNumber:@1]){
                    model.desc = @"单品包邮，快来抢购吧";
                }
                
                [YBMWechatShareView showSheetViewWithModel:self.detailModel shareModel:model];
                NSMutableDictionary *mutDic = [NSMutableDictionary new];
                mutDic[@"skuId"] =  self.productID ? : @"";
                mutDic[@"sid"] = self.sid;
                mutDic[@"spid"] = self.spid;
                mutDic[@"sptype"] = self.sptype;
                mutDic[@"merchantId"] = [[XYYUserData ShareUserData] merchantId];
                [YBMAnalyticsUtil track:share_Freeproduct_details properties:mutDic];

            } else{
//                [self shareActionFailed];
            }
        });
    }
}

#pragma mark - 顶部商品说明书切换逻辑

/// YBMProductNavigationBarViewDelegate
/// @param barView 切换视图
/// @param index 0:商品 1:说明书
- (void)ybm_productNavigationBarView:(YBMProductNavigationBarView *)barView didSelectIndex:(NSInteger)index {
    [self switchPageWithIndex:index];
}

- (void)ybm_productDetailView:(YBMProductDetailView *)detailView swithPageToIndex:(NSInteger)index {
    [self.navigationBarView switchButtonWithIndex:index];
}

- (void)ybm_switchProductDetailView:(YBMProductDetailView *)detailView {
//    [self switchPageWithIndex:1];
//    [self.navigationBarView switchButtonWithIndex:1];
}

//- (void)ybm_switchProductInstructionView:(YBMProductInstructionView *)instructionView {
//    [self switchPageWithIndex:0];
//    [self.navigationBarView switchButtonWithIndex:0];
//}

/// 顶部商品说明书切换方法
/// @param index 0:商品 1:说明书
- (void)switchPageWithIndex:(NSInteger)index {
    self.productView.hidden = NO;
    
    if (index == 0) {
        [self.productView setContentOffsetZero];
    } else {
        [self.productView scrollToInstructionCell];
    }
}

#pragma mark - 通知

- (void)setupNotification {
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillShow:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillHide:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];
    // 监听有货提醒
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(collect)
                                                 name:@"changeAttentionButtonToHasGoodsAttention"
                                               object:nil];
    // 监听资质状态变更
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(licenseStatusChange)
                                                 name:@"YBMlicenseStatusChange"
                                               object:nil];
    // 加购当前商品后更新底部购物车显示数量
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(upateShopNum:)
                                                 name:@"setUpShoppingNumber"
                                               object:nil];
}

- (void)keyboardWillShow:(NSNotification *)notification {
    BOOL flag = !(self.detailModel.addBuyModel.isAssemble.integerValue == 1);
    CGFloat offsetY = flag ? 0 : 50;
    
    NSDictionary *userInfo = [notification userInfo];
    NSValue *aValue = [userInfo objectForKey:UIKeyboardFrameEndUserInfoKey];
    CGRect keyboardRect = [aValue CGRectValue];
    CGFloat fieldY = self.addBuyView.frame.origin.y+self.addBuyView.frame.size.height;
    if (fieldY > keyboardRect.origin.y) {
        [self.addBuyView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.mas_equalTo(self.view).offset(-XYYScreenH+keyboardRect.origin.y + offsetY);
        }];
        [UIView animateWithDuration:0.25 animations:^{
            [self.view layoutIfNeeded];
        }];
    }
}

- (void)keyboardWillHide:(NSNotification *)notification {
    [self.addBuyView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.view).offset(-kBottomSafeHeight);
    }];
    [UIView animateWithDuration:0.25 animations:^{
        [self.view layoutIfNeeded];
    }];
}

- (void)licenseStatusChange {
    [self setupData];
}

- (void)upateShopNum:(NSNotification *)notificaiton {
    self.addBuyView.shopButton.badgeValue = notificaiton.object;
}

/// 发送收藏操作通知给首页
- (void)changeAttention {
//    [[NSNotificationCenter defaultCenter] postNotificationName:@"changeProductNumToH5" object:@"1"];
}

#pragma mark - 设置子视图

- (void)setupControls {
    [self.view addSubview:self.productView];
//    [self.view addSubview:self.instructionView];
    [self.view addSubview:self.defaultView];
    [self.view addSubview:self.priorityBuyView];
    [self.view addSubview:self.addBuyView];
    
    [_productView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.leading.trailing.mas_equalTo(self.view);
        make.bottom.mas_equalTo(self.view).offset(50);
    }];
//    [_instructionView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.top.leading.trailing.mas_equalTo(self.view);
//        make.bottom.mas_equalTo(self.view).offset(50);
//    }];
    [self.defaultView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.view);
    }];
    [self.priorityBuyView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.leading.trailing.mas_equalTo(self.view);
        make.height.mas_equalTo(44);
    }];
    [self.addBuyView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.mas_equalTo(self.view);
        make.bottom.mas_equalTo(self.view).offset(-kBottomSafeHeight);
        make.height.mas_equalTo(50);
    }];
    
    @weakify(self);
    self.addBuyView.animationBlock = ^{
        @strongify(self);
        self.productView.firstImage.hidden = NO;
        [[PurchaseCarAnimationTool shareTool]startAnimationandView:self.productView.firstImage andRect:CGRectMake(XYYScreenW/2, kNavBarHeight, XYYScreenW, 225) andFinisnRect:CGPointMake((iPhone4||iPhone5)? 90:160, ScreenHeight-49) andFinishBlock:^(BOOL finisn){
            self.productView.firstImage.hidden = YES;
        }];
    };
    self.priorityBuyView.updateDataActionBlock = ^{
        @strongify(self);
        [self setupData];
    };
}

- (void)setupNavigationBar {
    self.navigationItem.titleView = self.navigationBarView;
    [_navigationBarView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(44);
        make.width.mas_equalTo(130);
    }];
    // 客服按钮
    _serviceButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [_serviceButton setImage:[UIImage imageNamed:@"service"] forState:UIControlStateNormal];
    [_serviceButton addTarget:self action:@selector(serviceButtonAnction:) forControlEvents:UIControlEventTouchUpInside];
    _serviceButton.hidden = NO;
    // 更多按钮
    _moreButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [_moreButton setImage:[UIImage imageNamed:@"product_more"] forState:UIControlStateNormal];
    [_moreButton addTarget:self action:@selector(moreButtonAction:) forControlEvents:UIControlEventTouchUpInside];
    // 分享按钮
    _collectButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [_collectButton setImage:[UIImage imageNamed:@"share_icon_black"] forState:UIControlStateNormal];
    [_collectButton setImage:[UIImage imageNamed:@"share_icon_black"] forState:UIControlStateSelected];
    [_collectButton addTarget:self action:@selector(shareProductDetailAction:) forControlEvents:UIControlEventTouchUpInside];
    
    UIBarButtonItem *moreItem = [[UIBarButtonItem alloc] initWithCustomView:_moreButton];
    UIBarButtonItem *collectItem = [[UIBarButtonItem alloc] initWithCustomView:_collectButton];
    UIBarButtonItem *serviceItem = [[UIBarButtonItem alloc] initWithCustomView:_serviceButton];
    
    self.navigationItem.rightBarButtonItems = @[moreItem,collectItem,serviceItem];
    [_moreButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(22);
    }];
    [_collectButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.mas_equalTo(22);
    }];
    [_serviceButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(22);
        make.height.mas_equalTo(23);
    }];
}

#pragma - mark YBMProductAddBuyViewDelegate:底部加购

/// 跳店铺
- (void)ybm_productToShopAction:(YBMProductAddBuyView *)addBuyView{ //TODO: 确认为什么原来要用webview做一次中转
    NSString *string = [NSString stringWithFormat:@"%@", _detailModel.detail.shopInfo.shopUrl];
    UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:string];
    [AppConfigue pushVC:vc];
}

/// 分享
- (void)ybm_productShareAction:(YBMProductAddBuyView *)addBuyView{
    @weakify(self)
    [self.viewModel requestShareInfo:@{@"skuId": self.detailModel.ID ?:@"", @"actId": self.detailModel.detail.actPt.marketingId ?:@""} complete:^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
        @strongify(self)
        if(success && [response isKindOfClass:[YBMProductAssembleShareRootModel class]]){
            YBMProductAssembleShareRootModel* res = response;
            
            XYYWechatShareModel *model = [[XYYWechatShareModel alloc] init];
//            NSString* imageURLString = [NSString stringWithFormat:@"http://upload.ybm100.com/ybm/product/min/%@", res.data.imageUrl];
            NSString*  imageURLString = [[XYYSmallImagUrl stringByAppendingString:res.data.imageUrl] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
            NSData *logoData = [NSData dataWithContentsOfURL:[NSURL URLWithString:imageURLString]];
            model.logoImage = [UIImage imageWithData:logoData];
            model.title = res.data.title;
            model.title_pyq = res.data.title;
            model.links = res.data.shareUrl;
            model.desc = @"成团即包邮，快来参团吧";
            
            [YBMWechatShareView showSheetViewWithModel:self.detailModel shareModel:model];
        } else{
            UIAlertController* alert = [UIAlertController alertControllerWithTitle:nil message:@"商品分享信息获取失败" preferredStyle:UIAlertControllerStyleAlert];
            UIAlertAction* cancel = [UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleDefault handler:nil];
            [alert addAction:cancel];
            [self presentViewController:alert animated:YES completion:nil];
        }
    }];
}

/// 加常购、取消加常购
- (void)ybm_productAddBuyAction:(YBMProductAddBuyView *)addBuyView {
    @weakify(self)
    if (self.detailModel.addBuyModel.listStatus.intValue == 1) {
        // 已加入清单，执行取消清单
        [self.viewModel requestCancelOrderShellNoWithParams:@{@"skuId": self.detailModel.ID ?:@""} complete:^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
            @strongify(self)
            if (success) {
                self.detailModel.addBuyModel.listStatus = @2;
                [addBuyView.addOftenBuyButton setImage:[UIImage imageNamed:@"shangpinxiangqing_qingdan_btn_n"] forState:UIControlStateNormal];
                [addBuyView.addOftenBuyButton setTitle:@"加常购" forState:UIControlStateNormal];
                [addBuyView.addOftenBuyButton setTitleColor:[UIColor colorWithHexString:@"#292933"] forState:UIControlStateNormal];
                // 刷新常购清单列表数据
                [[NSNotificationCenter defaultCenter] postNotificationName:@"refreshOftenList" object:nil];
            }
        }];
    } else {
        [self.viewModel requestAddOrderShellNoWithParams:@{@"skuId": self.detailModel.ID ?:@""} complete:^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
            @strongify(self)
            if (success) {
                self.detailModel.addBuyModel.listStatus = @1;
                [addBuyView.addOftenBuyButton setImage:[UIImage imageNamed:@"shangpinxiangqing_qingdan_btn_s"] forState:UIControlStateNormal];
                [addBuyView.addOftenBuyButton setTitle:@"已加入" forState:UIControlStateNormal];
                [addBuyView.addOftenBuyButton setTitleColor:[UIColor colorWithHexString:@"#00B377"] forState:UIControlStateNormal];
                // 刷新常购清单列表数据
                [[NSNotificationCenter defaultCenter] postNotificationName:@"refreshOftenList" object:nil];
            }
        }];
    }
}

/// 客服
- (void)ybm_productAddBuyServiceAction:(YBMProductAddBuyView *)addBuyView {
    
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"isThirdCompany"] = self.detailModel.addBuyModel.isThirdCompany;
    [self.viewModel requestIMURLWithParams:params complete:^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
        if (success && [response isKindOfClass:[NSString class]]) {
            NSString *url = [NSString stringWithFormat:@"%@&pType=1&userid=%@&sc=%@&portalType=%@",response,[XYYUserData ShareUserData].merchantId,@"1003",@"1"];
            if (self.detailModel.addBuyModel.isThirdCompany.integerValue == 1) {
                NSString *string = [NSString stringWithFormat:@"%@&pType=1&userid=%@&merchantCode=%@&merchantName=%@&pop=1&ws=1&portalType=1&sc=1003",response,[XYYUserData ShareUserData].merchantId,self.detailModel.popShopInfoModel.orgId,self.detailModel.popShopInfoModel.orgName];
                NSMutableCharacterSet * set = [NSCharacterSet URLQueryAllowedCharacterSet].mutableCopy;
                [set addCharactersInString:@"#"];
                url = [string stringByAddingPercentEncodingWithAllowedCharacters:set];
            }
            XYYWebViewVC *vc = [[XYYWebViewVC alloc] init];
            vc.urlSting = url;
            [vc setRightButtonEnabled:YES];
            [self.navigationController pushViewController:vc animated:YES];        }
    }];
}

/// 到货提醒
- (void)ybm_productAddBuySubscibeAction:(YBMProductAddBuyView *)addBuyView {
    [self.view showSpinner:@"加载中..."];
    @weakify(self)
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    dic[@"businessType"] = self.detailModel.businessType;
    dic[@"skuId"] = self.detailModel.ID;
    dic[@"currentPrice"] = @"";
    dic[@"expectPrice"] = @"";
    [self.viewModel requestAttentionWithParams:dic complete:^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
        @strongify(self)
        [self.view hideSpinner];
        if (success) {
            [self.addBuyView.baseStatusButton setTitle:@"已订阅" forState:UIControlStateNormal];
            [self.addBuyView.baseStatusButton setBackgroundColor:[UIColor colorWithHexString:@"#A9AEB7"]];
            [self.addBuyView.baseStatusButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
            self.addBuyView.baseStatusButton.enabled = NO;
            self.detailModel.businessType = @1;
            [self collect];
            /// 之后确认
            [[NSNotificationCenter defaultCenter] postNotificationName:@"exchangeArrivalNotice" object:self.detailModel.ID];
            YBMAlertView *alert = [[YBMAlertView alloc] initWithTitle:@"订阅成功" message:@"若该商品在45天内到货,药帮忙会提醒您!同时您可以在我的收藏夹查看您订阅过的所有商品" cancelButtonTitle:nil otherButtonTitle:@"我知道啦"];
            [alert show];
        }
    }];
}

#pragma mark - 埋点

- (NSDictionary *)registerPageProperties {
    NSString *page_id = [YBMAnalyticsPages pageIdFromClass:self];
    NSMutableDictionary *mutD = [NSMutableDictionary dictionary];
    mutD[@"$title"] = [YBMAnalyticsPages returnTitleNameFrom:NSStringFromClass([self class])];
    mutD[@"page_id"] = page_id;
    return mutD;
}

-(void)initEntrance {
    self.searchSortStrategyCode = [AnalysysAgent getSuperProperty:kSearch_sort_strategy_id];
    if (self.activityEntrance && self.activityEntrance.length) {
        self.activityEntrance = [self.activityEntrance stringByRemovingPercentEncoding];
    }
    NSArray *controllers = self.navigationController.viewControllers;
    if (self.jgEntrance && self.jgEntrance.length) {
        self.jgEntrance = [self.jgEntrance stringByRemovingPercentEncoding];
        self.entrance = [NSString stringWithFormat:@"%@-商品详情页",self.jgEntrance];
    }else{
        if (controllers.count > 1) {
            UIViewController *frontVc = [self.navigationController.viewControllers objectAtIndex:self.navigationController.viewControllers.count-2];
            if ([frontVc respondsToSelector:@selector(entrance)]) {
                NSString *entrance = [frontVc valueForKey:@"entrance"];
                if (entrance && entrance.length) {
                    self.entrance = [NSString stringWithFormat:@"%@-商品详情页",entrance];
                }
            }
        }
    }
}

//获取详情页加购埋点参数
- (NSMutableDictionary *)getMdDataDict{
     NSMutableDictionary *mdDict = [NSMutableDictionary dictionary];
     mdDict[@"key_word"] = self.keyword;
     mdDict[@"list_position_typename"] = self.positionTypeName;
     if (self.positionType) {
        mdDict[@"list_position_type"] = [NSString stringWithFormat:@"%@",self.positionType];
     }
     mdDict[@"search_sort_strategy_id"] = self.searchSortStrategyCode;
     if(![Utility isEmpty:self.operationId]){//运营位
        mdDict[@"operation_id"] = self.operationId;
        mdDict[@"operation_rank"] = @(self.operationRank);
     }
  
     mdDict[@"rank"] = @(self.rank);;
     mdDict[@"result_cnt"] = @(self.totalCount);//列表总条数
     mdDict[@"page_no"] = @(self.pageNo);//当前页码
     mdDict[@"page_size"] = @(self.pageSize);//页面条数
     mdDict[@"total_page"] = @(self.totalPage);//总页数
     mdDict[@"sptype"] = self.sptype;//来源类型
     mdDict[@"sid"] = self.sid;//追踪ID
     mdDict[@"jgspid"] = self.jgspid;//来源ID
    //   0"-其他；"1"-列表页直接加购提单；"2"-详情页加购提单；"3"-购物车页面加购；"4"-订单再次购买加购
     mdDict[@"direct"] = @"2";//列表和列表弹窗都为详情页加购提单
     NSString *sessionId = [[AnalysysAgent getPresetProperties] objectForKey:@"$session_id"];
     mdDict[@"$session_id"] = sessionId;
     mdDict[@"direct"] = @"2";
     mdDict[@"page_type"] = self.pageType;
     mdDict[@"page_id"] = self.pageId;
     mdDict[@"page_name"] = self.pageName;
     mdDict[@"component_position"] = self.componentPosition;
     mdDict[@"component_name"] = self.componentName;
     mdDict[@"component_title"] = self.componentTitle;
     //商品相关
     if(self.detailModel.detail.rows.productActivityType){
       mdDict[@"product_activity_type"] = [NSString stringWithFormat:@"%@",self.detailModel.detail.rows.productActivityType];//商品营销活动类型
     }
     if (self.detailModel.detail.actPt && self.detailModel.detail.actPt.assemblePrice){//拼团 阶梯价和无阶梯价
         mdDict[@"product_price"] = self.detailModel.detail.actPt.assemblePrice;
     }else if (self.detailModel.detail.rows.actPgby) {
         mdDict[@"product_price"] = self.detailModel.detail.rows.actPgby.assemblePrice;//批购包邮
     }else{
         mdDict[@"product_price"] = self.detailModel.detail.rows.fob;//普通品
     }
     if(self.detailModel.detail.rows.productType){
         mdDict[@"product_type"] = [NSString stringWithFormat:@"%@",self.detailModel.detail.rows.productType];//商品类型
     }
     mdDict[@"product_shop_name"] =  self.detailModel.detail.shopInfo.shopName;//商品店铺名称
     mdDict[@"product_shop_code"] =  self.detailModel.detail.rows.shopCode;//商品店铺编码
     mdDict[@"product_name"] = self.detailModel.detail.rows.showName;//商品名称
     mdDict[@"product_id"] = self.detailModel.detail.rows.ID;//商品ID
     mdDict[@"product_first"] = [NSString stringWithFormat:@"%@",self.detailModel.detail.rows.categoryId];//商品一级分类
     return mdDict;
}


//埋点上报参数
- (NSMutableDictionary*)getTrackCommonParams:(YBMProductDetailModel *)detail{
        NSMutableDictionary *mutD = [NSMutableDictionary dictionary];
        mutD[@"jgspid"] = self.jgspid;
        mutD[@"direct"] = @"2";//0"-其他；"1"-列表页直接加购提单；"2"-详情页加购提单；"3"-购物车页面加购；"4"-订单再次购买加购
        if(![Utility isEmpty:self.operationId]){//运营位
            mutD[@"operation_id"] = self.operationId;
            mutD[@"operation_rank"] = @(self.operationRank);
        }
        mutD[@"rank"] = @(self.rank);
        if(self.positionType){
             mutD[@"list_position_type"] = [NSString stringWithFormat:@"%@",self.positionType]; //搜索坑位类型
        }
        if(self.positionTypeName){
            mutD[@"list_position_typename"] = self.positionTypeName;////搜索坑位类型名称
        }
        if(detail.detail.rows.productType){
            mutD[@"product_type"] = [NSString stringWithFormat:@"%@",detail.detail.rows.productType];//商品类型
        }
        if(detail.detail.rows.productActivityType){
            mutD[@"product_activity_type"] = [NSString stringWithFormat:@"%@",detail.detail.rows.productActivityType];//商品营销活动类型
        }
       
        if ([self getControlPriceModelAssemble]) {
            /**1.商详：拼团包括阶梯价和无阶梯价
             无阶梯价拼团: actPt.assemblePrice
             批购：actPgby.assemblePrice
             阶梯价：actPt.assemblePrice
             其他：fob

             2. 列表
             无阶梯价拼团: actPt.assemblePrice
             批购：actPgby.assemblePrice
             阶梯价：actPt.assemblePrice
             秒杀：actSk.skPrice
             其他：fob
             */
            //商品价格
//            if (detail.detail.actPt && detail.detail.actPt.assemblePrice){//拼团 阶梯价和无阶梯价
//                mutD[@"product_price"] = detail.detail.actPt.assemblePrice;
//            }else if (detail.detail.rows.actPgby) {
//                mutD[@"product_price"] = detail.detail.rows.actPgby.assemblePrice;//批购包邮
//            }else{
//                mutD[@"product_price"] = detail.detail.rows.fob;//普通品
//            }
            //             if (detail.detail.actSk && detail.detail.actSk.skPrice) {
            //                 mutD[@"product_price"] = detail.detail.actSk.skPrice;//秒杀
            //             }else if (detail.detail.actPt && detail.detail.actPt.assemblePrice){//拼团
            //                 mutD[@"product_price"] = detail.detail.actPt.assemblePrice;
            //             }else if (detail.detail.rows.actPgby) {
            //                 mutD[@"product_price"] = detail.detail.rows.actPgby.assemblePrice;//批购包邮
            //             }else{
            //                 mutD[@"product_price"] = detail.detail.rows.fob;//普通品
            //             }
        }
        
//        NSNumber *product_price = @(0);
//        if (detail.detail.actPt && detail.detail.actPt.assemblePrice){//拼团 阶梯价和无阶梯价
//            product_price = detail.detail.actPt.assemblePrice;
//        }else if (detail.detail.rows.actPgby) {
//            product_price = detail.detail.rows.actPgby.assemblePrice;//批购包邮
//        }else{
//            product_price = detail.detail.rows.fob;//普通品
//        }
//        if ([product_price compare:@0] == NSOrderedDescending) {//大于0 排除售罄、下架、没有权限购买等等 价格不上报
//            mutD[@"product_price"] = product_price;
//        }
    
         //商品价格
         if (detail.detail.actPt && detail.detail.actPt.assemblePrice){//拼团 阶梯价和无阶梯价
            mutD[@"product_price"] = detail.detail.actPt.assemblePrice;
         }else if (detail.detail.rows.actPgby) {
            mutD[@"product_price"] = detail.detail.rows.actPgby.assemblePrice;//批购包邮
         }else{
            mutD[@"product_price"] = detail.detail.rows.fob;//普通品
         }
         mutD[@"key_word"] = self.keyword;//搜索关键字
         mutD[@"sid"] = self.sid;//sid追踪ID
         mutD[@"sptype"] = self.sptype;//来源类型
         mutD[@"key_word"] = self.keyword;//搜索关键字
         mutD[@"search_sort_strategy_id"] = self.searchSortStrategyCode;
         mutD[@"product_shop_name"] =  detail.detail.shopInfo.shopName;//商品店铺名称
         mutD[@"product_shop_code"] =  detail.detail.rows.shopCode;//商品店铺编码
         mutD[@"product_name"] = detail.detail.rows.showName;//商品名称
         mutD[@"product_id"] = detail.detail.rows.ID;//商品ID
         mutD[@"product_first"] = [NSString stringWithFormat:@"%@",detail.detail.rows.categoryId];//商品一级分类
         mutD[@"result_cnt"] = @(self.totalCount);//列表总条数
         mutD[@"page_no"] = @(self.pageNo);//当前页码
         mutD[@"page_size"] = @(self.pageSize);//页面条数
         mutD[@"total_page"] = @(self.totalPage);//总页数
        //直睿携带埋点
         mutD[@"pageType"] = self.pageType;;
         mutD[@"pageId"] = self.pageId;
         mutD[@"pageName"] = self.pageName;
         mutD[@"componentPosition"] = self.componentPosition;
         mutD[@"componentName"] = self.componentName;
         mutD[@"componentTitle"] = self.componentTitle;
         return  mutD;
}

//判断当前商品是否有购买权限
- (BOOL)getControlPriceModelAssemble{
    NSArray *detailDataArray = self.detailModel.detailDataArray;
    YBMControlPriceModel *controlPriceModel;
    BOOL isPurchase = YES;
    for (id model in detailDataArray) {
        if ([model isKindOfClass:[YBMControlPriceModel class]]) {
            YBMControlPriceModel *controlPriceModel = model;
            if (!controlPriceModel.isAssemble) {
                isPurchase = NO;
            }
            break;
        }
        if ([model isKindOfClass:[YBMProductPriceModel class]]) {
            YBMProductPriceModel *productPriceModel = model;
            if (!productPriceModel.isPurchase) {
                isPurchase = NO;
            }
            break;
        }
    }
    return isPurchase;
}

/// 雪地埋点
- (void)snowAnalysisAction:(YBMProductDetailModel *)detail {
    // 雪地埋点  行为- 商详打开事件
    if (detail.detail.rows.ID.integerValue > 0 && detail.instructions.count > 0) {
        // TODO -v620
        // 说明商详的请求已经全部回来 确保打开事件只上报一次
        NSMutableDictionary *detailsDic = [NSMutableDictionary dictionary];
        detailsDic[@"source"] = self.source;
        detailsDic[@"index"] = self.index;
        [detailsDic setValue:self.sptype forKey:@"sptype"];
        [detailsDic setValue:self.spid forKey:@"spid"];
        [detailsDic setValue:self.sid forKey:@"sid"];
        [detailsDic setValue:detail.detail.rows.ID forKey:@"commodityId"];
        [detailsDic setValue:@"2" forKey:@"real"];
        [YBMAnalyticsUtil track:kPage_CommodityDetails_o properties:detailsDic];
        
        // 埋点检测 sptype
        [YBMAnalyticsCheckUtil checkSpTypeField:self.sptype andParams:detailsDic andIsFromTrack:YES];
        
        /// 埋点二期
        NSMutableDictionary *details2Dic = [NSMutableDictionary dictionary];
        details2Dic[@"terminalType"] = analytics_terminal_type;
        details2Dic[@"nsid"] = self.nsid;
        details2Dic[@"sdata"] = self.sdata;
        details2Dic[@"productId"] = detail.detail.rows.ID;
        details2Dic[@"productName"] = detail.detail.rows.showName;
        details2Dic[@"productCode"] = detail.detail.rows.barcode.length > 0 ?detail.detail.rows.barcode:@"";
        details2Dic[@"real"] = @"2";
        [YBMAnalyticsUtil track:kOrder_detail properties:details2Dic];

    }
    
    //TODO v640 为你推荐数据打开
    if (detail.recommended.rows.count > 0) {
        NSMutableDictionary *recommendDic = [NSMutableDictionary dictionary];
        recommendDic[@"source"] = self.source;
        recommendDic[@"index"] = self.index;
        [recommendDic setValue:detail.recommended.sptype forKey:@"sptype"];
        [recommendDic setValue:detail.recommended.spid forKey:@"spid"];
        [recommendDic setValue:detail.recommended.sid forKey:@"sid"];
        [YBMAnalyticsUtil track:page_CommodityList properties:recommendDic];
        
        // 埋点检测 sptype
        [YBMAnalyticsCheckUtil checkSpTypeField:detail.recommended.sptype andParams:recommendDic andIsFromTrack:YES];
    }
    self.enterViewDate = [NSDate date];
}

// 商详页关闭埋点 - 极光埋点已移除
- (void)auroraAnalysisWithProductDetailClose {
    // 极光埋点 product_view_close 已移除
    // 如果需要其他埋点系统的页面关闭统计，可以在此处添加

    // 保留方法以确保调用兼容性
    // 可以在此处添加其他埋点系统的实现，如QT埋点或雪地埋点
}


#pragma mark - GETTER

- (YBMProductDetailViewModel *)viewModel {
    if (!_viewModel) {
        _viewModel = YBMProductDetailViewModel.new;
        _viewModel.showRecPurchase = self.showRecPurchase;
        _viewModel.showRecPurchaseType = self.showRecPurchaseType;
        _viewModel.productID = self.productID;
    }
    return _viewModel;
}

- (YBMProductNavigationBarView *)navigationBarView {
    if (!_navigationBarView) {
        _navigationBarView = [[YBMProductNavigationBarView alloc] init];
        _navigationBarView.delegate = self;
    }
    return _navigationBarView;
}

- (YBMProductDetailView *)productView {
    if (!_productView) {
        _productView = [[YBMProductDetailView alloc] init];
        _productView.delegate = self;
        @weakify(self)
        _productView.updateDataActionBlock = ^{
            @strongify(self)
            [self setupData];
        };
    }
    return _productView;
}

- (YBMProductInstructionView *)instructionView {
    if (!_instructionView) {
        _instructionView = [[YBMProductInstructionView alloc] init];
//        _instructionView.delegate = self;
        _instructionView.hidden = YES;
    }
    return _instructionView;
}

- (YBMProductAddBuyView *)addBuyView {
    if (!_addBuyView) {
        _addBuyView = [[YBMProductAddBuyView alloc] init];
        _addBuyView.delegate = self;
    }
    return _addBuyView;
}

- (YBMProductDetailDefaultView *)defaultView {
    if (!_defaultView) {
        _defaultView = [YBMProductDetailDefaultView new];
        _defaultView.hidden = YES;
    }
    return _defaultView;
}

- (YBMProductPriorityBuyView *)priorityBuyView {
    if (!_priorityBuyView) {
        _priorityBuyView = [YBMProductPriorityBuyView new];
        _priorityBuyView.hidden = YES;
    }
    return _priorityBuyView;
}

- (YBMProductTipsView *)tipsView {
    if (!_tipsView) {
        _tipsView = [YBMProductTipsView new];
    }
    return _tipsView;
}

-(NSString *)sdata {
    if (!_sdata) {
        _sdata = @"";
    }
    return _sdata;
}

#pragma mark - dealloc

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    YBMLog(@"\n\n 【dealloc %@:%p】\n", NSStringFromClass([self class]), self);
}

 
#pragma mark -QT埋点

// 页面曝光
- (void)QTTrackPageExposureWithModel:(YBMProductDetailModel *)detailModel {
    if (!self.detailHasTrack) { // 控制上报一次不再上报
        self.detailHasTrack = YES;
        
        YBMQTSpmModel *spmEntity = [[YBMQTSpmModel alloc] init];
        spmEntity.spmB = [NSString stringWithFormat:@"productDetail_%@-0_0", detailModel.ID];
        
        YBMCMSTrackDataModel *trackData = [[YBMCMSTrackDataModel alloc] init];
        trackData.spmEntity = spmEntity;
        
        NSString *spm = [YBMQTTrack createSpmWithModel:trackData vc:self];
        NSMutableDictionary *qtDic = NSMutableDictionary.new;
        qtDic[@"spm_cnt"] = spm;
        [YBMQTTrack track:kQTPageExposure attributes:qtDic];
    }
}

@end


