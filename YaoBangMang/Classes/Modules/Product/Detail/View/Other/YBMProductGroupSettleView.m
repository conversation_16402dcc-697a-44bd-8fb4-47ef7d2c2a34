//
//  YBMProductGroupSettleView.m
//  YaoBangMang
//
//  Created by 张佳彧 on 2020/4/19.
//  Copyright © 2020 XiaoYaoYao.Ltd. All rights reserved.
//

#import "YBMProductGroupSettleView.h"
#import "YBMStepperButton.h"
#import "YBMEventButton.h"
#import "YBMProductDetailRequest.h"
#import "XYYPendingOrderViewController.h"
#import "YBMOrderPreSettleRootModel.h"
#import "YBMPromInfoRootModel.h"
#import "YBMPromInfoRequest.h"
#import "YBMLabel.h"
#import "YBMCSUDetailModel.h"
#import "YBMPromInfoRootModel.h"
#import "YBMProductTagItemModel.h"
#import "YBMChangePromotionRootModel.h"
#import "YBMFreeBuyViewController.h"//随心拼专区主页
#import "YBMFreeBuySingleton.h"
#import "YBMAlertView.h"
#import "YBMGroupPromoCell.h"
#import "YBMProductDetailController.h"
#import "YBMSearchVC.h"
#import "XYYNewSearchVC.h"
#import "YBMPreferentialGiftPoolController.h"

@interface YBMProductGroupSettleView ()<YBMStepperButtonDelegate, UITableViewDelegate, UITableViewDataSource, YBMPreferentialGiftViewDelegate>

@property (nonatomic, strong) UIView *darkView;

@property (nonatomic, strong) UIView *bottomView;

@property (nonatomic, strong) UIWindow *backWindow;

@property (nonatomic, strong) UILabel *titleLabel;
/// 规格
@property (nonatomic, strong) UILabel *specLabel;
/// 库存
@property (nonatomic, strong) UILabel *inventoryLabel;
/// 效期
@property (nonatomic, strong) UILabel *timeLabel;

@property (nonatomic, strong) UILabel *buyNumTitleLabel;

@property (nonatomic, strong) UILabel *buyNumLabel;
/// 确认按钮
@property (nonatomic, strong) UIButton *sureButton;
/// 随心拼
@property (nonatomic, strong) UIButton *suixinButton;
/// “加入购物车”按钮
@property (nonatomic, strong) UIButton *addToCartButton;

@property (nonatomic, strong) YBMEventButton *closeButton;

@property (nonatomic, strong) YBMStepperButton *stepperButton;

@property (copy, nonatomic) NSNumber *addShopNum;

//优惠：
@property (strong, nonatomic) UILabel *discountLab;
//满赠、满降
@property (nonatomic, strong) UITableView *promTableView;

@property (strong, nonatomic) UILabel *priceLab;//拼团价
@property (strong, nonatomic) UILabel *totalTitleLab;//合计
@property (strong, nonatomic) UILabel *totalLab;//合计

@property (assign, nonatomic) BOOL isLoad;

@property (nonatomic, strong) YBMPromInfoModel* promInfoModel;
@property (nonatomic, strong) YBMChangePromotionModel *promotionModel;
/// 随心拼 折扣图
@property (nonatomic, strong) UIImageView *discountImgView;
/// 随心拼 折扣lab
@property (nonatomic, strong) UILabel *popLab;

/// 同笔订单享受包邮 提示View
@property (nonatomic, strong) UIView *allFreeShipView;
@property (nonatomic, strong) UILabel *allFreeShipLabel;
@property (nonatomic, strong) UIImageView *allFreeShipIcon;

/// 判断是否展示“购买此商品，同笔订单商品均享受包邮”提示
@property (nonatomic, assign) BOOL showBottomShip;
/// 限量价格提醒View
@property (nonatomic, strong) UIView *limitedPriceView;
/// 限量价格提醒内容
@property (nonatomic, strong) UILabel *limitedPriceLabel;

@property (nonatomic, copy) NSString *actPurchaseTip;

/// 保存键盘的高度
@property (nonatomic, assign) CGFloat KeyboardHeight;

@end

@implementation YBMProductGroupSettleView

- (void)setupControls {
    [self addSubview:self.darkView];
    [self addSubview:self.bottomView];
    [self setFrame:(CGRect){0, 0, SCREEN_SIZE}];
    [self.backWindow addSubview:self];
    //    [self.bottomView addSubview:self.closeButton];
    [self.bottomView addSubview:self.titleLabel];
    [self.bottomView addSubview:self.specLabel];
    [self.bottomView addSubview:self.inventoryLabel];
    [self.bottomView addSubview:self.timeLabel];
    [self.bottomView addSubview:self.promTableView];
    [self.bottomView addSubview:self.stepperButton];
    [self.bottomView addSubview:self.priceLab];
    [self.bottomView addSubview:self.buyNumTitleLabel];
    [self.bottomView addSubview:self.buyNumLabel];
    [self.bottomView addSubview:self.sureButton];
    
    // 加入购物车
    if (self.fromCoudanPage && self.showAddToCart) {
        [self.bottomView addSubview:self.addToCartButton];
    } else {
        if (self.showAddToCart) {
            [self.bottomView addSubview:self.addToCartButton];
        } else {
            // 随心拼
            if(self.isAnyGroupUI){
                [self.bottomView addSubview:self.suixinButton];
            }
        }
    }
    
    [self.bottomView addSubview:self.allFreeShipView];
    [self.allFreeShipView addSubview:self.allFreeShipLabel];
    [self.allFreeShipView addSubview:self.allFreeShipIcon];
    
    [self.bottomView addSubview:self.totalLab];
    
    [self.darkView setFrame:(CGRect){0, 0, SCREEN_SIZE}];
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(dismiss)];
    [self.darkView addGestureRecognizer:tap];
    //    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
    //        make.top.mas_equalTo(self.bottomView).offset(10);
    //        make.trailing.mas_equalTo(self.bottomView).offset(-10);
    //    }];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.bottomView).offset(10);
        make.leading.mas_equalTo(self.bottomView).offset(10);
        make.trailing.mas_equalTo(self.bottomView).offset(-10);
    }];
    [self.inventoryLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.titleLabel.mas_bottom).offset(5);
        make.leading.mas_equalTo(self.specLabel.mas_trailing).offset(15);
        make.trailing.mas_equalTo(self.bottomView.mas_trailing).offset(-10);
    }];
    [self.specLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.titleLabel.mas_bottom).offset(5);
        make.leading.mas_equalTo(self.bottomView).offset(10);
        make.trailing.mas_equalTo(self.inventoryLabel.mas_leading).offset(-5);
    }];
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.specLabel.mas_bottom);
        make.leading.mas_equalTo(self.titleLabel.mas_leading);
    }];
    
    if (self.fromCoudanPage && self.showAddToCart) {
        [self.sureButton mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(self.bottomView).offset(10);
            make.width.mas_equalTo(0.01);
            make.height.mas_equalTo(44);
            make.bottom.offset(-16-kBottomSafeHeight);
        }];
        [self.addToCartButton mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.trailing.mas_equalTo(self.bottomView).offset(-10);
            make.leading.mas_equalTo(self.sureButton.mas_trailing);
            make.height.mas_equalTo(44);
            make.bottom.offset(-16-kBottomSafeHeight);
        }];
    } else {
        if (self.showAddToCart) {
            // 展示购物车按钮
            [self.sureButton setTitle:@"立即参团" forState:UIControlStateNormal];
            [_sureButton setTitleColor:[UIColor colorWithHexString:@"#00B955"] forState:UIControlStateNormal];
            _sureButton.backgroundColor = [UIColor whiteColor];
            _sureButton.borderColor = [UIColor colorWithHexString:@"#00B955"];
            _sureButton.borderWidth = 0.5;
            [self.sureButton mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.leading.mas_equalTo(self.bottomView).offset(15);
                make.width.mas_equalTo((SCREEN_SIZE.width - 45)/2);
                make.height.mas_equalTo(44);
                make.bottom.offset(-16-kBottomSafeHeight);
            }];
            [self.addToCartButton mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.trailing.mas_equalTo(self.bottomView).offset(-15);
                make.width.mas_equalTo((SCREEN_SIZE.width - 45)/2);
                make.height.mas_equalTo(44);
                make.bottom.offset(-16-kBottomSafeHeight);
            }];
            
            // 按钮曝光
            [self auroraAnalysisExposureWithBtnName:@"立即参团"];
            [self auroraAnalysisExposureWithBtnName:@"加入购物车"];
        } else {
            if (self.isAnyGroupUI) {
                // 展示随心拼按钮
                [self.sureButton setTitle:@"立即参团" forState:UIControlStateNormal];
                [_sureButton setTitleColor:[UIColor colorWithHexString:@"#00B377"] forState:UIControlStateNormal];
                _sureButton.backgroundColor = [UIColor whiteColor];
                _sureButton.borderColor = [UIColor colorWithHexString:@"#00B377"];
                _sureButton.borderWidth = 0.5;
                [self.sureButton mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.leading.mas_equalTo(self.bottomView).offset(15);
                    make.width.mas_equalTo((SCREEN_SIZE.width - 45)/2);
                    make.height.mas_equalTo(44);
                    make.bottom.offset(-16-kBottomSafeHeight);
                }];
                [self.suixinButton mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.trailing.mas_equalTo(self.bottomView).offset(-15);
                    make.width.mas_equalTo((SCREEN_SIZE.width - 45)/2);
                    make.height.mas_equalTo(44);
                    make.bottom.offset(-16-kBottomSafeHeight);
                }];
                
                [self.suixinButton addSubview:self.discountImgView];
                [self.discountImgView mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.centerY.mas_equalTo(self.suixinButton.mas_top).offset(3);
                    make.leading.mas_equalTo(self.suixinButton.mas_centerX);
                }];
                
                if (_model.suiXinPinButtonBubbleText.length) {
                    self.popLab.text = _model.suiXinPinButtonBubbleText;
                    [self.suixinButton setTitle:_model.suiXinPinButtonText forState:UIControlStateNormal];
                    self.discountImgView.hidden = self.popLab.hidden = NO;
                }else{
                    self.discountImgView.hidden = self.popLab.hidden = YES;
                }

                // 按钮曝光
                [self auroraAnalysisExposureWithBtnName:@"立即参团"];
                [self auroraAnalysisExposureWithBtnName:@"随心拼"];
            } else {
                // 只有一个按钮的情况
                [self.sureButton mas_remakeConstraints:^(MASConstraintMaker *make) {
                    make.leading.mas_equalTo(self.bottomView).offset(10);
                    make.trailing.mas_equalTo(self.bottomView).offset(-10);
                    make.height.mas_equalTo(44);
                    make.bottom.offset(-16-kBottomSafeHeight);
                }];
                
                // 按钮曝光
                [self auroraAnalysisExposureWithBtnName:@"确定"];
            }
        }
    }
    
    // 购买此商品，同笔订单商品均享受包邮
    [self.allFreeShipView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.sureButton.mas_top).offset(-6.5);
        make.leading.mas_equalTo(0);
        make.trailing.mas_equalTo(0);
        make.height.mas_equalTo(32);
    }];
    // 提示中的文案
    [self.allFreeShipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.offset(10);
        make.centerY.offset(0);
        make.height.offset(18.f);
    }];
    // 提示中的图标
    [self.allFreeShipIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(self.allFreeShipLabel.mas_left).offset(-6);
        make.centerY.offset(0);
        make.width.offset(16);
        make.height.offset(16);
    }];
    
    [self.stepperButton mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.top.mas_equalTo(self.promTableView.mas_bottom).offset(30);
        make.trailing.mas_equalTo(self).offset(-10);
        make.size.mas_equalTo(CGSizeMake(90, 30));
        make.bottom.mas_equalTo(self.totalLab.mas_top).offset(2);
    }];
    [self.buyNumLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.stepperButton.mas_top).offset(-4);
        make.centerX.mas_equalTo(self.stepperButton.mas_centerX);
    }];
    [self.buyNumTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.stepperButton.mas_centerY);
        make.leading.mas_equalTo(self.titleLabel.mas_leading);
    }];
    [self.priceLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.buyNumTitleLabel.mas_top).offset(-5);
        make.leading.mas_equalTo(10);
    }];
    [self.bottomView addSubview:self.totalTitleLab];
    
    if (self.showAllFreeShip) {
        // 展示底部“购买此商品，同笔订单商品均享受包邮“提示View
        [self.totalTitleLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.mas_equalTo(self.allFreeShipView.mas_top).offset(-15);
            make.leading.mas_equalTo(10);
        }];
    } else {
        [self.totalTitleLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.mas_equalTo(self.sureButton.mas_top).offset(-15);
            make.leading.mas_equalTo(10);
        }];
    }
    
    [self.totalLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.totalTitleLab.mas_trailing);
        //        make.bottom.mas_equalTo(lab.mas_bottom);
        make.centerY.mas_equalTo(self.totalTitleLab.mas_centerY).offset(-3);
    }];
    
    // 限量价格提醒
    [self.bottomView addSubview:self.limitedPriceView];
    [self.limitedPriceView addSubview:self.limitedPriceLabel];
    
    [self.limitedPriceView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.titleLabel.mas_leading);
        make.trailing.mas_equalTo(self.titleLabel.mas_trailing);
        make.top.mas_equalTo(self.timeLabel.mas_bottom).offset(15);
    }];
    
    [self.limitedPriceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.limitedPriceView.mas_leading).offset(5);
        make.trailing.mas_equalTo(self.limitedPriceView.mas_trailing).offset(-5);
        make.top.mas_equalTo(self.limitedPriceView.mas_top).offset(0);
        make.bottom.mas_equalTo(self.limitedPriceView.mas_bottom).offset(-0);
    }];
    
    [self.bottomView addSubview:self.discountLab];
    [self.discountLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.limitedPriceView.mas_bottom).offset(15);
//        make.top.mas_equalTo(self.timeLabel.mas_bottom).offset(10);
        make.leading.mas_equalTo(self.bottomView).offset(10);
        make.width.mas_equalTo(36);
    }];
    
    [self.promTableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.discountLab.mas_top).offset(0);
        make.leading.mas_equalTo(self.discountLab.mas_trailing).offset(0);
        make.trailing.mas_equalTo(self.bottomView).offset(0);
        make.height.mas_lessThanOrEqualTo(TABLE_MAX_HEIGHT);
//        make.bottom.mas_equalTo(self.stepperButton.mas_top).offset(-2);
    }];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillShow:) name:UIKeyboardWillShowNotification object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillHide:) name:UIKeyboardWillHideNotification object:nil];
}

- (void)keyboardWillShow:(NSNotification *)notification {
    NSDictionary *userInfo = [notification userInfo];
    NSValue *aValue = [userInfo objectForKey:UIKeyboardFrameEndUserInfoKey];
    CGRect keyboardRect = [aValue CGRectValue];
    
    CGRect frame = self.bottomView.frame;
    frame.origin.y = SCREEN_SIZE.height - (keyboardRect.size.height + frame.size.height) + 100;//增加100Pt使键盘遮挡住确定按钮，防止跳过完成进入购物车
    self.sureButton.hidden = YES;
    self.bottomView.frame = frame;
    
    self.KeyboardHeight = keyboardRect.size.height - 100;
}

- (void)keyboardWillHide:(NSNotification *)notification {
    CGRect frame = self.bottomView.frame;
    frame.origin.y = SCREEN_SIZE.height - frame.size.height;
    self.bottomView.frame = frame;
    self.sureButton.hidden = NO;
    
    self.KeyboardHeight = 0;
}

- (void)show:(CGRect)frame{
    self.backWindow.hidden = NO;
    [UIView animateWithDuration:0.3 delay:0 options:UIViewAnimationOptionCurveEaseOut animations:^{
        self.darkView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.3];
        self.darkView.userInteractionEnabled = YES;
        self.bottomView.frame = frame;
        
    } completion:^(BOOL finished) {
    }];
    
    NSMutableDictionary *otherDic = [NSMutableDictionary dictionary];
 
    [otherDic setValue:self.model.ID forKey:@"productId"];
    [otherDic setValue:@"3" forKey:@"productType"];
    [otherDic setValue:self.model.shopCode forKey:@"shop_code"];
    [otherDic setValue:self.model.sptype forKey:@"sptype"];
    [otherDic setValue:self.model.spid forKey:@"spid"];
    if(self.isAnyGroupUI){
        [otherDic setValue:@[@"0",@"1",@"3",@"4"] forKey:@"buttonList"];
    }else{
        [otherDic setValue:@[@"0",@"1",@"2"] forKey:@"buttonList"];
    }
    [YBMAnalyticsUtil track:kPintuan_newFloat_Exposure properties:otherDic];
}

#pragma mark - YBMStepperButtonDelegate

- (void)stepper:(YBMStepperButton *)stepper number:(NSUInteger)number increasing:(BOOL)increasing {
    @weakify(self);
    
    // 获取红条展示的内容
    [self requestAddCartTrialWithNumber:number];
    
    if (number == self.model.skuStartNum.integerValue) {
        //        stepper.reduceTitleColor = UIColor.grayColor;
    }else{
        
    }
    
    self.addShopNum = [NSNumber numberWithInteger:number];
    if (number < _model.skuStartNum.integerValue) self.addShopNum = _model.skuStartNum;
    
    if( [XYYUserData ShareUserData].merchantId == nil){
        //TODO, 似乎是详情页被踢导致对话框展示在了login页面上，一点崩了
        return;
    }
    
    NSMutableDictionary *dict = [NSMutableDictionary new];
    dict[@"skuId"] = self.model.ID ? : @"";
    dict[@"amount"] = self.addShopNum ? : @0;
    dict[@"promoId"] = self.model.marketingId ? : @"";
    dict[@"merchantId"] = [XYYUserData ShareUserData].merchantId;
    dict[@"scenceType"] = @"0";
    NSMutableDictionary *tempJG = [NSMutableDictionary dictionary];
    tempJG[@"key_word"] = self.model.keyword;
    if (self.model.positionType) {
        tempJG[@"list_position_type"] = [NSString stringWithFormat:@"%@",self.model.positionType];
    }
    tempJG[@"list_position_typename"] = self.model.positionTypeName;
    tempJG[@"search_sort_strategy_id"] = self.model.searchSortStrategyCode;
    tempJG[@"direct_module"] = @(self.model.cardType == 2 ? 2 : 1);
    if (self.model.operationId) {
        tempJG[@"operation_id"] = self.model.operationId;
        tempJG[@"operation_rank"] = @(self.model.operationRank + 1);
        tempJG[@"rank"] = @(self.model.rank + 1);
    }else{
        tempJG[@"rank"] = @(self.model.rank + 1);
    }
    UIViewController *frontVc = self.ybm_viewController;
    if ([frontVc respondsToSelector:@selector(entrance)]) {
        NSString *entrance = [frontVc valueForKey:@"entrance"];
        if (entrance && entrance.length) {
            tempJG[@"entrance"] = entrance;
        }
    }
    if ([frontVc isKindOfClass:[YBMProductDetailController class]]) {
        YBMProductDetailController *detailVc = (YBMProductDetailController *)frontVc;
        tempJG[@"page_type"] = detailVc.pageType;
        tempJG[@"page_id"] = detailVc.pageId;
        tempJG[@"page_name"] = detailVc.pageName;
        tempJG[@"component_position"] = detailVc.componentPosition;
        tempJG[@"component_name"] = detailVc.componentName;
        tempJG[@"component_title"] = detailVc.componentTitle;
    }
    NSString *sessionId = [[AnalysysAgent getPresetProperties] objectForKey:@"$session_id"];
    tempJG[@"$session_id"] = sessionId;
    dict[@"mddata"] = tempJG.mj_JSONString;
    YBMProductChangeCartForPromotionRequest *rq = YBMProductChangeCartForPromotionRequest.new;
    rq.addParams(dict).request(^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
        @strongify(self);
        if (success && response != nil) {
            YBMChangePromotionRootModel *rModel = response;
            self.promotionModel = rModel.data;
            self.stepperButton.number = rModel.data.qty.integerValue;
            self.addShopNum = rModel.data.qty;
            self.totalLab.attributedText = [self dealPrice:rModel.data.totalAmount];
            if (self.model.stepPriceStatus == 1) {
                self.priceLab.text = [NSString stringWithFormat:@"拼团价：%@", rModel.data.price];
                [Tools setLabel:self.priceLab color:ColorFromHex(0x292933) string:@"拼团价："];
            }else{
                self.priceLab.text = @"";
            }
            // 限量价格提醒
            self.actPurchaseTip = rModel.data.actPurchaseTip;
            self.limitedPriceLabel.text = self.actPurchaseTip;
            if (rModel.data.actPurchaseTip.length) {
                [self.limitedPriceLabel mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.top.mas_equalTo(self.limitedPriceView.mas_top).offset(4);
                    make.bottom.mas_equalTo(self.limitedPriceView.mas_bottom).offset(-3);
                }];
            } else {
                [self.limitedPriceLabel mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.top.mas_equalTo(self.limitedPriceView.mas_top).offset(0);
                    make.bottom.mas_equalTo(self.limitedPriceView.mas_bottom).offset(0);
                }];
            }
            
            [self resetTableViewHeight];
        }
    });
    
    
    //点击列表底部动态面板埋点(改为后端埋点)
//    NSMutableDictionary *mutDic = [NSMutableDictionary dictionary];
//    if(increasing){
//        mutDic[@"buttonType"] = @"0";
//    }else{
//        mutDic[@"buttonType"] = @"1";
//    }
//
//
//    [mutDic setValue:self.model.ID forKey:@"productId"];
//    [mutDic setValue:@"3" forKey:@"productType"];
//    [mutDic setValue:self.model.shopCode forKey:@"shop_code"];
//    [mutDic setValue:self.model.sptype forKey:@"sptype"];
//    [mutDic setValue:self.model.spid forKey:@"spid"];
//    [YBMAnalyticsUtil track:kPintuaNnewFloatExposureButtonClick properties:mutDic];
}

// 处理红色“购买此商品，同笔订单均享受包邮”提示语的逻辑---->商品详情页
- (void)requestAddCartTrialWithNumber:(NSUInteger)number
{
    // 如果不是从商品详情页进来的，就不往下走了，直接走列表来的逻辑
    if (!self.fromProductDetail) {
        return;
    }
    
    [XYYHttpApi cancelSessionTask];
    [ZBHUD showLoadingWithEnableUserInteraction:YES];
    
    NSMutableDictionary *dic = [NSMutableDictionary new];
    dic[@"skuId"] = self.model.ID;
    dic[@"quantity"] = @(number); // 加购数量
    dic[@"orgId"] = self.model.orgId; // 商品所在店铺的orgId
    dic[@"productPrice"] = self.model.fob; // 商品单价
    dic[@"productType"] = self.model.productType;
    dic[@"freeShippingFlag"] = @(self.freeShippingFlag);
    @weakify(self);
    [XYYHttpApi postUrl:API_Interface(@"/app/addCartTrial") Param:dic success:^(NSDictionary *resultData) {
        @strongify(self);
        [ZBHUD dismiss];
        
        NSString *freightTips = resultData[@"freightTips"];
        
        if (freightTips.length > 0) {
            self.allFreeShipLabel.text = freightTips;
            // 展示底部“购买此商品，同笔订单商品均享受包邮“提示View
            [self.totalTitleLab mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.bottom.mas_equalTo(self.allFreeShipView.mas_top).offset(-15);
                make.leading.mas_equalTo(10);
            }];
            self.showBottomShip = YES;
        } else {
            [self.totalTitleLab mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.bottom.mas_equalTo(self.sureButton.mas_top).offset(-15);
                make.leading.mas_equalTo(10);
            }];
            self.showBottomShip = NO;
        }
        
        [self resetTableViewHeight];
        
    } failure:^(NSDictionary *resultData) {
        YBMLog(@"请求: %d",number);
        [ZBHUD dismiss];
    }];
}

#pragma mark -  TOUCH

- (void)dismiss {
    [UIView animateWithDuration:0.3 delay:0 options:UIViewAnimationOptionCurveEaseOut animations:^{
        self.darkView.userInteractionEnabled = NO;
        self.darkView.backgroundColor = [UIColor colorWithWhite:0 alpha:0];
        CGRect frame = self.bottomView.frame;
        frame.origin.y = kScreenHeight;
        self.bottomView.frame = frame;
    } completion:^(BOOL finished) {
        self.backWindow.hidden = YES;
        [self removeFromSuperview];
    }];
}

// 点击“加入购物车”的事件
- (void)addToCartButtonClick:(UIButton *)button {
    if (self.block){
        // 埋点
//        [self auroraAnalysisClickWithBtnName:@"加入购物车"];
        self.block(self.stepperButton.number);
        [self dismiss];
    }
    
    //直睿极光新埋点
    [self trackProductButtonClick:@"加入购物车"];
    [self trackProductAddToCart];
}

- (void)sureActionButton:(UIButton *)button {
    //信息没加载全的时候，先不让点击
    if (!self.promotionModel){
        return;
    }
    
    // 有种情况，商详页弹出这个框，点击确定是加入购物车，根据fromProductDetail判断走这种情况
    if (self.fromProductDetail == YES) {
        //直睿极光新埋点 详情页购买
        [self trackProductButtonClick:@"确定"];
        [self trackProductAddToCart];

        if (self.block){
            self.block(self.stepperButton.number);
            [self dismiss];
        }
        return;
    }
    
    // qt埋点
    if (self.model.listClick) {
        // 商品列表-商品按钮点击
        [self QTTrackActionProductButtonClickModel:self.model btnIndex:@"4" btnText:button.titleLabel.text];
    } else {
        // 其它-子模块点击
        [self QTTrackActionSubModuleClickModel:self.model btnIndex:@"4" btnText:button.titleLabel.text];
    }
    
    [YBMFreeBuySingleton instance].model = nil;
    [[YBMFreeBuySingleton instance].suiXinPinArr removeAllObjects];
    
    @weakify(self)
    YBMProductGroupPurchasePreSettleRequest *request = YBMProductGroupPurchasePreSettleRequest.new;
    NSMutableDictionary *dic = [NSMutableDictionary new];
    dic[@"skuId"] = self.model.ID ? : @"";
    dic[@"productNum"] = self.addShopNum ?: @0;
    dic[@"sid"] = self.model.sid;
    dic[@"spid"] = self.model.spid;
    dic[@"sptype"] = self.model.sptype;
    
    // 埋点检测 sptype
    [YBMAnalyticsCheckUtil checkSpTypeField:self.model.sptype andParams:dic andIsFromTrack:NO];
    
    request.addParams(dic).request(^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
        @strongify(self)
        if (success && response != nil) {
            YBMOrderPreSettleRootModel *model = response;
            if([model.isShowDialog isEqualToNumber:@1]){
                YBMAlertView *alert = [[YBMAlertView alloc] initWithTitle:@"温馨提示" message:@"您的资质已过期, 请及时更新, 以免影响发货" cancelButtonTitle:nil otherButtonTitle:@"我知道了"];
                alert.messageAlignment = NSTextAlignmentCenter;
                alert.completionBlock = ^(NSInteger index){
                    if (index == 0){
                        if(self.isAnyGroupUI){
                            [self goToBuyPush:model];
                        }else{
                            [self pushSubViewController:model];
                        }
                    }
                };
                [alert show];
            }else{
                if(self.isAnyGroupUI){
                    [self goToBuyPush:model];
                   
                }else{
                    [self pushSubViewController:model];
                }
            }
        }
    });
    
    //点击列表底部动态面板埋点
    [self auroraAnalysisWithSureButtonClick];
    //直睿商品点击埋点
    if(self.isAnyGroupUI || self.showAddToCart){
      
        [self trackProductButtonClick:@"立即参团"];
    }else{
      
        [self trackProductButtonClick:@"确定"];//进入提单页 是否展示立即参团
    }
}

-(void)pushSubViewController:(YBMOrderPreSettleRootModel *)model{
    if (model.data != nil) {
        //随心拼专区主页
        [self configMainModel];
        if (self.isAnyGroup) {
            YBMFreeBuyViewController *vc = [[YBMFreeBuyViewController alloc]init];
            UIViewController *frontVc = self.ybm_viewController;
            if ([frontVc respondsToSelector:@selector(entrance)]) {
                NSString *entrance = [frontVc valueForKey:@"entrance"];
                if (entrance && entrance.length) {
                    //这里是加购埋点也就是拼团弹框出现的地方链路节点
                    vc.entrance = entrance;
                }
            }
            vc.isPiGou = [self.model.isWholesale isEqualToNumber:@1];
            vc.orgId = self.model.orgId;
            vc.shopCodes = self.model.shopCode;
            vc.isThirdCompany = self.model.isThirdCompany.integerValue;
            vc.tranNo = model.data.tranNo;
            vc.skuId = [NSString stringWithFormat:@"%@",self.model.ID];
            vc.productNum = self.addShopNum;
            vc.orgName = self.model.orgName;
            vc.operationId = self.model.operationId;
            vc.operationRank = @(self.model.operationRank + 1);
            vc.rank = @(self.model.rank + 1);
            vc.keyword = self.model.keyword;
            vc.positionType = self.model.positionType;
            vc.positionTypeName = self.model.positionTypeName;
            vc.sid = self.model.sid;
            vc.spid = self.model.spid;
            vc.sptype = self.model.sptype;
            [self.ybm_viewController.navigationController pushViewController:vc animated:YES];
        }else{//普通拼团去结算
            XYYPendingOrderViewController *vc = [XYYPendingOrderViewController new];
            vc.productMDdataDict = [self getMdDataDict];
            UIViewController *frontVc = self.ybm_viewController;
            if ([frontVc respondsToSelector:@selector(entrance)]) {
                NSString *entrance = [frontVc valueForKey:@"entrance"];
                if (entrance && entrance.length) {
                    //这里是加购埋点也就是拼团弹框出现的地方链路节点
                    vc.entrance = entrance;
                }
            }
            vc.shopCode = self.model.shopCode; // self.model.supportSuiXinPin ? self.model.shopCode : nil;
            vc.tranNo = model.data.tranNo;
            vc.orderFrom = YBMOrderConfirmOrderFromGroup;
            vc.skuId = [NSString stringWithFormat:@"%@",self.model.ID];
            vc.productNum = self.addShopNum;
            vc.operationId = self.model.operationId;
            vc.operationRank = @(self.model.operationRank + 1);
            vc.rank = @(self.model.rank + 1);
            vc.sid = self.model.sid;
            vc.spid = self.model.spid;
            vc.sptype = self.model.sptype;
            vc.keyword = self.model.keyword;
            vc.positionType = self.model.positionType;
            vc.positionTypeName = self.model.positionTypeName;
            vc.isSupportOldSxp = self.model.supportSuiXinPin;
            [AppConfigue pushVC:vc];
        }
        [self dismiss];
       
    }
}

// 配置随心拼主品数据
- (void)configMainModel{
    YBMProductItemModel *m = [[YBMProductItemModel alloc]init];
    m.showName = [NSString stringWithFormat:@"%@/%@/%@",self.model.showName,self.model.spec,self.model.productUnit];
    m.productUnit = self.model.productUnit;
    m.suggestPriceStr = self.promotionModel.price;
    m.qty = self.promotionModel.qty.stringValue;
    m.subtotal = self.promotionModel.totalAmount;
    m.imageUrl = self.model.imageUrl;
    m.markerUrl = self.model.markerUrl;
    [YBMFreeBuySingleton instance].pid = self.model.pid;
    [YBMFreeBuySingleton instance].model = m;
}

- (void)closeActionButton {
    [self dismiss];
}

-(void)suixinClick{
    
    //信息没加载全的时候，先不让点击
    if (!self.promotionModel){
        return;
    }
    [YBMFreeBuySingleton instance].model = nil;
    [[YBMFreeBuySingleton instance].suiXinPinArr removeAllObjects];
    @weakify(self)
    YBMProductGroupPurchasePreSettleRequest *request = YBMProductGroupPurchasePreSettleRequest.new;
    NSMutableDictionary *dic = [NSMutableDictionary new];
    dic[@"skuId"] = self.model.ID ? : @"";
    dic[@"productNum"] = self.addShopNum ?: @0;
    dic[@"sid"] = self.model.sid;
    dic[@"spid"] = self.model.spid;
    dic[@"sptype"] = self.model.sptype;
    dic[@"isPiGou"] = @"1";
    // 埋点检测 sptype
    [YBMAnalyticsCheckUtil checkSpTypeField:self.model.sptype andParams:dic andIsFromTrack:NO];
    [self showSpinner];
    request.addParams(dic).request(^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
        [self hideSpinner];
        @strongify(self)
        if (success && response != nil) {
            YBMOrderPreSettleRootModel *model = response;
            if([model.isShowDialog isEqualToNumber:@1]){
                YBMAlertView *alert = [[YBMAlertView alloc] initWithTitle:@"温馨提示" message:@"您的资质已过期, 请及时更新, 以免影响发货" cancelButtonTitle:nil otherButtonTitle:@"我知道了"];
                alert.messageAlignment = NSTextAlignmentCenter;
                alert.completionBlock = ^(NSInteger index){
                    if (index == 0){
                        [self suixinPush:model];
                    }
                };
                [alert show];
            }else{
                [self suixinPush:model];
            }
        }
    });
    
    //点击列表底部动态面板埋点
    [self auroraAnalysisWithSuixinButtonClick];
    // 点击埋点
//    [self auroraAnalysisClickWithBtnName:@"随心拼"];
    
    //直睿新极光商品点击埋点
    [self trackProductButtonClick:@"随心拼"];
    // qt埋点
    if (self.model.listClick) {
        // 商品列表-商品按钮点击
        [self QTTrackActionProductButtonClickModel:self.model btnIndex:@"5" btnText:@"随心拼"];
    } else {
        // 其它-子模块点击
        [self QTTrackActionSubModuleClickModel:self.model btnIndex:@"5" btnText:@"随心拼"];
    }
}

-(void)suixinPush:(YBMOrderPreSettleRootModel *)model{
    if (model.data != nil) {
        YBMFreeBuyViewController *vc = [[YBMFreeBuyViewController alloc]init];
        UIViewController *frontVc = self.ybm_viewController;
        if ([frontVc respondsToSelector:@selector(entrance)]) {
            NSString *entrance = [frontVc valueForKey:@"entrance"];
            if (entrance && entrance.length) {
                //这里是加购埋点也就是拼团弹框出现的地方链路节点
                vc.entrance = entrance;
            }
        }
        vc.isPiGou = [self.model.isWholesale isEqualToNumber:@1];
        vc.orgId = self.model.orgId;
        vc.shopCodes = self.model.shopCode;
        vc.isThirdCompany = self.model.isThirdCompany.integerValue;
        vc.tranNo = model.data.tranNo;
        vc.skuId = [NSString stringWithFormat:@"%@",self.model.ID];
        vc.productNum = self.addShopNum;
        vc.orgName = self.model.orgName;
        vc.operationId = self.model.operationId;
        vc.operationRank = @(self.model.operationRank + 1);
        vc.rank = @(self.model.rank + 1);
        vc.keyword = self.model.keyword;
        vc.positionType = self.model.positionType;
        vc.positionTypeName = self.model.positionTypeName;
        vc.sid = self.model.sid;
        vc.spid = self.model.spid;
        vc.sptype = self.model.sptype;
        [self configMainModel];
        [self.ybm_viewController.navigationController pushViewController:vc animated:YES];
        
    }
    [self dismiss];
}
-(void)goToBuyPush:(YBMOrderPreSettleRootModel *)model{
    if (model.data != nil) {
        XYYPendingOrderViewController *vc = [XYYPendingOrderViewController new];
        vc.productMDdataDict = [self getMdDataDict];
        UIViewController *frontVc = self.ybm_viewController;
        if ([frontVc respondsToSelector:@selector(entrance)]) {
            NSString *entrance = [frontVc valueForKey:@"entrance"];
            if (entrance && entrance.length) {
                //这里是加购埋点也就是拼团弹框出现的地方链路节点
                vc.entrance = entrance;
            }
        }
        vc.shopCode = self.model.supportSuiXinPin ? self.model.shopCode : nil;
        vc.tranNo = model.data.tranNo;
        vc.orderFrom = YBMOrderConfirmOrderFromGroup;
        vc.skuId = [NSString stringWithFormat:@"%@",self.model.ID];
        vc.productNum = self.addShopNum;
        vc.operationId = self.model.operationId;
        vc.operationRank = @(self.model.operationRank + 1);
        vc.rank = @(self.model.rank + 1);
        vc.sid = self.model.sid;
        vc.spid = self.model.spid;
        vc.sptype = self.model.sptype;
        vc.keyword = self.model.keyword;
        vc.positionType = self.model.positionType;
        vc.positionTypeName = self.model.positionTypeName;
        vc.isSupportOldSxp = self.model.supportSuiXinPin;
        [AppConfigue pushVC:vc];
        
    }
    [self dismiss];
}

//获取mddata参数     /**************直睿加入购物车mddata需要传递的链路参数*************/
- (NSMutableDictionary*)getMdDataDict{
     NSMutableDictionary *mdDict = @{}.mutableCopy;
     UIViewController *targetVc = self.ybm_viewController;
     if([targetVc isKindOfClass:[YBMSearchVC class]] || [targetVc isKindOfClass:[XYYNewSearchVC class]]){//商品列表页
         mdDict[@"key_word"] = self.productItemModel.keyword;//搜索关键字
         mdDict[@"list_position_typename"] = self.productItemModel.positionTypeName;//搜索坑位类型名称
         if (self.model.positionType) {//搜索坑位类型
             mdDict[@"list_position_type"] = [NSString stringWithFormat:@"%@",self.productItemModel.positionType];
         }
         mdDict[@"search_sort_strategy_id"] = self.productItemModel.searchSortStrategyCode;//策略id
         if (self.model.operationId) {
             mdDict[@"operation_id"] = self.productItemModel.operationId;//运营位ID
             mdDict[@"operation_rank"] = @(self.productItemModel.operationRank);
         }
         mdDict[@"rank"] = @(self.productItemModel.index);
         
         //商品相关
         if (self.productItemModel.productActivityType) {
             mdDict[@"product_activity_type"] = [NSString stringWithFormat:@"%@",self.productItemModel.productActivityType];//商品营销活动类型。例："1"，"2"，"3"，"4"，"5"
         }
         if (self.productItemModel.actPt && self.productItemModel.controlTitle.length == 0 && self.productItemModel.actPt.assembleStatus.integerValue <= 1 && self.productItemModel.available) {//拼团
             mdDict[@"product_price"] = self.productItemModel.actPt.assemblePrice;
         }else if (self.productItemModel.actSk && self.productItemModel.available){//秒杀
             mdDict[@"product_price"] =  self.productItemModel.actSk.skPrice;
         }else if (self.productItemModel.actPgby){//批购包邮
             mdDict[@"product_price"] = self.productItemModel.actPgby.assemblePrice;
         }else{
             mdDict[@"product_price"] = self.productItemModel.fob;
         }
         if(self.productItemModel.productType){
             mdDict[@"product_type"] = [NSString stringWithFormat:@"%@",self.productItemModel.productType];;//商品类型
         }
         mdDict[@"product_shop_name"] = self.productItemModel.shopName;//商品店铺名称
         mdDict[@"product_shop_code"] = self.productItemModel.shopCode;//商品店铺编码
         mdDict[@"product_name"] = self.productItemModel.showName;//商品名称
         mdDict[@"product_id"] = self.productItemModel.ID;//商品ID
         mdDict[@"product_first"] = [NSString stringWithFormat:@"%@",self.productItemModel.categoryId];//商品一级分类
         
         if ([targetVc respondsToSelector:@selector(queryModel)]) {
                YBMProductSearchDataModel *queryModel  = [targetVc valueForKey:@"queryModel"];
                mdDict[@"result_cnt"] = @(queryModel.totalCount);
                mdDict[@"page_no"] = @(queryModel.pageNo);
                mdDict[@"page_size"] = @(queryModel.pageSize);
                mdDict[@"total_page"] = @(queryModel.totalPage);
                mdDict[@"sptype"] = queryModel.sptype;
                mdDict[@"sid"] = queryModel.sid;
                mdDict[@"paixu"] = queryModel.paixu;
            }
         
         if ([targetVc respondsToSelector:@selector(searchJgspid)]) {
             mdDict[@"jgspid"] = [targetVc valueForKey:@"searchJgspid"];
          }
         //   0"-其他；"1"-列表页直接加购提单；"2"-详情页加购提单；"3"-购物车页面加购；"4"-订单再次购买加购
          mdDict[@"direct"] = @"1";//列表和弹窗都为列表加购
     }else if([targetVc isKindOfClass:[YBMProductDetailController class]]){//商品详情页
         YBMProductDetailController *productDetailVc = (YBMProductDetailController*)self.ybm_viewController;
         mdDict = productDetailVc.productDetailMdDataDict;
     }
    NSString *sessionId = [[AnalysysAgent getPresetProperties] objectForKey:@"$session_id"];
    mdDict[@"$session_id"] = sessionId;
    return  mdDict;
}
#pragma mark - 埋点
//直睿新埋点 商品按钮点击埋点
- (void)trackProductButtonClick:(NSString*)btnName{

    NSMutableDictionary *mutD;
    UIViewController *targetVc = self.ybm_viewController;
    if ([targetVc isKindOfClass:[YBMProductDetailController class]]) {
        mutD = @{}.mutableCopy;
        YBMProductDetailController *productDetailVc = (YBMProductDetailController*)self.ybm_viewController;
        [mutD addEntriesFromDictionary:productDetailVc.productDetailTrackDict];
        mutD[@"btn_desc"] = @"商详页底部弹窗";//"列表页"，"列表页底部弹窗"，"商详页"，"商详页底部弹窗
        mutD[@"product_number"] = @(self.stepperButton.number);//商品数量
        mutD[@"btn_name"] = btnName;//："立即参团"、"去抢购"、"加入购物车"
        // 极光埋点已移除 - action_product_button_click (商品详情页底部弹窗)
        // [AnalysysAgent track:kAction_product_button_click properties:mutD];
        return;

    }else if([targetVc isKindOfClass:[YBMSearchVC class]] || [targetVc isKindOfClass:[XYYNewSearchVC class]]){

        mutD = [self getTrackCommonParams];
        mutD[@"btn_desc"] = @"列表页底部弹窗";//"列表页"，"列表页底部弹窗"，"商详页"，"商详页底部弹窗
        mutD[@"direct"] = @"1";//0"-其他；"1"-列表页直接加购提单；"2"-详情页加购提单；"3"-购物车页面加购；"4"-订单再次购买加购

    }else{//其他来源 后面在这里加
        mutD = @{}.mutableCopy;
        mutD[@"product_number"] = @(self.stepperButton.number);//商品数量
        mutD[@"btn_name"] = btnName;//："立即参团"、"去抢购"、"加入购物车"
        // 极光埋点已移除 - action_product_button_click (其他页面)
        // [AnalysysAgent track:kAction_product_button_click properties:mutD];
        return;
    }
    mutD[@"product_number"] = @(self.stepperButton.number);//商品数量
    mutD[@"btn_name"] = btnName;//："立即参团"、"去抢购"、"加入购物车"
//    NSLog(@"%@-------",mutD);
    // 保留搜索页面的极光埋点
    [AnalysysAgent track:kAction_product_button_click properties:mutD];
}

//直睿新的列表商品加入购物车埋点
- (void)trackProductAddToCart{
 
    NSMutableDictionary *mutD;
    UIViewController *targetVc = self.ybm_viewController;
    if ([targetVc isKindOfClass:[YBMProductDetailController class]]) {
        mutD = @{}.mutableCopy;
        YBMProductDetailController *productDetailVc = (YBMProductDetailController*)self.ybm_viewController;
        [mutD addEntriesFromDictionary:productDetailVc.productDetailTrackDict];
   }else if([targetVc isKindOfClass:[YBMSearchVC class]] || [targetVc isKindOfClass:[XYYNewSearchVC class]]){
        
        mutD  = [self getTrackCommonParams];
        mutD[@"direct"] = @"1";//0"-其他；"1"-列表页直接加购提单；"2"-详情页加购提单；"3"-购物车页面加购；"4"-订单再次购买加购
    }else{//其他来源 后面在这里加
        mutD = @{}.mutableCopy;
    }
    mutD[@"product_number"] = @(self.stepperButton.number);//商品数量
    NSDate *currentDate = [NSDate date];
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
    mutD[@"add_cart_time"] = [dateFormatter stringFromDate:currentDate];;//加购时间。
//    NSLog(@"%@-------",mutD);
    [AnalysysAgent track:kAdd_to_cart properties:mutD];
}
 
- (NSMutableDictionary*)getTrackCommonParams{
    NSMutableDictionary *mutD = [NSMutableDictionary dictionary];
    YBMProductItemModel *productInfo = self.productItemModel;
    mutD[@"rank"] = @(productInfo.index);
    if(self.productItemModel.operationId){//运营位
        mutD[@"operation_rank"] = @(self.productItemModel.operationRank);
        mutD[@"operation_id"] = self.productItemModel.operationId;
    }
//   product_price 商品现价
    if (productInfo.actPt && productInfo.controlTitle.length == 0 && productInfo.actPt.assembleStatus.integerValue <= 1 && productInfo.available) {//拼团
        mutD[@"product_price"] = productInfo.actPt.assemblePrice;
    }else if (productInfo.actSk && productInfo.available){//秒杀
        mutD[@"product_price"] =  productInfo.actSk.skPrice;
    }else if (productInfo.actPgby){//批购包邮
        mutD[@"product_price"] = productInfo.actPgby.assemblePrice;
    }else{
        mutD[@"product_price"] = productInfo.fob;
    }
    if (productInfo.positionType) {//搜索坑位类型
        mutD[@"list_position_type"] = [NSString stringWithFormat:@"%@",productInfo.positionType];
    }
    if(productInfo.positionTypeName){
        mutD[@"list_position_typename"] = productInfo.positionTypeName;////搜索坑位类型名称
    }
    if(productInfo.searchSortStrategyCode){
        mutD[@"search_sort_strategy_id"] = productInfo.searchSortStrategyCode;//A/B策略ID
    }
    if(productInfo.productType){
        mutD[@"product_type"] = [NSString stringWithFormat:@"%@",productInfo.productType];;//商品类型
    }
    if (productInfo.productActivityType) {
        mutD[@"product_activity_type"] = [NSString stringWithFormat:@"%@",productInfo.productActivityType];//商品营销活动类型
    }
    mutD[@"key_word"] = productInfo.keyword;//搜索关键字
    mutD[@"sid"] = productInfo.sid;//sid追踪ID
    mutD[@"sptype"] = productInfo.sptype;//来源类型
    mutD[@"product_shop_name"] = productInfo.shopName;//商品店铺名称
    mutD[@"product_shop_code"] = productInfo.shopCode;//商品店铺编码
    mutD[@"product_name"] = productInfo.showName;//商品名称
    mutD[@"product_id"] = productInfo.ID;//商品ID
    mutD[@"product_first"] = [NSString stringWithFormat:@"%@",productInfo.categoryId];//商品一级分类
    
     UIViewController *targetVc = self.ybm_viewController;
     if ([targetVc respondsToSelector:@selector(queryModel)]) {
           YBMProductSearchDataModel *queryModel  = [targetVc valueForKey:@"queryModel"];
            mutD[@"result_cnt"] = @(queryModel.totalCount);
            mutD[@"page_no"] = @(queryModel.pageNo);
            mutD[@"page_size"] = @(queryModel.pageSize);
            mutD[@"total_page"] = @(queryModel.totalPage);
       }
     if ([targetVc respondsToSelector:@selector(searchJgspid)]) {
           mutD[@"jgspid"] = [targetVc valueForKey:@"searchJgspid"];
       }
  
//    UIViewController *viewController = self.ybm_viewController;
//    if([viewController isKindOfClass:[YBMSearchVC class]]){
//        YBMSearchVC *searchVC = (YBMSearchVC*) viewController;
//        mutD[@"result_cnt"] = @(searchVC.queryModel.totalCount);//列表总条数
//        mutD[@"page_no"] = @(searchVC.queryModel.pageNo);//当前页码
//        mutD[@"page_size"] = @(searchVC.queryModel.pageSize);//页面条数
//        mutD[@"total_page"] = @(searchVC.queryModel.totalPage);//总页数
//    }
    return  mutD;
}


#pragma mark - YBMPreferentialGiftViewDelegate
// 点击“更多赠品”
- (void)ybm_PreferentialGiftViewClickMoreCell:(YBMPreferentialGiftView *)preferentialGiftView withData:(YBMProductTagItemModel *)tagItemModel virtualSupplier:(BOOL)isVirtualSupplier {
    [self dismiss];
    // 跳转到“赠品池”
    [self showPreferentialGiftPoolVCWithData:tagItemModel virtualSupplier:isVirtualSupplier];
}

// 点击单个赠品
- (void)ybm_PreferentialGiftViewClickItemCell:(YBMPreferentialGiftView *)preferentialGiftView withSkuId:(NSString *)skuId {
    [YBMAnalyticsUtil track:kaction_groupPurchase_giftCard_Click properties:@{
        @"productId" : skuId
    }];
    [self dismiss];
}

// 单个赠品曝光
- (void)ybm_PreferentialGiftViewExpouseItemCell:(YBMPreferentialGiftView *)preferentialGiftView withSkuId:(NSString *)skuId {
    [YBMAnalyticsUtil track:kaction_groupPurchase_giftCard_Exposure properties:@{
        @"productId" : skuId
    }];
}

// 跳转到“赠品池”
- (void)showPreferentialGiftPoolVCWithData:(YBMProductTagItemModel *)model virtualSupplier:(BOOL)isVirtualSupplier {
    YBMPreferentialGiftPoolController *giftPoolVC = [[YBMPreferentialGiftPoolController alloc] init];
    giftPoolVC.tagItemModel = model;
    giftPoolVC.isVirtualSupplier = isVirtualSupplier;
    // show
    if (@available(iOS 16.0, *)) {
        if (giftPoolVC.sheetPresentationController) {
            UISheetPresentationController *sheet = giftPoolVC.sheetPresentationController;
            // 支持的自定义显示大小
            sheet.detents = @[
                [UISheetPresentationControllerDetent customDetentWithIdentifier:nil resolver:^CGFloat(id<UISheetPresentationControllerDetentResolutionContext> context) {
                    return kScreenHeight*0.6-kBottomSafeHeight; // 固定大小
                }],
            ];
            sheet.preferredCornerRadius = 4.f; // 展示时的圆角半径
        }
        [self.ybm_viewController presentViewController:giftPoolVC animated:YES completion:nil];
    } else {
        [self.ybm_viewController presentPanModal:giftPoolVC completion:nil];
    }
}

#pragma mark - 埋点

// 按钮曝光
- (void)auroraAnalysisExposureWithBtnName:(NSString *)btnName
{
    UIViewController *currentVC = self.ybm_viewController;
    NSMutableDictionary *mutD = [NSMutableDictionary dictionary];
    [YBMAnalyticsPages setReferrerWith:mutD controller:currentVC];
    mutD[@"$url"] = NSStringFromClass([currentVC class]);
    mutD[@"$url_domain"] = NSStringFromClass([currentVC class]);
    mutD[@"$title"] = [YBMAnalyticsPages returnTitleNameFrom:NSStringFromClass([currentVC class])];
    NSString *pageId = [YBMAnalyticsPages pageIdFromClass:currentVC];
    if ([pageId isEqualToString:@"sousuo"]) {
        mutD[@"page_id"] = @"sousuojieguoye";
        mutD[@"$title"] = @"搜索结果页";
    }else{
        mutD[@"page_id"] = pageId;
    }
    if (self.model.operationId) {
        mutD[@"operation_id"] = self.model.operationId;
        mutD[@"operation_rank"] = @(self.model.operationRank + 1);
        mutD[@"rank"] = @(self.model.rank + 1);
    }else{
        mutD[@"rank"] = @(self.model.rank + 1);
    }
    mutD[@"module"] = self.moduleStr;
    mutD[@"btn_name"] = btnName;
    mutD[@"btn_desc"] = @"底部弹窗";
    mutD[@"product_type"] = @"拼团";
    mutD[@"product_id"] = self.model.ID;
    mutD[@"rank"] = @(self.index + 1);
    mutD[@"navigation_2"] = self.model.floorName;
    mutD[@"navigation_1"] = self.model.navigation1;
    [AnalysysAgent track:btn_exposure properties:mutD];
}

// sureButton的埋点事件
- (void)auroraAnalysisWithSureButtonClick 
{
    NSMutableDictionary *mutDic = [NSMutableDictionary dictionary];
    if(self.isAnyGroupUI){
        mutDic[@"buttonType"] = @"3";
    }else{
        mutDic[@"buttonType"] = @"2";
    }
    
    [mutDic setValue:self.model.ID forKey:@"productId"];
    [mutDic setValue:@"3" forKey:@"productType"];
    [mutDic setValue:self.model.shopCode forKey:@"shop_code"];
    [mutDic setValue:self.model.sptype forKey:@"sptype"];
    [mutDic setValue:self.model.spid forKey:@"spid"];
    [YBMAnalyticsUtil track:kPintuaNnewFloatExposureButtonClick properties:mutDic];
    
    NSMutableDictionary *addlistDic = [NSMutableDictionary dictionary];
    addlistDic[@"sku_id"] = self.model.ID;
    addlistDic[@"sku_name"] = self.model.showName;
    addlistDic[@"sku_num"] = @(self.stepperButton.number);
    addlistDic[@"sku_price"] = self.model.fob;
    addlistDic[@"button"] = @"1";
    addlistDic[@"page_id"] = self.model.page_id;
    addlistDic[@"module"] = self.model.moduleType;
    addlistDic[@"offset"] = self.model.offset;
    [YBMAnalyticsUtil track:action_addlist_click properties:addlistDic];
//    NSMutableDictionary *mutD = [NSMutableDictionary dictionary];
//    NSString *page_id = [YBMAnalyticsPages pageIdFromClass:self.ybm_viewController];
//    NSTimeInterval time = [[NSDate date] timeIntervalSince1970];
//    if ([self.ybm_viewController isKindOfClass:[YBMProductDetailController class]]) {
//        mutD[@"module"] = @"商品详情页";
//    }else if([self.ybm_viewController isKindOfClass:[YBMSearchVC  class]]){
//        if (self.model.cardType == 2) {
//            //运营位
//            mutD[@"module"] =  @"搜索运营位";
//        }else{
//            mutD[@"module"] = @"搜索Feed流";
//        }
//        mutD[@"rank"] = @(self.model.offset.intValue+1);
//    }else if([self.ybm_viewController isKindOfClass:[XYYNewSearchVC  class]]){
//        mutD[@"module"] = @"搜索Feed流";
//        mutD[@"rank"] = @(self.model.offset.intValue+1);
//    }else{
//        mutD[@"module"] = @"商品列表";
//        mutD[@"rank"] = @(self.model.offset.intValue+1);
//    }
//    mutD[@"page_id"] = page_id;
//    mutD[@"$title"] = [YBMAnalyticsPages returnTitleNameFrom:NSStringFromClass([self.ybm_viewController class])];
//    mutD[@"addcarttime"] = @(time * 1000);
//    mutD[@"product_name"] = self.model.showName;
//    mutD[@"product_id"] = self.model.ID;
//    mutD[@"shop_id"] = self.model.shopCode;
//    mutD[@"shop_name"] = self.model.orgName;
//    mutD[@"operate_type"] = @"点击加购按钮";
//    if (self.ybm_viewController) {
//        UIViewController *frontVc = self.ybm_viewController;
//        if ([frontVc respondsToSelector:@selector(entrance)]) {
//            NSString *entrance = [frontVc valueForKey:@"entrance"];
//            if (entrance && entrance.length) {
//                mutD[@"entrance"] = entrance;
//            }
//        }
//    }
//    mutD[@"product_number"] = @(self.stepperButton.number);
//    mutD[@"product_present_price"] = self.model.assemblePrice;
//    [AnalysysAgent track:add_to_cart properties:mutD];
}

// suixin的埋点事件
- (void)auroraAnalysisWithSuixinButtonClick 
{
    NSMutableDictionary *mutDic = [NSMutableDictionary dictionary];
    mutDic[@"buttonType"] = @"4";
    
    [mutDic setValue:self.model.ID forKey:@"productId"];
    [mutDic setValue:@"3" forKey:@"productType"];
    [mutDic setValue:self.model.shopCode forKey:@"shop_code"];
    [mutDic setValue:self.model.sptype forKey:@"sptype"];
    [mutDic setValue:self.model.spid forKey:@"spid"];
    [YBMAnalyticsUtil track:kPintuaNnewFloatExposureButtonClick properties:mutDic];
    
    NSMutableDictionary *addlistDic = [NSMutableDictionary dictionary];
    addlistDic[@"sku_id"] = self.model.ID;
    addlistDic[@"sku_name"] = self.model.showName;
    addlistDic[@"sku_num"] = @(self.stepperButton.number);
    addlistDic[@"sku_price"] = self.model.fob;
    addlistDic[@"button"] = @"2";
    addlistDic[@"page_id"] = self.model.page_id;
    addlistDic[@"module"] = self.model.moduleType;
    addlistDic[@"offset"] = self.model.offset;
    [YBMAnalyticsUtil track:action_addlist_click properties:addlistDic];
//    NSMutableDictionary *mutD = [NSMutableDictionary dictionary];
//    NSString *page_id = [YBMAnalyticsPages pageIdFromClass:self.ybm_viewController];
//    NSTimeInterval time = [[NSDate date] timeIntervalSince1970];
//    if ([self.ybm_viewController isKindOfClass:[YBMProductDetailController class]]) {
//        mutD[@"module"] = @"商品详情页";
//    }else if([self.ybm_viewController isKindOfClass:[YBMSearchVC  class]]){
//        if (self.model.cardType == 2) {
//            //运营位
//            mutD[@"module"] =  @"搜索运营位";
//        }else{
//            mutD[@"module"] = @"搜索Feed流";
//        }
//        mutD[@"rank"] = @(self.model.offset.intValue+1);
//    }else if([self.ybm_viewController isKindOfClass:[XYYNewSearchVC  class]]){
//        mutD[@"module"] = @"搜索Feed流";
//        mutD[@"rank"] = @(self.model.offset.intValue+1);
//    }else{
//        mutD[@"module"] = @"商品列表";
//        mutD[@"rank"] = @(self.model.offset.intValue+1);
//    }
//    mutD[@"page_id"] = page_id;
//    mutD[@"$title"] = [YBMAnalyticsPages returnTitleNameFrom:NSStringFromClass([self.ybm_viewController class])];
//    mutD[@"addcarttime"] = @(time * 1000);
//    mutD[@"product_name"] = self.model.showName;
//    mutD[@"product_id"] = self.model.ID;
//    mutD[@"shop_id"] = self.model.shopCode;
//    mutD[@"shop_name"] = self.model.orgName;
//    mutD[@"operate_type"] = @"点击加购按钮";
//    if (self.ybm_viewController) {
//        UIViewController *frontVc = self.ybm_viewController;
//        if ([frontVc respondsToSelector:@selector(entrance)]) {
//            NSString *entrance = [frontVc valueForKey:@"entrance"];
//            if (entrance && entrance.length) {
//                mutD[@"entrance"] = entrance;
//            }
//        }
//    }
//    mutD[@"product_number"] = @(self.stepperButton.number);
//    mutD[@"product_present_price"] = self.model.assemblePrice;
//    [AnalysysAgent track:add_to_cart properties:mutD];
    
}

#pragma mark -QT埋点

/// 子模块点击事件
- (void)QTTrackActionSubModuleClickModel:(YBMProductAddBuyModel *)model btnIndex:(NSString *)btnIndex btnText:(NSString *)btnText {
    if (![self isTargetViewControllerAllowed]) {
        return;
    }
    
    //如果是运营位不上报
    if (self.productItemModel.cardType == 2) {
        return;
    }
    YBMCMSTrackDataModel *trackData = [[YBMCMSTrackDataModel alloc] init];
    YBMQTSpmModel *spmEntity = [[YBMQTSpmModel alloc] init];
    spmEntity.spmB = model.qtTrackData[@"spmB"];
    spmEntity.spmC = model.qtTrackData[@"spmC"];
    spmEntity.spmD = [NSString stringWithFormat:@"ftFloatProd@Z_btn@%@", btnIndex];
    trackData.spmEntity = spmEntity;
    
    YBMQTScmModel *scmEntity = [[YBMQTScmModel alloc]init];
    scmEntity.scmA = model.qtTrackData[@"scmA"];
    scmEntity.scmB = model.qtTrackData[@"scmB"] ?: @"0";
    scmEntity.scmC = model.qtTrackData[@"scmC"] ?: @"all_0";
    scmEntity.scmD = [NSString stringWithFormat:@"text-%@", btnText];
    NSString *scmE = model.qtTrackData[@"scmE"];
    scmEntity.scmE = scmE.length ? [NSString stringWithFormat:@"%@%@", scmE, [YBMQTTrack.sharedInstance generateRandomStrLength:6]] : nil;
    trackData.scmEntity = scmEntity;
    
    NSString *spm = [YBMQTTrack createSpmOrScmWithCmsModel:trackData trackType:YBMTrackTypeSPM];
    NSString *scm = [YBMQTTrack createSpmOrScmWithCmsModel:trackData trackType:YBMTrackTypeSCM];
    NSMutableDictionary *qtDic = NSMutableDictionary.new;
    qtDic[@"spm_cnt"] = spm;
    qtDic[@"scm_cnt"] = scm;
    [YBMQTTrack track:kQTActionSubModuleClick attributes:qtDic];
}

/// 商品按钮点击事件
- (void)QTTrackActionProductButtonClickModel:(YBMProductAddBuyModel *)model btnIndex:(NSString *)btnIndex btnText:(NSString *)btnText {
    if (![self isTargetViewControllerAllowed]) {
        return;
    }
    
    YBMCMSTrackDataModel *trackData = [[YBMCMSTrackDataModel alloc] init];
    YBMQTSpmModel *spmEntity = [[YBMQTSpmModel alloc] init];
    spmEntity.spmB = model.qtTrackData[@"spmB"];
    spmEntity.spmC = model.qtTrackData[@"spmC"];
//    spmEntity.spmD = [NSString stringWithFormat:@"prod@%ld_ftFloatProd@Z_btn@%@", model.rank, btnIndex];
    trackData.spmEntity = spmEntity;
    
    YBMQTScmModel *scmEntity = [[YBMQTScmModel alloc]init];
    scmEntity.scmA = model.qtTrackData[@"scmA"];
    scmEntity.scmB = model.qtTrackData[@"scmB"] ?: @"0";
    scmEntity.scmC = model.qtTrackData[@"scmC"] ?: @"all_0";
    scmEntity.scmD = [NSString stringWithFormat:@"prod-%@_text-%@", model.ID, btnText];
    NSString *scmE = model.qtTrackData[@"scmE"];
    scmEntity.scmE = scmE.length ? [NSString stringWithFormat:@"%@%@", scmE, [YBMQTTrack.sharedInstance generateRandomStrLength:6]] : nil;
    trackData.scmEntity = scmEntity;
    
    if (model.cardType != 2) {//1普通品
        spmEntity.spmD = [NSString stringWithFormat:@"prod@%ld_ftFloatProd@Z_btn@%@", model.rank, btnIndex];
    }else{//2运营位
        spmEntity.spmD = [NSString stringWithFormat:@"operation@%ld_prod@%ld-%ld_ftFloatProd@Z_btn@%@", model.rank, model.rank, model.operationRank, btnIndex];
        
        NSString *groupId = self.model.operationCustomerGroupId;
        groupId = [Tools filterStr:groupId];
        groupId = groupId.length > 0 ? groupId : @"all";
        
        NSString *exhibitionId = self.model.operationExhibitionId;
        exhibitionId = [Tools filterStr:exhibitionId];
        exhibitionId = exhibitionId.length > 0 ? exhibitionId : @"0";
        
        NSString *string = [NSString stringWithFormat:@"%@_%@",groupId, exhibitionId];
        scmEntity.scmC = string;
    }
    
    NSString *spm = [YBMQTTrack createSpmOrScmWithCmsModel:trackData trackType:YBMTrackTypeSPM];
    NSString *scm = [YBMQTTrack createSpmOrScmWithCmsModel:trackData trackType:YBMTrackTypeSCM];
    NSMutableDictionary *qtDic = NSMutableDictionary.new;
    qtDic[@"spm_cnt"] = spm;
    qtDic[@"scm_cnt"] = scm;
    qtDic[@"qt_sku_data"] = model.qtTrackData[@"qt_sku_data"];
    qtDic[@"qt_list_data"] = model.qtTrackData[@"qt_list_data"];
    qtDic[@"product_id"] = model.ID;
    qtDic[@"product_name"] = model.showName;
    [YBMQTTrack track:kQTActionProductButtonClick attributes:qtDic];
}

//验证支持埋点上报的类 12.1.0
- (BOOL)isTargetViewControllerAllowed {
    UIViewController *targetVc = self.ybm_viewController;
    //支持埋点上报的类
    NSArray<NSString *> *allowedClassNames = @[
        @"YBMSearchVC",
        @"XYYNewSearchVC",
        @"YBMOfftenBuyORSearchVC",
        @"YBMProductDetailController",
        @"YBMNewMeVC"
    ];

    for (NSString *className in allowedClassNames) {
        Class cls = NSClassFromString(className);
        if (cls && [targetVc isKindOfClass:cls]) {
            return YES;
        }
    }
    return NO;
}

#pragma mark - SETTER GETTER

- (void)setModel:(YBMProductAddBuyModel *)model {
    _model = model;
    [self setupControls];
    _titleLabel.text = model.showName?:@"";
    if (!kStringIsEmpty(model.spec)) { /// 规格
        _specLabel.text = [NSString stringWithFormat:@"规格:%@",model.spec?:@""];
    } else {
        _specLabel.text = @"";
        [self.inventoryLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(self.specLabel.mas_centerY);
            make.leading.mas_equalTo(self).offset(10);
            make.width.lessThanOrEqualTo(@(XYYScreenW/2));
        }];
    }
    if (model.availableQty.floatValue > 0) { /// 库存
        NSInteger count = [model.availableQty integerValue];
        _inventoryLabel.text = [NSString stringWithFormat:@"库存:%@",(count>100) ? [NSString stringWithFormat:@"大于100"] : model.availableQty];
    } else {
        _inventoryLabel.text = @"";
    }
    
    if (model.nearEffect.length > 0) {
        NSString *str = model.nearEffect;
        self.timeLabel.text = [NSString stringWithFormat:@"有效期：%@",str];
        [Tools setLabel:self.timeLabel font:[UIFont fontWithName:FontTypePingFangMe size:13] color:ColorFromHex(0x292933) strings:@[str]];
    }
    
    _buyNumLabel.text =  [NSString stringWithFormat:@"%zd%@起拼",model.skuStartNum.integerValue,model.productUnit];
    _stepperButton.number = model.skuStartNum.integerValue;
    _stepperButton.minNumber = self.model.skuStartNum.integerValue;
    
    // 弹窗弹起时，就要更新底部“购买此商品，整单均包邮”的提示
    [self requestAddCartTrialWithNumber:_stepperButton.number];
    
    //每加减一次，请求一次接口，显示返回的字段
    //    self.totalLab.attributedText = [self dealPrice:@125.00];
    
    
    self.addShopNum = @([model.skuStartNum integerValue]);
    _stepperButton.stepValue = model.mediumPackageNum.integerValue == 0 ? 1 : model.mediumPackageNum.integerValue;
    if (_model.isSplit.integerValue != 1) {
        _stepperButton.stepLessValue = model.mediumPackageNum.integerValue == 0 ? 1 : model.mediumPackageNum.integerValue;
    }
    
    //第一次时，请求拼团起拼合计金额
    if (!self.isLoad) {
        self.isLoad = YES;
        NSInteger count = model.skuStartNum.integerValue;// > 0 ? model.skuStartNum.integerValue : 1;
        [self stepper:self.stepperButton number:count increasing:YES];
    }
    //发起请求
    YBMPromInfoRequest* request = [YBMPromInfoRequest new];
    NSMutableDictionary* params = [[NSMutableDictionary alloc] init];
    params[@"csuId"] = model.ID;
    /************/
//    NSMutableArray* array = [NSMutableArray array];
//    YBMProductTagItemModel * m = [YBMProductTagItemModel new];
//    m.Description = @"11223";
//    m.uiStyle = @"2";
//    m.text = @"333333";
//    [array addObject:m];
//    [array addObject:m];
//
//    NSMutableArray*  carray = [NSMutableArray array];
//       YBMCSUDetailModel* detailModel = [YBMCSUDetailModel new];
//       detailModel.imageUrl = @"http://files.ybm100.com/INVT/Ykq/business/picture/2022-06-06/2d9b10a3a8bf4c4487c575114f4424b7.png";
//       detailModel.showName = @"133232424234234234234234";
//       detailModel.nearEffect = @"1111";
//       detailModel.farEffect = @"22222";
//
//   //    [array addObject:detailModel];
////       [carray addObject:detailModel];
////       [carray addObject:detailModel];
//       m.csuList = carray;
    
//    YBMPromInfoModel* mo = [YBMPromInfoModel new];
//    mo.tagList = array;
//
//    self.promInfoModel = mo;
////    [self addCSUInfo : array];
//
//    [self resetTableViewHeight];
//
//    [self.promTableView reloadData];
    
    
    request.addParams(params).serialize(YES). request(^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
        if(success){
            self.promInfoModel = ((YBMPromInfoRootModel*)response).data;
        }else{
            self.promInfoModel = nil;
        }
        [self resetTableViewHeight];
        [self.promTableView reloadData];
    });
}

- (void)setAllFreeShipStr:(NSString *)allFreeShipStr {
    _allFreeShipStr = allFreeShipStr;
    self.allFreeShipLabel.text = allFreeShipStr;
}

- (void)resetTableViewHeight{
    NSInteger height = 0;
    if (self.promInfoModel.tagList.count > 0){
        for (YBMProductTagItemModel *model in self.promInfoModel.tagList){
            CGFloat tempHeight = [self getDesclabelHeight:model.Description];
            height += tempHeight;
            // 如果有赠品，高度增加80
//            height += model.csuList.count * CSU_HEIGHT;
            if (model.csuList.count > 0) {
                height += 70;
            }
        }
        self.discountLab.hidden = NO;
    }
    
    CGFloat titleH = [self.titleLabel.text boundingRectWithSize:CGSizeMake(kScreenWidth -20, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:self.titleLabel.font} context:nil].size.height;
    height += titleH;
    
    if (height >= TABLE_MAX_HEIGHT){
        height = TABLE_MAX_HEIGHT;
    }
    [self.promTableView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(height);
    }];
    // 限量价格提醒
    CGFloat limitPriceHeight = 0;
    if (self.actPurchaseTip.length) {
        limitPriceHeight = [self getLimitPriceLabelHeight:self.actPurchaseTip];
    }
    
    CGFloat totalHeight = 220 + kBottomSafeHeight + limitPriceHeight;
    // 判断是否展示底部提示
    if (self.showAllFreeShip || self.showBottomShip) {
        self.allFreeShipView.hidden = NO;
        totalHeight += 40;
    } else {
        self.allFreeShipView.hidden = YES;
    }
    
    CGRect frame = CGRectMake(0, SCREEN_SIZE.height, SCREEN_SIZE.width, totalHeight);
    frame.size.height += height+15;
    frame.origin.y = kScreenHeight - frame.size.height - self.KeyboardHeight;
    [self show:frame];
}

// 获取赠品文案高度
- (CGFloat)getDesclabelHeight:(NSString *)descString {
    if (descString == nil || descString.length <= 0) {
        return 26.f;
    }
    CGSize constraint = CGSizeMake(SCREEN_SIZE.width - 100.f, CGFLOAT_MAX);
    CGSize size = [descString boundingRectWithSize:constraint options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName : [UIFont fontWithName:FontTypePingFangRe size:12]} context:nil].size;
    float fHeight = size.height + 2.0;
    return fHeight;
}

// 限量价格提醒高度
- (CGFloat)getLimitPriceLabelHeight:(NSString *)content {
    // 限量价格提醒文字高度
    UILabel *label = [[UILabel alloc] init];
    label.text = content;
    label.textAlignment = NSTextAlignmentLeft;
    label.font = [UIFont fontWithName:FontTypePingFangRe size:12.f];
    label.numberOfLines = 0;
    CGFloat labelH = [label sizeThatFits:CGSizeMake(kScreenWidth - 30, CGFLOAT_MAX)].height;
    CGFloat limitPriceHeight = labelH + 7.f + 20.f;
    
    return limitPriceHeight;
}

#pragma mark - UITableView
-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    YBMGroupPromoCell* cell = [tableView dequeueReusableCellWithIdentifier:@"YBMGroupPromoCell"];
    
    cell.giftView.isVirtualSupplier = _model.isVirtualSupplier;
    cell.giftView.giftDelegate = self;
    
    YBMProductTagItemModel*  model = self.promInfoModel.tagList[indexPath.row];
    cell.model = model;
    return cell;
}

-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    if (self.promInfoModel != nil && self.promInfoModel.tagList != nil) {
        return self.promInfoModel.tagList.count;
    } else {
        return 0;
    }
}

/*
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    NSArray<YBMCSUDetailModel*>*  model = self.promInfoModel.tagList[indexPath.row].csuList;
    return model == nil || model.count < 0 ? 20 : 100;
    return 90;
}
 */

///设置指定文字的字体、颜色
- (NSMutableAttributedString *)dealPrice:(NSNumber *)price{
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] init];
    if (price.stringValue.length > 0) {
        NSString *string = [NSString stringWithFormat:@"￥%.2f",price.floatValue];
        NSMutableAttributedString *priceString = [[NSMutableAttributedString alloc] initWithString:string];
        NSUInteger location = [string rangeOfString:@"."].location;
        NSRange range = NSMakeRange(1, location-1);
        
        NSMutableDictionary *dict = [[NSMutableDictionary alloc]initWithDictionary:@{
            NSFontAttributeName : [UIFont fontWithName:FontTypePingFangMe size:18],
            //            NSForegroundColorAttributeName : color,
        }];
        [priceString addAttributes:dict range:range];
        [attributedString appendAttributedString:priceString];
    }
    return attributedString;
}

#pragma mark - lazy
- (UIWindow *)backWindow {
    if (!_backWindow) {
        _backWindow = [[UIWindow alloc] initWithFrame: [UIScreen mainScreen].bounds];
        _backWindow.windowLevel = UIWindowLevelStatusBar;
        _backWindow.backgroundColor = [UIColor clearColor];
        _backWindow.hidden = NO;
    }
    return _backWindow;
}

- (UIView *)darkView {
    if (!_darkView) {
        _darkView = [UIView new];
        _darkView.userInteractionEnabled = NO;
        _darkView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.3];
    }
    return _darkView;
}

- (UIView *)bottomView {
    if (!_bottomView) {
        _bottomView = [UIView new];
        _bottomView.backgroundColor = UIColor.whiteColor;
        _bottomView.frame = CGRectMake(0, SCREEN_SIZE.height, SCREEN_SIZE.width, 220 + kBottomSafeHeight);
    }
    return _bottomView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.font = [UIFont fontWithName:FontTypePingFangMe size:16];
        _titleLabel.textColor = [UIColor colorWithHexString:@"#292933"];
        _titleLabel.numberOfLines = 2;
    }
    return _titleLabel;
}

- (UILabel *)specLabel {
    if (!_specLabel) {
        _specLabel = [UILabel new];
        _specLabel.font = [UIFont fontWithName:FontTypePingFangRe size:13];
        _specLabel.textColor = [UIColor colorWithHexString:@"#575766"];
        _specLabel.numberOfLines = 0;
        //        _specLabel.backgroundColor = UIColor.redColor;
    }
    return _specLabel;
}

- (UILabel *)inventoryLabel {
    if (!_inventoryLabel) {
        _inventoryLabel = [UILabel new];
        _inventoryLabel.font = [UIFont fontWithName:FontTypePingFangRe size:13];
        _inventoryLabel.textColor = [UIColor colorWithHexString:@"#575766"];
        _inventoryLabel.numberOfLines = 0;
        _inventoryLabel.textAlignment = NSTextAlignmentCenter;
        //        _inventoryLabel.backgroundColor = UIColor.orangeColor;
    }
    return _inventoryLabel;
}

- (UILabel *)timeLabel {
    if (!_timeLabel) {
        _timeLabel = [UILabel new];
        _timeLabel.font = [UIFont fontWithName:FontTypePingFangRe size:13];
        _timeLabel.textColor = [UIColor colorWithHexString:@"#575766"];
        _timeLabel.numberOfLines = 0;
        _timeLabel.text = @"有效期：-";
        //        _timeLabel.backgroundColor = UIColor.blueColor;
    }
    return _timeLabel;
}

- (UILabel *)priceLab{
    if (!_priceLab) {
        UILabel *lab = [[UILabel alloc]init];
        lab.font = [UIFont fontWithName:FontTypePingFangMe size:13];
        lab.textColor = ColorFromHex(0xFF2121);
//        lab.text = @"拼团价：--";
//        lab.backgroundColor = UIColor.greenColor;
        _priceLab = lab;
    }
    return _priceLab;
}

- (UILabel *)buyNumTitleLabel {
    if (!_buyNumTitleLabel) {
        _buyNumTitleLabel = [UILabel new];
        _buyNumTitleLabel.font = [UIFont fontWithName:FontTypePingFangRe size:13];
        _buyNumTitleLabel.textColor = [UIColor colorWithHexString:@"#575766"];
        _buyNumTitleLabel.numberOfLines = 0;
        _buyNumTitleLabel.text = @"购买数量:";
    }
    return _buyNumTitleLabel;
}

- (UILabel *)discountLab{
    if (!_discountLab) {
        UILabel *label = UILabel.new;
        label.text = @"优惠：";
        label.textColor = [UIColor colorWithHexString:@"#292933"];
        label.font = [UIFont fontWithName:FontTypePingFangRe size:12];
        label.hidden = YES;
        _discountLab = label;
    }
    return _discountLab;
}

-(UITableView *) promTableView{
    if(!_promTableView){
        UITableView *tab = [[UITableView alloc] init];
        tab.estimatedRowHeight = 20;
        tab.estimatedSectionFooterHeight = 0;
        tab.estimatedSectionHeaderHeight = 0;
        tab.backgroundColor = [UIColor whiteColor];
        tab.delegate = self;
        tab.dataSource = self;
        tab.separatorStyle = UITableViewCellSeparatorStyleNone;
        tab.bounces = NO;
        tab.allowsSelection = NO;
        [tab registerClass:[YBMGroupPromoCell class] forCellReuseIdentifier:@"YBMGroupPromoCell"];
//        tab.backgroundColor = UIColor.orangeColor;
                
        _promTableView = tab;
    }
    return _promTableView;
}

- (UILabel *)buyNumLabel {
    if (!_buyNumLabel) {
        _buyNumLabel = [UILabel new];
        _buyNumLabel.font = [UIFont fontWithName:FontTypePingFangRe size:12];
        _buyNumLabel.textColor = [UIColor colorWithHexString:@"#9494A6"];
        _buyNumLabel.numberOfLines = 0;
    }
    return _buyNumLabel;
}

- (UILabel *)totalLab{
    if (!_totalLab) {
        _totalLab = [[UILabel alloc]init];
        _totalLab.font = [UIFont systemFontOfSize:13];
        _totalLab.textColor = ColorFromHex(0xFF2121);
        NSMutableDictionary *dict = [[NSMutableDictionary alloc]initWithDictionary:@{
            NSFontAttributeName : [UIFont fontWithName:FontTypePingFangMe size:18],
        }];
        NSAttributedString *string = [[NSAttributedString alloc]initWithString:@"--" attributes:dict];
        _totalLab.attributedText = string;
    }
    return _totalLab;
}

- (UILabel *)totalTitleLab{
    if (!_totalTitleLab) {
        UILabel *lab = [[UILabel alloc]init];
        lab.font = [UIFont systemFontOfSize:13];
        lab.textColor = ColorFromHex(0x575766);
        lab.text = @"合计：";
        _totalTitleLab = lab;
    }
    return _totalTitleLab;
}

- (UIButton *)sureButton {
    if (!_sureButton) {
        _sureButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_sureButton setTitle:@"确定" forState:UIControlStateNormal];
        [_sureButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        _sureButton.titleLabel.font = [UIFont fontWithName:FontTypePingFangMe size:16];
        _sureButton.backgroundColor = [UIColor colorWithHexString:@"#00B377"];
        [_sureButton addTarget:self action:@selector(sureActionButton:) forControlEvents:UIControlEventTouchUpInside];
        _sureButton.layer.cornerRadius = 2;
        _sureButton.clipsToBounds = YES;
    }
    return _sureButton;
}


- (UIButton *)suixinButton {
    if (!_suixinButton) {
        _suixinButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_suixinButton setTitle:@"随心拼" forState:UIControlStateNormal];
        [_suixinButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        _suixinButton.titleLabel.font = [UIFont fontWithName:FontTypePingFangMe size:16];
        _suixinButton.backgroundColor = [UIColor colorWithHexString:@"#00B377"];
        [_suixinButton addTarget:self action:@selector(suixinClick) forControlEvents:UIControlEventTouchUpInside];
        _suixinButton.layer.cornerRadius = 2;
    }
    return _suixinButton;
}

- (UIImageView *)discountImgView{
    if (!_discountImgView) {
        UIImageView *imgView = [[UIImageView alloc]init];
        imgView.image = [UIImage imageNamed:@"detail_chat"];
        imgView.hidden = YES;
        [imgView addSubview:self.popLab];
        [self.popLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.insets(UIEdgeInsetsMake(0, 2, 3, 2));
        }];
        _discountImgView = imgView;
    }
    return _discountImgView;
}

- (UILabel *)popLab{
    if (!_popLab) {
        UILabel *lab = [[UILabel alloc]init];
        lab.textColor = UIColor.whiteColor;
        lab.font = [UIFont boldSystemFontOfSize:11];
        lab.textAlignment = NSTextAlignmentCenter;
        lab.hidden = YES;
//        lab.text = @"89折";
        _popLab = lab;
    }
    return _popLab;
}

- (UIButton *)addToCartButton {
    if (!_addToCartButton) {
        _addToCartButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_addToCartButton setTitle:@"加入购物车" forState:UIControlStateNormal];
        [_addToCartButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        _addToCartButton.titleLabel.font = [UIFont fontWithName:FontTypePingFangMe size:16];
        _addToCartButton.backgroundColor = [UIColor colorWithHexString:@"#00B955"];
        [_addToCartButton addTarget:self action:@selector(addToCartButtonClick:) forControlEvents:UIControlEventTouchUpInside];
        _addToCartButton.layer.cornerRadius = 4.f;
    }
    return _addToCartButton;
}

// 同笔订单享受包邮
- (UIView *)allFreeShipView {
    if (!_allFreeShipView) {
        _allFreeShipView = [[UIView alloc] init];
        _allFreeShipView.backgroundColor = [UIColor colorWithHexString:@"#FFF6F6"];
        _allFreeShipView.hidden = YES;
    }
    return _allFreeShipView;
}

- (UILabel *)allFreeShipLabel {
    if (!_allFreeShipLabel) {
        _allFreeShipLabel = [[UILabel alloc] init];
        _allFreeShipLabel.text = @"购买此商品，同笔订单商品均享受包邮";
        _allFreeShipLabel.font = [UIFont fontWithName:FontTypePingFangMe size:13];
        _allFreeShipLabel.textColor = [UIColor colorWithHexString:@"#FF2121"];
        _allFreeShipLabel.textAlignment = NSTextAlignmentCenter;
        _allFreeShipLabel.numberOfLines = 1;
    }
    return _allFreeShipLabel;
}

- (UIImageView *)allFreeShipIcon {
    if (!_allFreeShipIcon) {
        _allFreeShipIcon = [[UIImageView alloc] init];
        _allFreeShipIcon.image = [UIImage imageNamed:@"add_cart_free_ship"];
        _allFreeShipIcon.contentMode = UIViewContentModeScaleAspectFill;
        _allFreeShipIcon.clipsToBounds = YES;
    }
    return _allFreeShipIcon;
}

- (YBMStepperButton *)stepperButton {
    if (!_stepperButton) {
        _stepperButton = [YBMStepperButton new];
        UIColor *color = [UIColor colorWithHexString:@"#999999"];
        _stepperButton.borderColor = [UIColor whiteColor];
        _stepperButton.borderWidth = 0.5;
        _stepperButton.cornerRadius = 2.f;
        _stepperButton.increaseTitle = @"＋";
        _stepperButton.increaseTitleColor = color;
        _stepperButton.reduceTitle = @"－";
        _stepperButton.reduceTitleColor = color;
        _stepperButton.reduceDisableTitleColor = UIColor.grayColor;
        _stepperButton.number = 0;
        _stepperButton.autoHideReduce = NO;
        _stepperButton.delegate = self;
    }
    return _stepperButton;
}

- (YBMEventButton *)closeButton {
    if (!_closeButton) {
        _closeButton = [YBMEventButton buttonWithType:UIButtonTypeCustom];
        [_closeButton setImage:[UIImage imageNamed:@"nav_close_gray"] forState:UIControlStateNormal];
        [_closeButton addTarget:self action:@selector(closeActionButton) forControlEvents:UIControlEventTouchUpInside];
    }
    return _closeButton;
}

- (UIView *)limitedPriceView {
    if (!_limitedPriceView) {
        _limitedPriceView = [[UIView alloc] init];
        _limitedPriceView.clipsToBounds = YES;
        _limitedPriceView.backgroundColor = [UIColor colorWithHexString:@"#FFF7E9"];
    }
    return _limitedPriceView;
}

- (UILabel *)limitedPriceLabel {
    if (!_limitedPriceLabel) {
        _limitedPriceLabel = [[UILabel alloc] init];
        _limitedPriceLabel.textAlignment= NSTextAlignmentLeft;
        _limitedPriceLabel.text = @"";
        _limitedPriceLabel.font = [UIFont fontWithName:FontTypePingFangRe size:12.f];
        _limitedPriceLabel.textColor = [UIColor colorWithHexString:@"#333333"];
        _limitedPriceLabel.numberOfLines = 0;
    }
    return _limitedPriceLabel;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}


@end
