//
//  YBMProductAddView.m
//  YaoBangMang
//
//  Created by ZB on 2023/9/1.
//  Copyright © 2023 XiaoYaoYao.Ltd. All rights reserved.
//

#import "YBMProductAddView.h"
#import "YBMStepperButton.h"
#import "YBMProductUtils.h"
#import "YBMInsetLabel.h"
#import "ZBHUD.h"
#import "YBMProductDetailController.h"
#import "YBMLabel.h"
#import "YBMSearchVC.h"
#import "XYYNewSearchVC.h"

/// 屏幕尺寸
#define SCREEN_SIZE [UIScreen mainScreen].bounds.size

@interface YBMProductAddView ()<YBMStepperButtonDelegate>
@property (nonatomic, strong) UIWindow *backWindow;
@property (nonatomic, strong) UIView *darkView;// 灰色背景
@property (nonatomic, strong) UIView *contentView;// 内容view
@property (nonatomic, strong) UIView *manJiangView;// 满降view
@property (nonatomic, strong) YBMLabel *tagLab;//标签lab
@property (nonatomic, strong) UILabel *descLab;//标签后面的描述信息

/// 商品图
@property (nonatomic, strong) UIImageView *productImageView;
/// 卖点标签
@property (nonatomic, strong) UIImageView *sellingPointImageView;
/// 商品名称
@property (nonatomic, strong) UILabel *nameLab;
/// 凑单提示
@property (nonatomic, strong) YBMInsetLabel *tipsLab;
@property (nonatomic, strong) UILabel *inventoryLab;//库存
@property (nonatomic, strong) UILabel *timeLabel;//有效期
@property (nonatomic, strong) UILabel *buyNumtitleLab;//购买数量
@property (nonatomic, strong) UIButton *sureButton;//确认按钮
@property (nonatomic, strong) YBMStepperButton *stepperButton;//加购按钮
@property (strong, nonatomic) UILabel *discountLab;//折后价
@property (strong, nonatomic) UILabel *priceLab;//拼团价
@property (strong, nonatomic) UILabel *totalTitleLab;//合计
@property (strong, nonatomic) UILabel *totalLab;//合计

@property (assign, nonatomic) BOOL isLoad;

@end

@implementation YBMProductAddView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setupControls];
    }
    return self;
}

- (void)setupControls {
    [self addSubview:self.darkView];
    [self addSubview:self.contentView];
    [self setFrame:(CGRect){0, 0, SCREEN_SIZE}];
    [self.backWindow addSubview:self];
    [self.contentView addSubview:self.productImageView];
//    [self.contentView addSubview:self.sellingPointImageView];
    [self.contentView addSubview:self.nameLab];
    [self.contentView addSubview:self.tipsLab];
    [self.contentView addSubview:self.inventoryLab];
    [self.contentView addSubview:self.timeLabel];
    [self.contentView addSubview:self.stepperButton];
    [self.contentView addSubview:self.priceLab];
    [self.contentView addSubview:self.buyNumtitleLab];
    [self.contentView addSubview:self.totalTitleLab];
    [self.contentView addSubview:self.sureButton];
    [self.contentView addSubview:self.discountLab];
    [self.contentView addSubview:self.totalLab];

    [self.darkView setFrame:(CGRect){0, 0, SCREEN_SIZE}];
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(dismiss)];
    [self.darkView addGestureRecognizer:tap];
    //商品图片
    [self.productImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.contentView).offset(12);
        make.leading.mas_equalTo(self.contentView).offset(12);
        make.height.width.mas_equalTo(77);
    }];
    //商品名称/规格
    [self.nameLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.contentView).offset(10);
        make.leading.mas_equalTo(self.productImageView.mas_trailing).offset(10);
        make.trailing.mas_equalTo(self.contentView).offset(-10);
    }];
    //有效期
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.nameLab.mas_bottom).offset(2);
        make.leading.mas_equalTo(self.nameLab);
    }];
    //价格
    [self.priceLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.productImageView.mas_bottom).offset(5);
        make.leading.mas_equalTo(self.nameLab);
    }];
    //折后价
    [self.discountLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.priceLab.mas_bottom).offset(-5);
        make.leading.mas_equalTo(self.priceLab.mas_trailing).offset(8);
    }];
    //库存
    [self.inventoryLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.discountLab.mas_centerY);
        make.trailing.mas_equalTo(self.contentView.mas_trailing).offset(-10);
    }];
    
    UIView *line = [[UIView alloc]init];
    line.backgroundColor = ColorFromHex(0xEEEEEE);
    [self.contentView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.productImageView.mas_bottom).offset(12);
        make.leading.mas_equalTo(self.contentView).offset(10);
        make.trailing.mas_equalTo(self.contentView).offset(-10);
        make.height.mas_equalTo(0.5);
    }];
    
    //---------------------------------------------------------------------
    //确定
    [self.sureButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.contentView).offset(10);
        make.trailing.mas_equalTo(self.contentView).offset(-10);
        make.height.mas_equalTo(44);
        make.bottom.offset(-10-kBottomSafeHeight);
    }];
    //凑单提示
    [self.tipsLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.contentView).offset(10);
        make.trailing.mas_equalTo(self.contentView).offset(-10);
        make.bottom.mas_equalTo(self.sureButton.mas_top);
    }];
    
    //加购
    [self.stepperButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.totalTitleLab.mas_top).offset(-10);
        make.trailing.mas_equalTo(self).offset(-10);
        make.size.mas_equalTo(CGSizeMake(90, 30));
    }];
    //购买数量
    [self.buyNumtitleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.stepperButton.mas_centerY);
        make.leading.mas_equalTo(self.contentView).offset(10);
    }];

    //合计：
    [self.totalTitleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.tipsLab.mas_top).offset(-10);
        make.leading.mas_equalTo(10);
    }];
    [self.totalLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.totalTitleLab.mas_trailing);
        //        make.bottom.mas_equalTo(lab.mas_bottom);
        make.centerY.mas_equalTo(self.totalTitleLab.mas_centerY).offset(-3);
    }];
    
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillShow:) name:UIKeyboardWillShowNotification object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillHide:) name:UIKeyboardWillHideNotification object:nil];
}

- (void)keyboardWillShow:(NSNotification *)notification {
    NSDictionary *userInfo = [notification userInfo];
    NSValue *aValue = [userInfo objectForKey:UIKeyboardFrameEndUserInfoKey];
    CGRect keyboardRect = [aValue CGRectValue];
    
    CGRect frame = self.contentView.frame;
    frame.origin.y = SCREEN_SIZE.height - (keyboardRect.size.height + frame.size.height) + 100;//增加100Pt使键盘遮挡住确定按钮，防止跳过完成进入购物车
    self.sureButton.hidden = YES;
    self.contentView.frame = frame;
}

- (void)keyboardWillHide:(NSNotification *)notification {
    CGRect frame = self.contentView.frame;
    frame.origin.y = SCREEN_SIZE.height - frame.size.height;
    self.contentView.frame = frame;
    self.sureButton.hidden = NO;
}

#pragma mark - SETTER GETTER
- (void)setModel:(YBMProductItemModel *)model {
    _model = model;
    
    //商品图片
    if (model.imageUrl.length > 0) {
        NSString *imageURLString = model.imageUrl;
        if (![model.imageUrl hasPrefix:@"http"]) {
            imageURLString = [[XYYSmallImagUrl stringByAppendingString:model.imageUrl] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
        }
        [_productImageView sd_setImageWithURL:[NSURL URLWithString:imageURLString] placeholderImage:[UIImage imageNamed:@"placeholderImage"]];
    }
    
    // 卖点标签，后台暂未完全改造完，完成后统一使用model.tags.markerTag
//    NSString *markerUrl = model.tags.markerTag.length ? model.tags.markerTag : model.markerUrl;
//    if (markerUrl.length > 0) {
//        _sellingPointImageView.hidden = NO;
//        if (![markerUrl hasPrefix:@"http"]) {
//            markerUrl = [[XYYManageImg stringByAppendingString:markerUrl] stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
//        }
//        [_sellingPointImageView sd_setImageWithURL:[NSURL URLWithString:markerUrl] placeholderImage:nil];
//    } else {
//        _sellingPointImageView.hidden = YES;
//    }
    
    // 商品名称/规格
    NSString *showName = [NSString stringWithFormat:@"%@/%@",model.showName, model.spec];
    self.nameLab.text = showName;
    
    // 先隐藏折后价
    self.discountLab.text = @"";
    // 价格
    if (model.levelPriceDTO) {
        self.priceLab.attributedText = [Tools formatPriceStr:model.levelPriceDTO.startingPriceShowText];
    }else{
        self.priceLab.attributedText = [YBMProductUtils dealPrice:model.fob prefix:model.pricePrefix];
        //折后约
        if (model.discountPrice.length > 0) {
            self.discountLab.text = model.discountPrice;
        }
    }
    
    //有效期
    if (model.nearEffect.length > 0) {
        self.timeLabel.text = [NSString stringWithFormat:@"有效期至：%@",model.nearEffect];
    }
    
    // 库存
    if (model.availableQty.floatValue > 0) {
        NSInteger count = [model.availableQty integerValue];
        self.inventoryLab.text = [NSString stringWithFormat:@"库存:%@",(count>100) ? [NSString stringWithFormat:@"大于100"] : model.availableQty];
    } else {
        self.inventoryLab.text = @"";
    }
    
    //合计金额，每加减一次，请求一次接口，显示返回的字段
    self.totalLab.attributedText = [YBMProductUtils dealPrice:model.fob prefix:model.pricePrefix];
    
    NSInteger value = model.mediumPackageNum.integerValue == 0 ? 1 : model.mediumPackageNum.integerValue;
    self.stepperButton.stepValue = value;
    if (_model.isSplit.integerValue != 1) {
        _stepperButton.stepLessValue = model.mediumPackageNum.integerValue == 0 ? 1 : model.mediumPackageNum.integerValue;
    }
}

#pragma mark - YBMStepperButtonDelegate
- (void)stepper:(YBMStepperButton *)stepper number:(NSUInteger)number increasing:(BOOL)increasing {
    YBMLog(@"number: %d",number);
    NSNumber *totalAmount = @(number * self.model.fob.floatValue);
    self.totalLab.attributedText = [YBMProductUtils dealPrice:totalAmount prefix:@""];
    [self requestAddCartTrialWithNumber:number];
}

//商详加购，运费tips带标签
- (void)requestAddCartTrialWithNumber:(NSUInteger)number{
    [XYYHttpApi cancelSessionTask];
    [ZBHUD showLoadingWithEnableUserInteraction:YES];
    
    NSMutableDictionary *dic = [NSMutableDictionary new];
    dic[@"skuId"] = self.model.ID;
    dic[@"quantity"] = @(number);//加购数量
    dic[@"orgId"] = self.model.orgId;//商品所在店铺的orgId
    dic[@"productPrice"] = self.model.fob;//商品单价
    dic[@"productType"] = self.model.productType;
    dic[@"freeShippingFlag"] = @(self.freeShippingFlag);
    NSMutableDictionary *tempJG = [NSMutableDictionary dictionary];
    tempJG[@"key_word"] = self.model.keyword;
    if (self.model.positionType) {
        tempJG[@"list_position_type"] = [NSString stringWithFormat:@"%@",self.model.positionType];
    }
    tempJG[@"list_position_typename"] = self.model.positionTypeName;
    if (self.model.operationId) {
        tempJG[@"operation_id"] = self.model.operationId;
        tempJG[@"operation_rank"] = @(self.model.operationRank + 1);
    }
    tempJG[@"rank"] = @(self.model.index + 1);
    tempJG[@"search_sort_strategy_id"] = self.model.searchSortStrategyCode;
    tempJG[@"product_activity_type"] = [NSString stringWithFormat:@"%@",self.model.productActivityType];//商品营销活动类型。例："1"，"2"，"3"，"4"，"5"
    UIViewController *frontVc = self.ybm_viewController;
    if ([frontVc respondsToSelector:@selector(entrance)]) {
        NSString *entrance = [frontVc valueForKey:@"entrance"];
        if (entrance && entrance.length) {
            tempJG[@"entrance"] = entrance;
        }
    }
    if ([frontVc respondsToSelector:@selector(activityEntrance)]) {
        NSString *activityEntrance = [frontVc valueForKey:@"activityEntrance"];
        if (activityEntrance && activityEntrance.length) {
            tempJG[@"activity_entrance"] = activityEntrance;
        }
    }
    
    
    if ([frontVc isKindOfClass:[YBMProductDetailController class]]) {
        YBMProductDetailController *detailVc = (YBMProductDetailController *)frontVc;
        tempJG[@"page_type"] = detailVc.pageType;
        tempJG[@"page_id"] = detailVc.pageId;
        tempJG[@"page_name"] = detailVc.pageName;
        tempJG[@"component_position"] = detailVc.componentPosition;
        tempJG[@"component_name"] = detailVc.componentName;
        tempJG[@"component_title"] = detailVc.componentTitle;
    }
    
    
    NSString *sessionId = [[AnalysysAgent getPresetProperties] objectForKey:@"$session_id"];
    tempJG[@"$session_id"] = sessionId;
    dic[@"mddata"] = tempJG.mj_JSONString;
    @weakify(self);
    [XYYHttpApi postUrl:API_Interface(@"/app/addCartTrial") Param:dic success:^(NSDictionary *resultData) {
        @strongify(self);
        [ZBHUD dismiss];
        NSNumber *totalAmount = resultData[@"totalAmount"];
        NSString *freightTips = resultData[@"freightTips"];
        NSString *message = resultData[@"message"];
        if (totalAmount.floatValue > 0){
            self.totalLab.attributedText = [YBMProductUtils dealPrice:totalAmount prefix:@""];
        }
        
        if (freightTips.length){
            NSAttributedString *attrStr = [[NSAttributedString alloc] initWithData:[freightTips dataUsingEncoding:NSUnicodeStringEncoding] options:@{NSDocumentTypeDocumentAttribute:NSHTMLTextDocumentType} documentAttributes:nil error:nil];
            self.tipsLab.attributedText = attrStr;
            self.tipsLab.textAlignment = NSTextAlignmentCenter;
            self.tipsLab.contentInset = UIEdgeInsetsMake(10, 10, 10, 10); // 设置左内边距
//            self.tipsLab.font = [UIFont systemFontOfSize:12];
        }else{
            self.tipsLab.text = @"";
            self.tipsLab.contentInset = UIEdgeInsetsZero; // 设置左内边距
        }
        if (message.length) {
            MBShowTextNoIcon(message);
        }
    } failure:^(NSDictionary *resultData) {
        YBMLog(@"请求: %d",number);
        [ZBHUD dismiss];
    }];
}

#pragma mark -  TOUCH
- (void)showWithCount:(NSInteger)count {
    // 如果是阶梯价才展示满降信息
    if (self.model.levelPriceDTO) {
        CGRect frame = self.contentView.frame;
        frame.size.height += 40;
        self.contentView.frame = frame;
        
        // 满降信息
        [self.contentView addSubview:self.manJiangView];
        [self.manJiangView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(102);
            make.leading.trailing.mas_equalTo(self.contentView);
            make.height.mas_equalTo(40);
        }];
        self.tagLab.itemModel = self.model.tags.manJiangTag;
        self.descLab.text = self.model.tags.manJiangTag.Description;
    }
    
    self.backWindow.hidden = NO;
    [UIView animateWithDuration:0.3 delay:0 options:UIViewAnimationOptionCurveEaseOut animations:^{
        self.darkView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.3];
        self.darkView.userInteractionEnabled = YES;
        CGRect frame = self.contentView.frame;
        frame.origin.y -= frame.size.height;
        self.contentView.frame = frame;
    } completion:^(BOOL finished) {
    }];
    
    //第一次时，请求接口合计金额
    count = count > 0 ? count : self.model.mediumPackageNum.integerValue;
    self.stepperButton.number = count;
    [self stepper:self.stepperButton number:count increasing:YES];
}

- (void)dismiss {
    [UIView animateWithDuration:0.3 delay:0 options:UIViewAnimationOptionCurveEaseOut animations:^{
        self.darkView.userInteractionEnabled = NO;
        self.darkView.backgroundColor = [UIColor colorWithWhite:0 alpha:0];
        CGRect frame = self.contentView.frame;
        frame.origin.y = kScreenHeight;
        self.contentView.frame = frame;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

//确定按钮点击
- (void)sureActionButton {
    
    //按钮点击埋点
    [self trackProductButtonClick:@"确定"];
    //加入购物车埋点
    [self trackProductAddToCart];
    
    if (self.block){
        self.block(self.stepperButton.number);
        [self dismiss];
    }
    
    //加入采购单埋点
//    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
//    dic[@"commodityId"] = self.model.ID;
//    dic[@"sid"] = self.model.sid;
//    dic[@"sptype"] = self.model.sptype;
//    dic[@"spid"] = self.model.spid;
//    [YBMAnalyticsUtil track:@"action_AddShoppingCart" properties:dic];
//    
//
//    NSMutableDictionary *addlistDic = [NSMutableDictionary dictionary];
//    addlistDic[@"sku_id"] = self.model.ID;
//    addlistDic[@"sku_name"] = self.model.showName;
//    addlistDic[@"sku_num"] = @(self.stepperButton.number);
//    addlistDic[@"sku_price"] = self.model.fob;
//    addlistDic[@"button"] = @"3";
//    addlistDic[@"page_id"] = self.model.page_id;
//    addlistDic[@"module"] = self.model.moduleType;
//    addlistDic[@"offset"] = [NSString stringWithFormat:@"%ld",self.model.index];
//    [YBMAnalyticsUtil track:action_addlist_click properties:addlistDic];
//        NSMutableDictionary *mutD = [NSMutableDictionary dictionary];
//        NSString *page_id = [YBMAnalyticsPages pageIdFromClass:self.ybm_viewController];
//        NSTimeInterval time = [[NSDate date] timeIntervalSince1970];
//    if ([self.ybm_viewController isKindOfClass:[YBMProductDetailController class]]) {
//        mutD[@"module"] = @"商品详情页";
//    }else{
//        mutD[@"module"] = @"商品列表";
//    }
//        mutD[@"page_id"] = page_id;
//        mutD[@"$title"] = self.ybm_viewController.title;
//        mutD[@"addcarttime"] = @(time);
//        mutD[@"product_name"] = self.model.showName;
//        mutD[@"product_id"] = self.model.ID;
//        mutD[@"shop_id"] = self.model.shopCode;
//        mutD[@"shop_name"] = self.model.shopName;
//        mutD[@"rank"] = @(self.model.offset.intValue+1);
//        mutD[@"operate_type"] = @"点击加购按钮";
//        mutD[@"entrance"] = NSStringFromClass([self.ybm_viewController  class]);
//        mutD[@"product_number"] = @(self.stepperButton.number);
//        mutD[@"product_present_price"] = self.model.fob;
//        [AnalysysAgent track:add_to_cart properties:mutD];
    

}

//直睿新埋点 商品按钮点击埋点
- (void)trackProductButtonClick:(NSString*)btnName{

    NSMutableDictionary *mutD;
    UIViewController *targetVc = self.ybm_viewController;
    if ([targetVc isKindOfClass:[YBMProductDetailController class]]) {

        mutD = @{}.mutableCopy;
        YBMProductDetailController *productDetailVc = (YBMProductDetailController*)self.ybm_viewController;
        [mutD addEntriesFromDictionary:productDetailVc.productDetailTrackDict];
        mutD[@"btn_desc"] = @"商详页底部弹窗";//"列表页"，"列表页底部弹窗"，"商详页"，"商详页底部弹窗
        mutD[@"product_number"] = @(self.stepperButton.number);//商品数量
        mutD[@"btn_name"] = btnName;//："立即参团"、"去抢购"、"加入购物车"
        // 极光埋点已移除 - action_product_button_click (商品详情页底部弹窗)
        // [AnalysysAgent track:kAction_product_button_click properties:mutD];
        return;

    }else if([targetVc isKindOfClass:[YBMSearchVC  class]] || [targetVc isKindOfClass:[XYYNewSearchVC  class]]){

        mutD = [self getTrackCommonParams];
        mutD[@"btn_desc"] = @"列表页底部弹窗";//"列表页"，"列表页底部弹窗"，"商详页"，"商详页底部弹窗
        mutD[@"direct"] = @"1";//0"-其他；"1"-列表页直接加购提单；"2"-详情页加购提单；"3"-购物车页面加购；"4"-订单再次购买加购

    }else{//其他来源 后面在这里加
        mutD = @{}.mutableCopy;
        mutD[@"product_number"] = @(self.stepperButton.number);//商品数量
        mutD[@"btn_name"] = btnName;//："立即参团"、"去抢购"、"加入购物车"
        // 极光埋点已移除 - action_product_button_click (其他页面)
        // [AnalysysAgent track:kAction_product_button_click properties:mutD];
        return;
    }
    mutD[@"product_number"] = @(self.stepperButton.number);//商品数量
    mutD[@"btn_name"] = btnName;//："立即参团"、"去抢购"、"加入购物车"
//    NSLog(@"%@-------",mutD);
    // 保留搜索页面的极光埋点
    [AnalysysAgent track:kAction_product_button_click properties:mutD];

}

//直睿新的列表商品加入购物车埋点
- (void)trackProductAddToCart{
   
    NSMutableDictionary *mutD;
    UIViewController *targetVc = self.ybm_viewController;
    if ([targetVc isKindOfClass:[YBMProductDetailController class]]) {
        mutD = @{}.mutableCopy;
        YBMProductDetailController *productDetailVc = (YBMProductDetailController*)targetVc;
        [mutD addEntriesFromDictionary:productDetailVc.productDetailTrackDict];
    }else if([targetVc isKindOfClass:[YBMSearchVC  class]] || [targetVc isKindOfClass:[XYYNewSearchVC  class]]){
        
        mutD = [self getTrackCommonParams];
        mutD[@"direct"] = @"1";//0"-其他；"1"-列表页直接加购提单；"2"-详情页加购提单；"3"-购物车页面加购；"4"-订单再次购买加购
       
    }else{//其他来源 后面在这里加
        mutD = @{}.mutableCopy;
    }
    
    mutD[@"product_number"] = @(self.stepperButton.number);//商品数量
    NSDate *currentDate = [NSDate date];
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
    mutD[@"add_cart_time"] = [dateFormatter stringFromDate:currentDate];;//加购时间。
    
//    NSLog(@"%@-------",mutD);
    [AnalysysAgent track:kAdd_to_cart properties:mutD];
}
 
- (NSMutableDictionary*)getTrackCommonParams{
    NSMutableDictionary *mutD = [NSMutableDictionary dictionary];
    YBMProductItemModel *productInfo = self.model;
    mutD[@"rank"] = @(self.model.index);
    if(productInfo.operationId){//运营位
        mutD[@"operation_rank"] = @(productInfo.operationRank);
        mutD[@"operation_id"] = productInfo.operationId;
    }
//   product_price 商品现价
    if (productInfo.actPt && productInfo.controlTitle.length == 0 && productInfo.actPt.assembleStatus.integerValue <= 1 && productInfo.available) {//拼团
        mutD[@"product_price"] = productInfo.actPt.assemblePrice;
    }else if (productInfo.actSk && productInfo.available){//秒杀
        mutD[@"product_price"] =  productInfo.actSk.skPrice;
    }else if (productInfo.actPgby){//批购包邮
        mutD[@"product_price"] = productInfo.actPgby.assemblePrice;
    }else{
        mutD[@"product_price"] = productInfo.fob;
    }
     
    if (productInfo.positionType) {//搜索坑位类型
        mutD[@"list_position_type"] = [NSString stringWithFormat:@"%@",productInfo.positionType];
    }
    if(productInfo.positionTypeName){
        mutD[@"list_position_typename"] = productInfo.positionTypeName;////搜索坑位类型名称
    }
    if(productInfo.searchSortStrategyCode){
        mutD[@"search_sort_strategy_id"] = productInfo.searchSortStrategyCode;//A/B策略ID
    }
    if(productInfo.productType){
        mutD[@"product_type"] = [NSString stringWithFormat:@"%@",productInfo.productType];;//商品类型
    }
    if (productInfo.productActivityType) {
        mutD[@"product_activity_type"] = [NSString stringWithFormat:@"%@",productInfo.productActivityType];//商品营销活动类型
    }
    mutD[@"key_word"] = productInfo.keyword;//搜索关键字
    mutD[@"sid"] = productInfo.sid;//sid追踪ID
    mutD[@"sptype"] = productInfo.sptype;//来源类型
    mutD[@"product_shop_name"] = productInfo.shopName;//商品店铺名称
    mutD[@"product_shop_code"] = productInfo.shopCode;//商品店铺编码
    mutD[@"product_name"] = productInfo.showName;//商品名称
    mutD[@"product_id"] = productInfo.ID;//商品ID
    mutD[@"product_first"] = [NSString stringWithFormat:@"%@",productInfo.categoryId];//商品一级分类
    
    UIViewController *targetVc = self.ybm_viewController;
    if ([targetVc respondsToSelector:@selector(queryModel)]) {
           YBMProductSearchDataModel *queryModel  = [targetVc valueForKey:@"queryModel"];
            mutD[@"result_cnt"] = @(queryModel.totalCount);
            mutD[@"page_no"] = @(queryModel.pageNo);
            mutD[@"page_size"] = @(queryModel.pageSize);
            mutD[@"total_page"] = @(queryModel.totalPage);
       }
     if ([targetVc respondsToSelector:@selector(searchJgspid)]) {
         mutD[@"jgspid"] = [targetVc valueForKey:@"searchJgspid"];
     }
    return  mutD;
}


- (void)onSkuExposure:(NSString *)skuId{
    [YBMAnalyticsUtil track:kaction_groupPurchase_giftCard_Exposure properties:@{
        @"productId" : skuId
    }];
}

- (void)onSkuClicked:(NSString *)skuId{
    [YBMAnalyticsUtil track:kaction_groupPurchase_giftCard_Click properties:@{
        @"productId" : skuId
    }];
    [self dismiss];
}

#pragma mark - lazy

- (UIWindow *)backWindow {
    if (!_backWindow) {
        _backWindow = [[UIWindow alloc] initWithFrame: [UIScreen mainScreen].bounds];
//        _backWindow.windowLevel = UIWindowLevelStatusBar;
    }
    return _backWindow;
}

- (UIView *)darkView {
    if (!_darkView) {
        _darkView = [UIView new];
        _darkView.userInteractionEnabled = NO;
        _darkView.backgroundColor = [UIColor colorWithWhite:0 alpha:0];
    }
    return _darkView;
}

- (UIView *)contentView {
    if (!_contentView) {
        _contentView = [UIView new];
        _contentView.frame = CGRectMake(0, SCREEN_SIZE.height, SCREEN_SIZE.width, 264 + kBottomSafeHeight);
        _contentView.backgroundColor = UIColor.whiteColor;
    }
    return _contentView;
}

- (UIImageView *)productImageView {
    if (!_productImageView) {
        _productImageView = UIImageView.new;
        _productImageView.backgroundColor = ColorFromHex(0xF2F2F2);
    }
    return _productImageView;
}

- (UIImageView *)sellingPointImageView {
    if (!_sellingPointImageView) {
        _sellingPointImageView = UIImageView.new;
    }
    return _sellingPointImageView;
}

- (UILabel *)nameLab{
    if (!_nameLab) {
        UILabel *lab = [UILabel new];
        lab.font = [UIFont fontWithName:FontTypePingFangMe size:14];
        lab.textColor = [UIColor colorWithHexString:@"#292933"];
        lab.numberOfLines = 2;
        _nameLab = lab;
    }
    return _nameLab;
}

- (YBMInsetLabel *)tipsLab {
    if (!_tipsLab) {
        YBMInsetLabel *lab = [YBMInsetLabel new];
        lab.font = [UIFont systemFontOfSize:12];
        lab.textColor = [UIColor colorWithHexString:@"#292933"];
        lab.numberOfLines = 0;
        lab.textAlignment = NSTextAlignmentCenter;
        lab.backgroundColor = ColorFromHex(0xFFF7CF);
        _tipsLab = lab;
    }
    return _tipsLab;
}

- (UILabel *)inventoryLab {
    if (!_inventoryLab) {
        _inventoryLab = [UILabel new];
        _inventoryLab.font = [UIFont systemFontOfSize:11];
        _inventoryLab.textColor = [UIColor colorWithHexString:@"#9494A6"];
        //        _inventoryLab.backgroundColor = UIColor.orangeColor;
    }
    return _inventoryLab;
}

- (UILabel *)timeLabel {
    if (!_timeLabel) {
        _timeLabel = [UILabel new];
        _timeLabel.font = [UIFont systemFontOfSize:11];
        _timeLabel.textColor = [UIColor colorWithHexString:@"#9494A6"];
        _timeLabel.numberOfLines = 0;
        _timeLabel.text = @"有效期至：";
        //        _timeLabel.backgroundColor = UIColor.blueColor;
    }
    return _timeLabel;
}

- (UILabel *)priceLab{
    if (!_priceLab) {
        UILabel *lab = [[UILabel alloc]init];
        lab.font = [UIFont fontWithName:FontTypePingFangMe size:13];
        lab.textColor = ColorFromHex(0xFF2121);
//        lab.text = @"价格：";
//        lab.backgroundColor = UIColor.greenColor;
        _priceLab = lab;
    }
    return _priceLab;
}

- (UILabel *)buyNumtitleLab {
    if (!_buyNumtitleLab) {
        UILabel *lab = [[UILabel alloc]init];
        lab.font = [UIFont systemFontOfSize:12];
        lab.textColor = [UIColor colorWithHexString:@"#333333"];
        lab.numberOfLines = 0;
        lab.text = @"购买数量";
        _buyNumtitleLab = lab;
    }
    return _buyNumtitleLab;
}

- (UILabel *)discountLab{
    if (!_discountLab) {
        UILabel *label = UILabel.new;
        label.text = @"折后约¥xx";
        label.textColor = [UIColor colorWithHexString:@"#292933"];
        label.font = [UIFont systemFontOfSize:11];
//        label.backgroundColor = UIColor.orangeColor;
        _discountLab = label;
    }
    return _discountLab;
}

- (UILabel *)totalLab{
    if (!_totalLab) {
        _totalLab = [[UILabel alloc]init];
        _totalLab.font = [UIFont systemFontOfSize:13];
        _totalLab.textColor = ColorFromHex(0xFF2121);
    }
    return _totalLab;
}

- (UILabel *)totalTitleLab{
    if (!_totalTitleLab) {
        UILabel *lab = [[UILabel alloc]init];
        lab.font = [UIFont systemFontOfSize:12];
        lab.textColor = [UIColor colorWithHexString:@"#333333"];
        lab.text = @"合计：";
        _totalTitleLab = lab;
    }
    return _totalTitleLab;
}

- (UIButton *)sureButton {
    if (!_sureButton) {
        _sureButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_sureButton setTitle:@"确定" forState:UIControlStateNormal];
        [_sureButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        _sureButton.titleLabel.font = [UIFont fontWithName:FontTypePingFangMe size:16];
        _sureButton.backgroundColor = [UIColor colorWithHexString:@"#00B377"];
        [_sureButton addTarget:self action:@selector(sureActionButton) forControlEvents:UIControlEventTouchUpInside];
        _sureButton.layer.cornerRadius = 2.f;
        _sureButton.clipsToBounds = YES;
    }
    return _sureButton;
}

- (YBMStepperButton *)stepperButton {
    if (!_stepperButton) {
        _stepperButton = [YBMStepperButton new];
        UIColor *color = [UIColor colorWithHexString:@"#999999"];
        _stepperButton.borderColor = [UIColor whiteColor];
        _stepperButton.borderWidth = 0.5;
        _stepperButton.cornerRadius = 2.f;
        _stepperButton.increaseTitle = @"＋";
        _stepperButton.increaseTitleColor = color;
        _stepperButton.reduceTitle = @"－";
        _stepperButton.reduceTitleColor = color;
        _stepperButton.reduceDisableTitleColor = UIColor.grayColor;
        _stepperButton.number = 0;
        _stepperButton.autoHideReduce = NO;
        _stepperButton.delegate = self;
    }
    return _stepperButton;
}

- (UIView *)manJiangView{
    if (!_manJiangView) {
        UIView *view = [[UIView alloc]init];
        view.clipsToBounds = YES;
        
        UILabel *titleLab = [[UILabel alloc] init];
        titleLab.font = [UIFont fontWithName:FontTypePingFangMe size:12];
        titleLab.textColor = ColorFromHex(0x333333);
        titleLab.text = @"优惠：";
        [view addSubview:titleLab];
        [titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.bottom.mas_equalTo(0);
            make.leading.mas_equalTo(10);
            make.width.mas_equalTo(36);
        }];
        
        YBMLabel *lab = [[YBMLabel alloc]init];
        lab.font = [UIFont systemFontOfSize:10.f];
        [view addSubview:lab];
        [lab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.mas_equalTo(titleLab.mas_trailing);
            make.centerY.offset(0);
            make.height.offset(15.f);
        }];
        //别挤我（抗压缩优先级）
        [lab setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
        self.tagLab = lab;
        
        UILabel *descLab = [[UILabel alloc] init];
        descLab.font = [UIFont systemFontOfSize:10];
        descLab.textColor = ColorFromHex(0x333333);
        descLab.numberOfLines = 0;
        [view addSubview:descLab];
        [descLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.tagLab.mas_trailing).offset(10.f);
            make.trailing.offset(-5.f);
            make.top.offset(5.f);
            make.bottom.offset(-5.f);
        }];
        self.descLab = descLab;
        
        UIView *line = [[UIView alloc]init];
        line.backgroundColor = ColorFromHex(0xEEEEEE);
        [view addSubview:line];
        [line mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.mas_equalTo(0);
            make.leading.mas_equalTo(view).offset(10);
            make.trailing.mas_equalTo(view).offset(-10);
            make.height.mas_equalTo(0.5);
        }];
        
        _manJiangView = view;
    }
    return _manJiangView;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end
