//
//  YBMProductAddBuyView.m
//  YaoBangMang
//
//  Created by 张佳彧 on 2020/4/19.
//  Copyright © 2020 XiaoYaoYao.Ltd. All rights reserved.
//

#import "YBMProductAddBuyView.h"
#import "YBMVerticalButton.h"
#import "XYYAddSubView.h"
#import "YBMProductAddBuyModel.h"
#import "XYYControlProductM.h"
#import "YBMQualificationHomeController.h"
#import "YBMProductGroupSettleView.h"
#import "XYYWechatShareView.h"
#import "YBMProductAddView.h" // 加购弹框
#import "YBMWholeSaleSettleView.h"
#import "YBMProductDetailController.h"

@interface YBMProductAddBuyView ()
/// 客服按钮
@property (nonatomic, strong) YBMVerticalButton *serviceButton;
/// 无法加购的标记
@property (nonatomic, strong) UILabel *notBuyLabel;
/// 加购按钮
@property (nonatomic ,strong) XYYAddSubView *addsubView;
/// 普通品加车按钮
@property (nonatomic, strong) UIButton *normalAddToCartButton;
/// 店铺按钮
@property (nonatomic ,strong) YBMVerticalButton *storeButton;
/// 分享按钮
@property (nonatomic ,strong) YBMVerticalButton *shareButton;

@property (nonatomic ,strong) UIView *lineView;
/// 随心拼 状态下的 拼团按钮
@property (nonatomic, strong) UIButton *leftSpellGroupButton;
/// 随心拼 按钮
@property (nonatomic, strong) UIButton *rightSpellGroupButton;
/// 加入购物车 按钮
@property (nonatomic, strong) UIButton *addToCartButton;
/// 随心拼 模式下的view
@property (nonatomic, strong) UIView *spellGroupBaseView;
/// 随心拼 折扣图
@property (nonatomic, strong) UIImageView *discountImgView;
/// 随心拼 折扣lab
@property (nonatomic, strong) UILabel *discountLab;

@property (nonatomic, assign) BOOL isShowSettleView;
@property (nonatomic, assign) BOOL isBtnExposure;
/// qt埋点需要传到购物车的字段
@property (nonatomic, copy) NSDictionary *qtRequestDict;

@end

@implementation YBMProductAddBuyView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setupControls];
    }
    return self;
}

- (void)setupControls {
    [self addSubview:self.lineView];
    [self addSubview:self.shareButton];
    [self addSubview:self.storeButton];
    [self addSubview:self.serviceButton];
//    [self addSubview:self.addOftenBuyButton];
    [self addSubview:self.shopButton];
    [self addSubview:self.addsubView];
    [self.addsubView addSubview:self.normalAddToCartButton];
    [self addSubview:self.baseStatusButton];
    [self addSubview:self.notBuyLabel];
    [self addSubview: self.spellGroupBaseView];
    [self.spellGroupBaseView addSubview:self.leftSpellGroupButton];
    [self.spellGroupBaseView addSubview:self.rightSpellGroupButton];
    [self.spellGroupBaseView addSubview:self.addToCartButton]; // 添加购物车按钮
    
    self.serviceButton.offsetY = -2;
    //    self.addOftenBuyButton.offsetY = -2;
    self.shopButton.offsetY = -2;
    self.storeButton.offsetY = -2;
    self.shareButton.offsetY = -2;
    
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.leading.trailing.mas_equalTo(self);
        make.height.mas_equalTo(.5);
    }];
    /*
    [self.serviceButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.leading.mas_equalTo(self);
        make.width.mas_equalTo(w);
    }];
    [self.addOftenBuyButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.serviceButton.mas_trailing);
        make.top.bottom.mas_equalTo(self.serviceButton);
        make.width.mas_equalTo(w);
    }];
    [self.shopButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.addOftenBuyButton.mas_trailing);
        make.top.bottom.mas_equalTo(self.serviceButton);
        make.width.mas_equalTo(w);
    }];
     */
    
    // fieldBackView 是加减号控件中显示数字部分的View
    [self.addsubView.fieldBackView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(_addsubView.contentView.mas_top).offset(13);
        make.bottom.mas_equalTo(_addsubView.contentView.mas_bottom).offset(-13);
        make.leading.mas_equalTo(_addsubView.imageSub.mas_trailing).offset(12);
        make.trailing.mas_equalTo(_addsubView.imageAdd.mas_leading).offset(-12);
    }];
    
    [self.addsubView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(self.mas_right).offset(-10);
        make.top.mas_equalTo(self.mas_top).offset(8);
        make.height.mas_equalTo(@(37));
        make.left.mas_equalTo(122);
    }];
    
    [self.normalAddToCartButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.addsubView.mas_top);
        make.left.mas_equalTo(self.addsubView.mas_left);
        make.bottom.mas_equalTo(self.addsubView.mas_bottom);
        make.right.mas_equalTo(self.addsubView.mas_right);
    }];
    
    [self.baseStatusButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(self.mas_right).offset(-10);
        make.top.mas_equalTo(self.mas_top).offset(8);
        make.height.mas_equalTo(@(37));
        make.left.mas_equalTo(122);
    }];
    
    [self.spellGroupBaseView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(self.mas_right).offset(-10);
        make.top.mas_equalTo(self.mas_top).offset(8);
        make.height.mas_equalTo(@(37));
        make.left.mas_equalTo(122);
    }];
    
    [self.leftSpellGroupButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self.spellGroupBaseView).offset(0);
        make.height.mas_equalTo(@(37));
        make.right.mas_equalTo(self.rightSpellGroupButton.mas_left).offset(-9);
        make.centerY.mas_equalTo(self.mas_centerY);
        make.width.mas_equalTo(self.rightSpellGroupButton);
    }];
    
    [self.rightSpellGroupButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(self.spellGroupBaseView).offset(0);
        make.height.mas_equalTo(@(37));
        make.centerY.mas_equalTo(self.mas_centerY);
    }];
    
    // 加入购物车按钮
    [self.addToCartButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.rightSpellGroupButton.mas_top);
        make.left.mas_equalTo(self.rightSpellGroupButton.mas_left);
        make.bottom.mas_equalTo(self.rightSpellGroupButton.mas_bottom);
        make.right.mas_equalTo(self.rightSpellGroupButton.mas_right);
    }];
    
    [self.notBuyLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(self.mas_right).offset(-10);
        make.top.mas_equalTo(self.mas_top).offset(6);
        make.height.mas_equalTo(@(37));
        make.left.mas_equalTo(122);
    }];
}

#pragma mark - SETTER GETTER

/*
 1，右侧按钮
    addSubView - 加购按钮
    baseStatusButton - 订阅/资质问题/拼团
    notBuyLabel - 无法加购
 
    1，资质问题： 1-5 显示baseStatusButton，提示资质问题
    else资质正常：
    2，团购：进行中显示addSubView， 加购按钮
    else 非进行中团购：
    3，状态为2 ：baseStatusButton 无法加购
    else 状态非2:
    4，赠品不可加购，显示notLabel, 不可加购,无法点击
    else 不是赠品
    5，控销不可购：显示notLabel, 不可加购，无法点击
    else 控销可购：
    6，OEM且未签署：显示baseStatusButton: 价格签署协议后可见
    else 均已通过
    7，售罄显示 baseStatusButton：已订阅/到货提醒，点击后取消订阅
    else 显示正常加购按钮
 
    2，前排按钮的显示逻辑
        拼团中：依次显示分享，店铺，客服
        KA：客服，店铺
        其他：客服（三方为联系商家），店铺，购物车
 */

- (void)setAddBuyModel:(YBMProductAddBuyModel *)addBuyModel {
    _addBuyModel = addBuyModel;

    // TODO:zjy
    XYYControlProductM *modelP = [[XYYControlProductM alloc] init];
    modelP.ID = [NSString stringWithFormat:@"%@",addBuyModel.ID];
    modelP.status = [NSString stringWithFormat:@"%@",addBuyModel.status];
    modelP.availableQty = [NSString stringWithFormat:@"%@",addBuyModel.availableQty];
    modelP.isSplit = addBuyModel.isSplit.integerValue;
    modelP.mediumPackageNum = addBuyModel.mediumPackageNum.integerValue;
    modelP.showName = addBuyModel.showName;
    modelP.fob  = addBuyModel.fob.stringValue;
    modelP.page_id = _addBuyModel.page_id;
    modelP.moduleType = _addBuyModel.moduleType;
    modelP.offset = _addBuyModel.offset;
    modelP.detailM = _addBuyModel.detailM;
    self.addsubView.model = modelP;
    
    self.spellGroupBaseView.hidden = YES;
   
    /// 库存
    if (addBuyModel.availableQty.integerValue <= 0 || addBuyModel.status.integerValue == 2 || addBuyModel.status.integerValue == 4) {
        self.baseStatusButton.hidden = NO;
        self.addsubView.hidden = YES;
    } else {
        self.baseStatusButton.hidden = YES;
        self.addsubView.hidden = NO;
    }
    if (addBuyModel.favoriteFlag.integerValue == 0) {
        [self.baseStatusButton setTitle:@"已订阅" forState:UIControlStateNormal];
        [self.baseStatusButton setBackgroundColor:[UIColor colorWithHexString:@"#A9AEB7"]];
        [self.baseStatusButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        self.baseStatusButton.enabled = NO;
    } else {
        [self.baseStatusButton setTitle:@"到货提醒" forState:UIControlStateNormal];
        self.baseStatusButton.backgroundColor = [UIColor colorWithHexString:@"0x00B955"];
        [self.baseStatusButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        self.baseStatusButton.enabled = YES;
    }
    if (addBuyModel.isControl.intValue == 1) {//控销
        if (addBuyModel.isPurchase) {//可购买
//            self.notBuyLabel.hidden = YES;
//            self.addsubView.hidden = NO;
        } else {
            self.baseStatusButton.hidden = YES;
            self.notBuyLabel.hidden = NO;
            self.addsubView.hidden = YES;
        }
        if (addBuyModel.isOEM && addBuyModel.signStatus == 0) {
            self.baseStatusButton.hidden = YES;
        }
    }
    // 是否是赠品
    if (addBuyModel.isGive.integerValue == 1 || addBuyModel.productType.intValue == 4) {
        self.baseStatusButton.hidden = NO;
        [self.baseStatusButton setTitle:@"赠品无法加购" forState:UIControlStateNormal];
        [self.baseStatusButton setBackgroundColor:[UIColor colorWithHexString:@"#A9AEB6"]];
        self.addsubView.hidden = YES;
    }
    if ([XYYUserData ShareUserData].licenseStatus.intValue == 1) {
//    if (addBuyModel.controlType && addBuyModel.controlType.intValue == 1) {
        _addsubView.hidden = YES;
        _baseStatusButton.hidden = NO;
        [_baseStatusButton setTitle:@"资质认证" forState:UIControlStateNormal];
        _baseStatusButton.backgroundColor = [UIColor colorWithHexString:@"0x00B955"];
    }else if ([XYYUserData ShareUserData].licenseStatus.intValue == 5) {
        _addsubView.hidden = YES;
        _baseStatusButton.hidden = NO;
        [_baseStatusButton setTitle:@"资质审核中" forState:UIControlStateNormal];
        _baseStatusButton.backgroundColor = [UIColor colorWithHexString:@"0x00B955"];
    } else {
        if (_addBuyModel && _addBuyModel.isAssemble.integerValue == 1) {
            /// 是否拼团 0否 1是 拼团中显示
            if (_addBuyModel.assembleStatus.intValue == 1 && _addBuyModel.canAddToCart) {
                self.baseStatusButton.hidden = YES;
                self.notBuyLabel.hidden = YES;
                self.addsubView.hidden = YES;
                self.spellGroupBaseView.hidden = NO;
                self.discountImgView.hidden = self.discountLab.hidden = YES;
                self.rightSpellGroupButton.hidden = YES;
                self.addToCartButton.hidden = NO;
                if (!_isBtnExposure) {
                    _isBtnExposure = YES;
                    // 按钮曝光埋点
                    [self auroraAnalysisExposureWithBtnName:@"立即参团"];
                    [self auroraAnalysisExposureWithBtnName:@"加入购物车"];
                }
            } else {
                if (_addBuyModel.assembleStatus.intValue == 1 && _addBuyModel.supportSuiXinPin) {
                    /// 随心拼  进行中的拼团
                    self.baseStatusButton.hidden = YES;
                    self.notBuyLabel.hidden = YES;
                    self.addsubView.hidden = YES;
                    self.spellGroupBaseView.hidden = NO;
                    self.rightSpellGroupButton.hidden = NO;
                    self.addToCartButton.hidden = YES; // 加入购物车按钮隐藏
                    [self.rightSpellGroupButton setTitle:_addBuyModel.suiXinPinButtonText forState:UIControlStateNormal];
                    if (_addBuyModel.suiXinPinButtonBubbleText.length) {
                        self.discountLab.text = _addBuyModel.suiXinPinButtonBubbleText;
                        self.discountImgView.hidden = self.discountLab.hidden = NO;
                    }else{
                        self.discountImgView.hidden = self.discountLab.hidden = YES;
                    }
                    if (!_isBtnExposure) {
                        _isBtnExposure = YES;
                        // 按钮曝光埋点
                        [self auroraAnalysisExposureWithBtnName:@"立即参团"];
                        [self auroraAnalysisExposureWithBtnName:@"随心拼"];
                    }
                } else {
                    /// 拼团
                    self.baseStatusButton.hidden = NO;
                    self.notBuyLabel.hidden = YES;
                    self.addsubView.hidden = YES;
                    if(_addBuyModel.assembleStatus.intValue == 1) {
                        if(_addBuyModel.status.intValue == 2) {
                            [_baseStatusButton setTitle:@"已抢光" forState:UIControlStateNormal];
                            self.baseStatusButton.backgroundColor = [UIColor colorWithHexString:@"#A9AEB6"];
                        } else {
                            [_baseStatusButton setTitle:@"立即参团" forState:UIControlStateNormal];
                            _baseStatusButton.backgroundColor = [UIColor colorWithHexString:@"0x00B955"];
                        }
                    } else { //未开始的品团
                        NSNumber* beginTime = _addBuyModel.assembleStartTime;
                        NSDate* time = [NSDate dateWithTimeIntervalSince1970:(beginTime.doubleValue/1000)];
                        NSDateFormatter *dateFmt = [[NSDateFormatter alloc]init];
                        dateFmt.dateFormat = @"MM月dd日 HH:mm开团";
                        NSString* timeString = [dateFmt stringFromDate:time];
                        [_baseStatusButton setTitle:timeString forState:UIControlStateNormal];
                        _baseStatusButton.backgroundColor = [UIColor colorWithHexString:@"0x00B955"];
                    }
                    _baseStatusButton.titleLabel.font = [UIFont fontWithName:FontTypePingFangMe size:16];
                }
            }
            
        } else if (_addBuyModel.actSk && _addBuyModel.actSk.status.intValue == 0){
            self.baseStatusButton.hidden = NO;
            self.notBuyLabel.hidden = YES;
            self.addsubView.hidden = YES;
            [_baseStatusButton setTitle:@"即将开抢" forState:UIControlStateNormal];
            self.baseStatusButton.backgroundColor = [UIColor colorWithHexString:@"#01B377"];
            
        } else if (_addBuyModel.actSk && _addBuyModel.status.intValue == 2) {
            self.baseStatusButton.hidden = NO;
            self.addsubView.hidden = YES;
            self.notBuyLabel.hidden = YES;
            [_baseStatusButton setTitle:@"已抢光" forState:UIControlStateNormal];
            self.baseStatusButton.backgroundColor = [UIColor colorWithHexString:@"#A9AEB6"];
        }
        else {
              if (addBuyModel.merchantStatus.intValue == 2) {
                  /// 其他用户
                  _addsubView.hidden = YES;
                  _baseStatusButton.hidden = NO;
                  [_baseStatusButton setTitle:@"无法加购" forState:UIControlStateNormal];
                  _baseStatusButton.backgroundColor = [UIColor colorWithHexString:@"#A9AEB7"];
              }else if ([addBuyModel.isWholesale isEqualToNumber:@1]){
                  if (_addBuyModel.assembleStatus.intValue == 1 && _addBuyModel.canAddToCart) {
                      self.baseStatusButton.hidden = YES;
                      self.notBuyLabel.hidden = YES;
                      self.addsubView.hidden = YES;
                      self.spellGroupBaseView.hidden = NO;
                      [self.leftSpellGroupButton setTitle:@"去抢购" forState:UIControlStateNormal];
                      self.rightSpellGroupButton.hidden = YES;
                      self.discountImgView.hidden = self.discountLab.hidden = YES;
                      self.addToCartButton.hidden = NO;
                      if (!_isBtnExposure) {
                          _isBtnExposure = YES;
                          // 按钮曝光埋点
                          [self auroraAnalysisExposureWithBtnName:@"去抢购"];
                          [self auroraAnalysisExposureWithBtnName:@"加入购物车"];
                      }
                  } else {
                      if (_addBuyModel.assembleStatus.intValue == 1 && _addBuyModel.supportSuiXinPin) {
                          self.baseStatusButton.hidden = YES;
                          self.notBuyLabel.hidden = YES;
                          self.addsubView.hidden = YES;
                          self.spellGroupBaseView.hidden = NO;
                          self.addToCartButton.hidden = YES;
                          self.rightSpellGroupButton.hidden = NO;
                          [self.leftSpellGroupButton setTitle:@"去抢购" forState:UIControlStateNormal];
                          [self.rightSpellGroupButton setTitle:_addBuyModel.suiXinPinButtonText forState:UIControlStateNormal];
                          if (_addBuyModel.suiXinPinButtonBubbleText.length) {
                              self.discountLab.text = _addBuyModel.suiXinPinButtonBubbleText;
                              self.discountImgView.hidden = self.discountLab.hidden = NO;
                          }else{
                              self.discountImgView.hidden = self.discountLab.hidden = YES;
                          }
                          if (!_isBtnExposure) {
                              _isBtnExposure = YES;
                              // 按钮曝光埋点
                              [self auroraAnalysisExposureWithBtnName:@"去抢购"];
                              [self auroraAnalysisExposureWithBtnName:@"随心拼"];
                          }
                      } else {
                          //批购包邮
                          _addsubView.hidden = YES;
                          _baseStatusButton.hidden = NO;
                          [_baseStatusButton setTitle:@"去抢购" forState:UIControlStateNormal];
                          _baseStatusButton.backgroundColor = [UIColor colorWithHexString:@"#17B044"];
                          if (addBuyModel.status.intValue == 2 || addBuyModel.availableQty.intValue <= 0) {
                              [self.baseStatusButton setTitle:@"到货提醒" forState:UIControlStateNormal];
                              self.baseStatusButton.backgroundColor = [UIColor colorWithHexString:@"0x00B955"];
                              [self.baseStatusButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
                          }
                      }
                  }
              }
          }
    }
    
    if(!addBuyModel.available){ //被控销的情况下展示被控销样式
        self.baseStatusButton.hidden = NO;
        self.addsubView.hidden = YES;
        self.notBuyLabel.hidden = YES;
        self.baseStatusButton.backgroundColor = [UIColor colorWithHexString:@"#A9AEB7"];
        [self.baseStatusButton setTitle:addBuyModel.controlPurchaseButton forState:UIControlStateNormal];
        
        if(addBuyModel.controlType && addBuyModel.controlType.intValue == 1){
            self.baseStatusButton.backgroundColor = [UIColor colorWithHexString:@"0x00B955"];
        }
    }
    
    // 控销品按钮置灰
    if (addBuyModel.controlTitle.length > 0 && [addBuyModel.controlType isEqualToNumber:@5]) {
        // 设置左边按钮的文字颜色和边框颜色
        [self.leftSpellGroupButton setTitleColor:[UIColor colorWithHexString:@"0xA9AEB7"] forState:UIControlStateNormal];
        self.leftSpellGroupButton.layer.borderColor = [UIColor colorWithHexString:@"0xA9AEB7"].CGColor;
        // 设置右边按钮的背景色
        self.rightSpellGroupButton.backgroundColor = [UIColor colorWithHexString:@"0xA9AEB7"];
        // 设置加入购物车按钮置灰且不可点击
        self.addToCartButton.backgroundColor = [UIColor colorWithHexString:@"0xA9AEB7"];
        self.addToCartButton.userInteractionEnabled = NO;
    } else {
        // 设置左边按钮的文字颜色和边框颜色
        [self.leftSpellGroupButton setTitleColor:[UIColor colorWithHexString:@"0x00B955"] forState:UIControlStateNormal];
        self.leftSpellGroupButton.layer.borderColor = [UIColor colorWithHexString:@"0x00B955"].CGColor;
        // 设置右边按钮的背景色
        self.rightSpellGroupButton.backgroundColor = [UIColor colorWithHexString:@"0x00B955"];
        // 设置加入购物车按钮恢复正常
        self.addToCartButton.backgroundColor = [UIColor colorWithHexString:@"0x00B955"];
        self.addToCartButton.userInteractionEnabled = YES;
    }
    
    // 埋点参数赋值
    self.addsubView.sptype = addBuyModel.sptype;
    self.addsubView.spid = addBuyModel.spid;
    self.addsubView.sid = addBuyModel.sid;
    self.addsubView.nsid = addBuyModel.nsid;
    self.addsubView.sdata = addBuyModel.sdata;
    
    CGFloat buttonW = 43.f;
    // 店铺按钮
    [self.storeButton mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self);
        make.top.mas_equalTo(self).offset(2);
        make.leading.mas_equalTo(self).offset(10);
        make.width.mas_equalTo(buttonW);
    }];
    
    // 购物车按钮
    [self.shopButton mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.mas_equalTo(self.storeButton);
        make.leading.mas_equalTo(self.storeButton.mas_trailing).offset(10);
        make.width.mas_equalTo(buttonW);
    }];
    
    if(!_isShowSettleView && [addBuyModel.isWholesale isEqualToNumber:@1] && !addBuyModel.supportSuiXinPin){
        // 11.9.5 进入商品详情页，默认不弹窗
//        [self showAddBuyActionSheet: _addBuyModel andAnyGroup:NO];
        _isShowSettleView = YES;
    }
}

- (BOOL)isAssemble {
    return (_addBuyModel) && (_addBuyModel.isAssemble.integerValue == 1 && _addBuyModel.assembleStatus.integerValue == 1);
}

#pragma mark - view send

- (void)viewSendData:(id)data Type:(XYY_VIEW_SENDDATA_TYPE)type {
    if (type == XYY_V_SD_ADDSUBCLICK) {
        self.animationBlock();
    }
}

#pragma mark - Action

- (void)addOftenBuyActionButton {
    if (_addBuyModel.isShowShop.integerValue == 1) {
        NSString *string = [NSString stringWithFormat:@"ybmpage://commonh5activity?url=%@",_addBuyModel.shopHomeUrl];
        UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:string];
        [AppConfigue pushVC:vc];
    } else {
        if (_delegate && [_delegate respondsToSelector:@selector(ybm_productAddBuyAction:)]) {
            [_delegate ybm_productAddBuyAction:self];
        }
    }
}

/// 购物车按钮
- (void)shopActionButton {
    // QT埋点
    [self QTTrackActionSubModuleClickModel:self.addBuyModel btnIndex:@"2" btnText:@"购物车" popUP:NO];
    
    UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:@"ybmpage://cartactivity"];
    [self.viewController.navigationController pushViewController:vc animated:YES];
}

- (void)serviceActionButton {
    if (_delegate && [_delegate respondsToSelector:@selector(ybm_productAddBuyServiceAction:)]) {
        [_delegate ybm_productAddBuyServiceAction:self];
    }
}

/// 店铺按钮
- (void)storeActionButton {
    // QT埋点
    [self QTTrackActionSubModuleClickModel:self.addBuyModel btnIndex:@"1" btnText:@"店铺" popUP:NO];
    
    if (_delegate && [_delegate respondsToSelector:@selector(ybm_productToShopAction:)]) {
        [_delegate ybm_productToShopAction:self];
    }
}

- (void)shareActionButton {
    if (_delegate && [_delegate respondsToSelector:@selector(ybm_productShareAction:)]) {
        [_delegate ybm_productShareAction:self];
    }
}

- (void)baseStatusActionButton:(UIButton *)button {
    if(!_addBuyModel.available){  //后端控制不可见时忽略其他点击事件
        if(_addBuyModel.controlType && _addBuyModel.controlType.intValue == 1){
            YBMQualificationHomeController *vc = [YBMQualificationHomeController new];
            [AppConfigue pushVC:vc];
        }
    } else if ([button.titleLabel.text isEqualToString:@"资质认证"] || [button.titleLabel.text isEqualToString:@"资质审核中"]) {
        // 极光埋点
        [self auroraAnalysisClickWithBtnName:@"资质认证"];
        YBMQualificationHomeController *vc = [YBMQualificationHomeController new];
        [AppConfigue pushVC:vc];
    } else if ([button.titleLabel.text isEqualToString:@"已订阅"]) {
        /// 不做处理
    } else if ([button.titleLabel.text isEqualToString:@"到货提醒"]) {
        if (_delegate && [_delegate respondsToSelector:@selector(ybm_productAddBuySubscibeAction:)]) {
            [_delegate ybm_productAddBuySubscibeAction:self];
        }
    } else if ([button.titleLabel.text containsString:@"立即参团"]) {
        [self showAddBuyActionSheet: _addBuyModel andAnyGroup:NO];
        // 极光埋点
        //直睿极光埋点
        [self trackProductButtonClick:@"立即参团"];
        // QT埋点
        [self QTTrackActionSubModuleClickModel:self.addBuyModel btnIndex:@"3" btnText:@"立即参团" popUP:NO];
    } else if ([button.titleLabel.text containsString:@"随心拼"]) {
        [self showAddBuyActionSheet: _addBuyModel andAnyGroup:YES];
        // 极光埋点
        //直睿极光埋点
        [self trackProductButtonClick:@"随心拼"];
        // QT埋点
        [self QTTrackActionSubModuleClickModel:self.addBuyModel btnIndex:@"4" btnText:@"随心拼" popUP:NO];
    }else if ([button.titleLabel.text containsString:@"去抢购"]) {
        [self showAddBuyActionSheet: _addBuyModel andAnyGroup:NO];
        //直睿极光埋点
        [self trackProductButtonClick:@"去抢购"];
        // QT埋点
        [self QTTrackActionSubModuleClickModel:self.addBuyModel btnIndex:@"3" btnText:@"去抢购" popUP:NO];
    }
    else if ([button.titleLabel.text containsString:@"即将开抢"]) {
        MBShowTextNoIcon(@"活动暂未开始");
    }
}

// 普通品“加入购物车”按钮
- (void)normalAddToCartButtonClick:(UIButton *)button {
    [self showAddView];
    
     // 直睿极光新埋点
//    [self trackProductAddToCart]; //在showAddView的中确定上报加购埋点，此处不上报
    [self trackProductButtonClick:@"加入购物车"];
    // QT埋点
    [self QTTrackActionSubModuleClickModel:self.addBuyModel btnIndex:@"3" btnText:@"加入购物车" popUP:NO];
}

// 点击“加入购物车“的事件
- (void)addToCartButtonClick:(UIButton *)button {
    // 根据拼团或者批购包邮品，弹出对应弹窗
    [self showAddBuyActionSheet:_addBuyModel fromProductDetail:YES];
    // 直睿极光新埋点
//    [self trackProductAddToCart]; //在showAddBuyActionSheet的中确定上报加购埋点，此处不上报
    [self trackProductButtonClick:@"加入购物车"];
    // QT埋点
    [self QTTrackActionSubModuleClickModel:self.addBuyModel btnIndex:@"4" btnText:@"加入购物车" popUP:NO];
}

// 11.9.5 之前，底部按钮走这个逻辑，两个弹窗里均不涉及“加入购物车”
-(void) showAddBuyActionSheet:(YBMProductAddBuyModel*)addBuyModel andAnyGroup:(BOOL)isAnyGroup {
    addBuyModel.qtTrackData = [self getSameProductAddBuyParams];
    if([addBuyModel.isWholesale isEqualToNumber:@1]){
        YBMWholeSaleSettleView *sheetView = [[YBMWholeSaleSettleView alloc]init];
        sheetView.sourceFrom = YBMAnalysisShoppingSourceTypeFromDetail;
        sheetView.model = addBuyModel;
        sheetView.isAnyGroup = isAnyGroup;
//        [sheetView show];
        return;
    }
    YBMProductGroupSettleView *sheet = [YBMProductGroupSettleView new];
    sheet.model = addBuyModel;
    sheet.isAnyGroup = isAnyGroup;
//    [sheet show];
}

// 11.9.5 之前，底部按钮走这个逻辑，两个弹窗里均不涉及“加入购物车”
-(void) showAddBuyActionSheet:(YBMProductAddBuyModel*)addBuyModel fromProductDetail:(BOOL)fromProductDetail {
    addBuyModel.qtTrackData = [self getSameProductAddBuyParams];
    if([addBuyModel.isWholesale isEqualToNumber:@1]){
        YBMWholeSaleSettleView *sheetView = [[YBMWholeSaleSettleView alloc]init];
        sheetView.sourceFrom = YBMAnalysisShoppingSourceTypeFromDetail;
        sheetView.showAddToCart = NO;
        sheetView.isAnyGroupUI = NO;
        sheetView.fromProductDetail = fromProductDetail;
        sheetView.freeShippingFlag = self.freeShippingFlag;
        sheetView.model = addBuyModel;
        @weakify(self)
        sheetView.block = ^(NSInteger count) {
            @strongify(self)
            self.addsubView.showToast = YES;
            self.addsubView.fieldCount.text = @(count).stringValue;
            UIViewController *vc = self.ybm_viewController;
            if([vc isKindOfClass:[YBMProductDetailController class]]){
               YBMProductDetailController *productDetailVc = (YBMProductDetailController*)vc;
                self.addsubView.productDetailMdDataDict = productDetailVc.productDetailMdDataDict;
                // qt埋点
                [self QTTrackActionSubModuleClickModel:self.addBuyModel btnIndex:@"4" btnText:@"确定" popUP:YES];
                // qt埋点字段
                self.addsubView.qtDictM = self.qtRequestDict;
            }
            [self.addsubView pushProductCountToServer];
        };
        return;
    }
    YBMProductGroupSettleView *sheet = [YBMProductGroupSettleView new];
    sheet.showAddToCart = NO;
    sheet.isAnyGroupUI = NO;
    sheet.fromProductDetail = fromProductDetail;
    sheet.freeShippingFlag = self.freeShippingFlag;
    sheet.model = addBuyModel;
    @weakify(self)
    sheet.block = ^(NSInteger count) {
        @strongify(self)
        self.addsubView.showToast = YES;
        self.addsubView.fieldCount.text = @(count).stringValue;
        UIViewController *vc = self.ybm_viewController;
        if([vc isKindOfClass:[YBMProductDetailController class]]){
           YBMProductDetailController *productDetailVc = (YBMProductDetailController*)vc;
            self.addsubView.productDetailMdDataDict = productDetailVc.productDetailMdDataDict;
            // qt埋点
            [self QTTrackActionSubModuleClickModel:self.addBuyModel btnIndex:@"4" btnText:@"确定" popUP:YES];
            // qt埋点字段
            self.addsubView.qtDictM = self.qtRequestDict;
       }
        [self.addsubView pushProductCountToServer];
    };
}

- (void)showAddView{
    [[UIApplication sharedApplication].keyWindow endEditing:YES];
    UIViewController *vc = self.ybm_viewController;
    if([vc isKindOfClass:[YBMProductDetailController class]]){
       YBMProductDetailController *productDetailVc = (YBMProductDetailController*)vc;
        self.addsubView.productDetailMdDataDict = productDetailVc.productDetailMdDataDict;
   }
    YBMProductItemModel *model = [YBMProductItemModel alloc];
    model.imageUrl = self.addBuyModel.imageUrl;
    model.showName = self.addBuyModel.showName;
    model.spec = self.addBuyModel.spec;
    model.fob = self.addBuyModel.fob;
//    model.pricePrefix = self.addBuyModel.productUnit;
    // 设置折后价
    model.discountPrice = [self getDiscountPriceStringWithModel:self.addBuyModel];
    model.nearEffect = self.addBuyModel.nearEffect;
    model.availableQty = self.addBuyModel.availableQty;
    model.mediumPackageNum = self.addBuyModel.mediumPackageNum;
    model.isSplit = self.addBuyModel.isSplit;
    model.ID = self.addBuyModel.ID;
    model.orgId = self.addBuyModel.orgId;
    // 11.9.5 增加一个商品类型字段，用于“addCartTrial”接口
    model.productType = self.addBuyModel.productType;

    YBMProductAddView *sheet = [YBMProductAddView new];
    sheet.model = model;
    sheet.freeShippingFlag = self.freeShippingFlag;
    @weakify(self)
    sheet.block = ^(NSInteger count) {
        @strongify(self)
        self.addsubView.showToast = YES;
        self.addsubView.fieldCount.text = @(count).stringValue;
        // qt埋点
        [self QTTrackActionSubModuleClickModel:self.addBuyModel btnIndex:@"4" btnText:@"确定" popUP:YES];
        // qt埋点字段
        self.addsubView.qtDictM = self.qtRequestDict;
        [self.addsubView pushProductCountToServer];
    };
    [sheet showWithCount:self.addsubView.fieldCount.text.integerValue];
}

// 获取折后价
- (NSString *)getDiscountPriceStringWithModel:(YBMProductAddBuyModel *)addBuyModel {
    NSString *fobString = [NSString stringWithFormat:@"¥%@", addBuyModel.fob.stringValue];
    BOOL showDiscount = [self calculateWithPrice:fobString discountPrice:addBuyModel.discountPrice];
    if (addBuyModel.discountPrice.length > 0 && showDiscount) {
        NSString *discountPrice = [NSString stringWithFormat:@"折后约%@", addBuyModel.discountPrice];
        return discountPrice;
    } else {
        return @"";
    }
}

// 去掉¥比较, 当折后价小于正常的价格，返回YES，否则返回NO
- (BOOL)calculateWithPrice:(NSString *)price discountPrice:(NSString *)discountPrice{
    NSString *numPrice = [price substringFromIndex:1];
    NSString *numDiscount = [discountPrice substringFromIndex:1];
    double doublePrice = numPrice.doubleValue;
    double doubleDiscount = numDiscount.doubleValue;
    return doubleDiscount < doublePrice;
}

#pragma mark - 埋点
//直睿新埋点 商品按钮点击埋点
- (void)trackProductButtonClick:(NSString*)btnName{

    NSMutableDictionary *mutD = @{}.mutableCopy;
    UIViewController *vc = self.ybm_viewController;
    if([vc isKindOfClass:[YBMProductDetailController class]]){//商品详情页
       YBMProductDetailController *productDetailVc = (YBMProductDetailController*)vc;
        [mutD addEntriesFromDictionary:productDetailVc.productDetailTrackDict];
   }
    mutD[@"product_number"] = self.addBuyModel.skuStartNum?:0;//商品数量
    mutD[@"btn_name"] = btnName;//："立即参团"、"去抢购"、"加入购物车" "确定"
    mutD[@"btn_desc"] = @"商详页";//"列表页"，"列表页底部弹窗"，"商详页"，"商详页底部弹窗"
    mutD[@"direct"] = @"2";//0"-其他；"1"-列表页直接加购提单；"2"-详情页加购提单；"3"-购物车页面加购；"4"-订单再次购买加购
//    NSLog(@"%@-------",mutD);
    // 极光埋点已移除 - action_product_button_click (商品详情页)
    // [AnalysysAgent track:kAction_product_button_click properties:mutD];
}

//直睿新的商品加入购物车埋点
- (void)trackProductAddToCart{
    NSMutableDictionary *mutD = @{}.mutableCopy;
    UIViewController *vc = self.ybm_viewController;
    if([vc isKindOfClass:[YBMProductDetailController class]]){
       YBMProductDetailController *productDetailVc = (YBMProductDetailController*)vc;
        [mutD addEntriesFromDictionary:productDetailVc.productDetailTrackDict];
   }
    mutD[@"product_number"] =  self.addBuyModel.skuStartNum?:0;//商品数量
    NSDate *currentDate = [NSDate date];
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
    mutD[@"add_cart_time"] = [dateFormatter stringFromDate:currentDate];;//加购时间。
    mutD[@"direct"] = @"2";//0"-其他；"1"-列表页直接加购提单；"2"-详情页加购提单；"3"-购物车页面加购；"4"-订单再次购买加购
//    NSLog(@"%@-------",mutD);
    [AnalysysAgent track:kAdd_to_cart properties:mutD];
}

// 按钮曝光
- (void)auroraAnalysisExposureWithBtnName:(NSString *)btnName
{
    UIViewController *currentVC = self.ybm_viewController;
    NSMutableDictionary *mutD = [NSMutableDictionary dictionary];
    [YBMAnalyticsPages setReferrerWith:mutD controller:currentVC];
    mutD[@"$url"] = NSStringFromClass([currentVC class]);
    mutD[@"$url_domain"] = NSStringFromClass([currentVC class]);
    mutD[@"$title"] = [YBMAnalyticsPages returnTitleNameFrom:NSStringFromClass([currentVC class])];
    mutD[@"page_id"] = [YBMAnalyticsPages pageIdFromClass:currentVC];
    mutD[@"module"] = @"商品详情页";
    mutD[@"btn_name"] = btnName;
    mutD[@"btn_desc"] = @"商详页";
    mutD[@"product_type"] = [self getProductTypeString];
    mutD[@"product_id"] = self.addBuyModel.ID;
    [AnalysysAgent track:btn_exposure properties:mutD];
}

// 按钮点击
- (void)auroraAnalysisClickWithBtnName:(NSString *)btnName {
    NSMutableDictionary *lzrDic = [NSMutableDictionary dictionary];
    // 极光埋点已移除 - action_product_button_click (商品详情页按钮点击)
    // [AnalysysAgent track:kAction_product_button_click properties:lzrDic];
}

// 获取商品类型
- (NSString *)getProductTypeString {
    // 1普通商品，2秒杀商品，3拼团商品，4赠品活动商品
    if ([self.addBuyModel.productType isEqualToNumber:@2]) {
        return @"秒杀商品";
    } else if ([self.addBuyModel.productType isEqualToNumber:@3]) {
        return @"拼团商品";
    } else if ([self.addBuyModel.productType isEqualToNumber:@4]) {
        return @"赠品活动商品";
    } else {
        return @"普通品";
    }
}

#pragma mark -QT埋点

// 子模块点击事件
- (void)QTTrackActionSubModuleClickModel:(YBMProductAddBuyModel *)model btnIndex:(NSString *)btnIndex btnText:(NSString *)btnText popUP:(BOOL)popUP {
    NSMutableDictionary *qtDic = [self getQTTrackFieldDictWithModel:model btnIndex:btnIndex btnText:btnText popUP:popUP];
    self.qtRequestDict = qtDic;
    [YBMQTTrack track:kQTActionSubModuleClick attributes:qtDic];
}

/// 获取埋点字段
/// - Parameters:
///   - model: 当前模型
///   - btnIndex: 按钮的序号
///   - btnText: 按钮的文案
///   - popUP: YES：表示商详页面底部点击弹出的弹窗 NO：商详页面底部按钮
- (NSMutableDictionary *)getQTTrackFieldDictWithModel:(YBMProductAddBuyModel *)model btnIndex:(NSString *)btnIndex btnText:(NSString *)btnText popUP:(BOOL)popUP {
    YBMCMSTrackDataModel *trackData = [[YBMCMSTrackDataModel alloc] init];
    YBMQTSpmModel *spmEntity = [[YBMQTSpmModel alloc] init];
    spmEntity.spmB = [NSString stringWithFormat:@"productDetail_%@-0_0", model.ID];
    spmEntity.spmC = @"ftProdDetail@Z";
    NSString *spmDPreStr = popUP ? @"ftFloatProd@Z_btn@" : @"btn@";
    spmEntity.spmD = [NSString stringWithFormat:@"%@%@", spmDPreStr, btnIndex];
    trackData.spmEntity = spmEntity;
    
    YBMQTScmModel *scmEntity = [[YBMQTScmModel alloc]init];
    scmEntity.scmA = @"appFE";
    scmEntity.scmC = @"all_0";
    if ([btnText isEqualToString:@"店铺"]) {
        scmEntity.scmD = [NSString stringWithFormat:@"text-%@_shop-%@", btnText, model.shopCode];
    } else {
        scmEntity.scmD = [NSString stringWithFormat:@"text-%@", btnText];
    }
    trackData.scmEntity = scmEntity;
    
    NSString *spm = [YBMQTTrack createSpmOrScmWithCmsModel:trackData trackType:YBMTrackTypeSPM];
    NSString *scm = [YBMQTTrack createSpmOrScmWithCmsModel:trackData trackType:YBMTrackTypeSCM];
    NSMutableDictionary *qtDic = NSMutableDictionary.new;
    qtDic[@"spm_cnt"] = spm;
    qtDic[@"scm_cnt"] = scm;
    return qtDic;
}

/// scm&spm参数
- (NSDictionary *)getSameProductAddBuyParams {
    NSMutableDictionary *mutDict = [NSMutableDictionary dictionary];
    mutDict[@"spmB"] = [NSString stringWithFormat:@"productDetail_%@-0_0", self.addBuyModel.ID];
    mutDict[@"spmC"] = @"ftProdDetail@Z";
    mutDict[@"scmA"] = @"appFE";
    return [mutDict copy];
}

#pragma mark - Lazy

- (YBMVerticalButton *)serviceButton {
    if (!_serviceButton) {
        _serviceButton = [YBMVerticalButton buttonWithType:UIButtonTypeCustom];
        [_serviceButton setTitleColor:ColorText_292933 forState:UIControlStateNormal];
        _serviceButton.titleLabel.font = [UIFont fontWithName:FontTypePingFangRe size:12];
        [_serviceButton setImage:[UIImage imageNamed:@"service"] forState:UIControlStateNormal];
        [_serviceButton setTitle:@"客服" forState:UIControlStateNormal];
//        _serviceButton.offsetY = -5;
        [_serviceButton addTarget:self action:@selector(serviceActionButton) forControlEvents:UIControlEventTouchUpInside];
        _serviceButton.hidden = YES;
    }
    return _serviceButton;
}

- (YBMVerticalButton *)addOftenBuyButton {
    if (!_addOftenBuyButton) {
        _addOftenBuyButton = [YBMVerticalButton buttonWithType:UIButtonTypeCustom];
        [_addOftenBuyButton setTitleColor:ColorText_292933 forState:UIControlStateNormal];
        _addOftenBuyButton.titleLabel.font = [UIFont fontWithName:FontTypePingFangRe size:12];
        [_addOftenBuyButton setImage:[UIImage imageNamed:@"shangpinxiangqing_qingdan_btn_n"] forState:UIControlStateNormal];
        [_addOftenBuyButton setTitle:@"加常购" forState:UIControlStateNormal];
        [_addOftenBuyButton addTarget:self action:@selector(addOftenBuyActionButton) forControlEvents:UIControlEventTouchUpInside];
        _addOftenBuyButton.hidden = YES;
    }
    return _addOftenBuyButton;
}

- (YBMVerticalButton *)shopButton {
    if (!_shopButton) {
        _shopButton = [YBMVerticalButton buttonWithType:UIButtonTypeCustom];
        [_shopButton setTitleColor:ColorText_292933 forState:UIControlStateNormal];
        _shopButton.titleLabel.font = [UIFont fontWithName:FontTypePingFangRe size:12];
        [_shopButton setImage:[UIImage imageNamed:@"ic_detail_shop_car"] forState:UIControlStateNormal];
        [_shopButton setTitle:@"购物车" forState:UIControlStateNormal];
        [_shopButton addTarget:self action:@selector(shopActionButton) forControlEvents:UIControlEventTouchUpInside];
        _shopButton.hidden = NO;
    }
    return _shopButton;
}

- (XYYAddSubView *)addsubView {
    if (!_addsubView) {
        _addsubView = [[XYYAddSubView alloc] initWithType:ADDSUB_BIG andHight:50];
        _addsubView.isNotNeedAnimation = YES;
        _addsubView.recView = self;
        _addsubView.isColor = YES;
        _addsubView.imageAdd.image = [UIImage imageNamed:@"ic_detail_plus"];
        _addsubView.imageSub.image = [UIImage imageNamed:@"ic_detail_less"];
        _addsubView.imageAdd.backgroundColor = [UIColor clearColor];
        _addsubView.imageSub.backgroundColor = [UIColor clearColor];
        _addsubView.contentView.backgroundColor = [UIColor clearColor];
        _addsubView.fieldBackView.backgroundColor = [UIColor whiteColor];
        _addsubView.fieldCount.textColor = ColorText_292933;
        _addsubView.soruceFrom = YBMAnalysisShoppingSourceTypeFromDetail;
        
        UIButton *button = self.addsubView.btnAdd;
        //移除_addsubView里，加号按钮的所有点击事件
        [button removeTarget:self.addsubView action:@selector(tap) forControlEvents:UIControlEventAllEvents];
        [button addTarget:self action:@selector(showAddView) forControlEvents:UIControlEventTouchUpInside];
    }
    return _addsubView;
}

- (UIButton *)normalAddToCartButton {
    if (!_normalAddToCartButton) {
        _normalAddToCartButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_normalAddToCartButton setTitle:@"加入购物车" forState:UIControlStateNormal];
        [_normalAddToCartButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        _normalAddToCartButton.titleLabel.font = [UIFont fontWithName:FontTypePingFangMe size:16];
        _normalAddToCartButton.backgroundColor = [UIColor colorWithHexString:@"#00B955"];
        [_normalAddToCartButton addTarget:self action:@selector(normalAddToCartButtonClick:) forControlEvents:UIControlEventTouchUpInside];
        _normalAddToCartButton.layer.cornerRadius = 5.f;
        _normalAddToCartButton.layer.masksToBounds = YES;
    }
    return _normalAddToCartButton;
}

- (UIButton *)baseStatusButton {
    if (!_baseStatusButton) {
        _baseStatusButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_baseStatusButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        _baseStatusButton.titleLabel.font = [UIFont systemFontOfSize:16];
        _baseStatusButton.backgroundColor = [UIColor colorWithHexString:@"0x00B955"];
        [_baseStatusButton addTarget:self action:@selector(baseStatusActionButton:) forControlEvents:UIControlEventTouchUpInside];
        _baseStatusButton.layer.cornerRadius = 5.f;
        _baseStatusButton.layer.masksToBounds = YES;
        _baseStatusButton.hidden = YES;
    }
    return _baseStatusButton;
}

- (UIButton *)leftSpellGroupButton {
    if (!_leftSpellGroupButton) {
        _leftSpellGroupButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_leftSpellGroupButton setTitle:@"立即参团" forState:UIControlStateNormal];
        [_leftSpellGroupButton setTitleColor:[UIColor colorWithHexString:@"0x00B955"] forState:UIControlStateNormal];
        _leftSpellGroupButton.titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:16];
        _leftSpellGroupButton.backgroundColor = [UIColor colorWithHexString:@"0xFFFFFF"];
        [_leftSpellGroupButton addTarget:self action:@selector(baseStatusActionButton:) forControlEvents:UIControlEventTouchUpInside];
        _leftSpellGroupButton.layer.cornerRadius = 5.f;
        _leftSpellGroupButton.layer.borderWidth = 1;
        _leftSpellGroupButton.layer.borderColor = [UIColor colorWithHexString:@"0x00B955"].CGColor;
    }
    return _leftSpellGroupButton;
}

- (UIButton *)rightSpellGroupButton {
    if (!_rightSpellGroupButton) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        [btn setTitle:@"随心拼" forState:UIControlStateNormal];
        [btn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        btn.titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:16];
        btn.backgroundColor = [UIColor colorWithHexString:@"0x00B955"];
        [btn addTarget:self action:@selector(baseStatusActionButton:) forControlEvents:UIControlEventTouchUpInside];
        btn.layer.cornerRadius = 5.f;
        
        [btn addSubview:self.discountImgView];
        [self.discountImgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.mas_equalTo(btn.mas_top).offset(3);
            make.leading.mas_equalTo(btn.mas_centerX);
        }];
        _rightSpellGroupButton = btn;
    }
    return _rightSpellGroupButton;
}

- (UIImageView *)discountImgView{
    if (!_discountImgView) {
        UIImageView *imgView = [[UIImageView alloc]init];
        imgView.image = [UIImage imageNamed:@"detail_chat"];
        imgView.hidden = YES;
        [imgView addSubview:self.discountLab];
        [self.discountLab mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.insets(UIEdgeInsetsMake(0, 2, 3, 2));
        }];
        _discountImgView = imgView;
    }
    return _discountImgView;
}

- (UILabel *)discountLab{
    if (!_discountLab) {
        UILabel *lab = [[UILabel alloc]init];
        lab.textColor = UIColor.whiteColor;
        lab.font = [UIFont boldSystemFontOfSize:11];
        lab.textAlignment = NSTextAlignmentCenter;
        lab.hidden = YES;
//        lab.text = @"89折";
        _discountLab = lab;
    }
    return _discountLab;
}

- (UIButton *)addToCartButton {
    if (!_addToCartButton) {
        _addToCartButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_addToCartButton setTitle:@"加入购物车" forState:UIControlStateNormal];
        [_addToCartButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        _addToCartButton.titleLabel.font = [UIFont fontWithName:FontTypePingFangMe size:16];
        _addToCartButton.backgroundColor = [UIColor colorWithHexString:@"#00B955"];
        [_addToCartButton addTarget:self action:@selector(addToCartButtonClick:) forControlEvents:UIControlEventTouchUpInside];
        _addToCartButton.layer.cornerRadius = 5.f;
        _addToCartButton.layer.masksToBounds = YES;
    }
    return _addToCartButton;
}

- (UIView *)spellGroupBaseView {
    if (!_spellGroupBaseView) {
        _spellGroupBaseView = [[UIView alloc] init];
        _spellGroupBaseView.backgroundColor = [UIColor colorWithHexString:@"0xFFFFFF"];
        _spellGroupBaseView.hidden = YES;
    }
    return _spellGroupBaseView;
}

- (UIView *)lineView {
    if (!_lineView) {
        _lineView = [UIView new];
        _lineView.backgroundColor = [UIColor colorWithHexString:@"#EEEEEE"];
    }
    return _lineView;
}

- (UILabel *)notBuyLabel {
    if (!_notBuyLabel) {
        _notBuyLabel = [[UILabel alloc] init];
        _notBuyLabel.backgroundColor = [UIColor colorWithHexString:@"#A9AEB7"];
        _notBuyLabel.text = @"无法加购";
        _notBuyLabel.font = [UIFont systemFontOfSize:16];
        _notBuyLabel.textColor = [UIColor whiteColor];
        _notBuyLabel.textAlignment = NSTextAlignmentCenter;
        _notBuyLabel.hidden = YES;
    }
    return _notBuyLabel;
}

-(YBMVerticalButton*)storeButton{
    if (!_storeButton) {
        _storeButton = [YBMVerticalButton buttonWithType:UIButtonTypeCustom];
        [_storeButton setTitleColor:ColorText_292933 forState:UIControlStateNormal];
        _storeButton.titleLabel.font = [UIFont fontWithName:FontTypePingFangRe size:12];
        [_storeButton setImage:[UIImage imageNamed:@"store_tool_button"] forState:UIControlStateNormal];
        [_storeButton setTitle:@"店铺" forState:UIControlStateNormal];
        [_storeButton addTarget:self action:@selector(storeActionButton) forControlEvents:UIControlEventTouchUpInside];
        _storeButton.hidden = NO;
    }
    return _storeButton;
}

-(YBMVerticalButton*)shareButton{
    if (!_shareButton) {
        _shareButton = [YBMVerticalButton buttonWithType:UIButtonTypeCustom];
        [_shareButton setTitleColor:ColorText_292933 forState:UIControlStateNormal];
        _shareButton.titleLabel.font = [UIFont fontWithName:FontTypePingFangRe size:12];
        [_shareButton setImage:[UIImage imageNamed:@"share_tool_button"] forState:UIControlStateNormal];
        [_shareButton setTitle:@"分享" forState:UIControlStateNormal];
        [_shareButton addTarget:self action:@selector(shareActionButton) forControlEvents:UIControlEventTouchUpInside];
        _shareButton.hidden = YES;
    }
    return _shareButton;
}

@end
