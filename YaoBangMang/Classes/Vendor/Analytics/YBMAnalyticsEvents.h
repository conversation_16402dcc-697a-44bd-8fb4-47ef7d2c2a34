//
//  YBMAnalyticsEvents.h
//  YaoBangMang
//
//  Created by 骆阳光 on 2020/3/3.
//  Copyright © 2020 XiaoYaoYao.Ltd. All rights reserved.
//

#ifndef YBMAnalyticsEvents_h
#define YBMAnalyticsEvents_h


#define kSearch_sort_strategy_id @"search_sort_strategy_id" //自定义属性。搜索属性。A/B策略ID。例："main_precise_sort_A"

//页面及事件名称（可后期增加）
#pragma mark -  用户信息参数
#define kphoneNum @"phoneNum" //手机号
#define kbusinessType @"businessType" //药店类型
#define kbusinessTypeName @"businessTypeName" //药店类型名称
#define kaddress @"address" //地址
#define kprovinceId @"provinceId" //省id
#define kprovinceName @"provinceName" //省名称
#define kcityId @"cityId" //市id
#define kcityName @"cityName" //市名称
#define kdistrictId @"districtId" //区/县id
#define kdistrictName @"districtName" //区/县名称
#define kregisteredDate @"registeredDate" //注册时间
#define kchannelCode @"channelCode" //当前渠道

#pragma mark - 页面相关

#define kPV @"PV" //总页面浏览量
#define ktotal_Time @"total_Time" //总访问时长
#define kduration @"duration" //单个页面访问时长
#define kpageName @"pageName" //访问页面名称
#define kpageStartD @"pageStartD" //记录页面访问开始时间
#define kpageEntryName @"pageEntryName"//页面跳转入口key值
#define kpage_HomePage @"page_HomePage" //首页  YBMHomeV3Controller
#define Kpage_ChannelPage @"page_ChannelPage" //频道页   YBMHomeChannelController
#define kpage_AllDrugs @"page_AllDrugs" //全部药品 XYYUpdateNewAllProductVC
#define kpage_ShoppingCart @"page_ShoppingCart"//采购单 YBMShoppingCartAdapter
#define kpage_Me @"page_Me"//我的 YBMNewMeVC

#define kpage_WebActivity @"page_WebActivity"//H5活动页面  XYYWebViewVC

#define kpage_ConfirmedOrder @"page_ConfirmedOrder"//待确认订单页 XYYPendingOrderViewController
#define kpage_Coupon @"page_Coupon"//选择优惠券页 XYYPlanCouponViewController
#define kpage_Cashier @"page_Cashier"//收银台 XYYPaymentViewController
#define kpage_Success_OnLine @"page_Success_On-line"//支付成功（线上） XYYPlanOrderSucceedViewController
#define kpage_Success_UnderLine @"page_Success_Under-line"//支付成功（线下） XYYOfflinePayViewController
#define kpage_Success_pay_Coupon_Click @"pay_Coupon_Click" //支付成功页优惠券点击
#define kpage_Success_pay_Image_Click @"pay_Image_Click" //支付成功页横幅广告点击
#define kpage_Success_pay_Coupon_Exposure @"pay_Coupon_Exposure" //支付成功页优惠券曝光
#define kpage_Success_pay_Image_Exposure @"pay_Image_Exposure" //支付成功页横幅广告曝光


#define kpage_OrderList @"page_OrderList"//我的订单（我的订单）XYYAllShopViewController
#define kpage_OrderDetails @"page_OrderDetails"//订单详情  XYYOrderdetailViewController
#define kpage_OrderTracking @"page_OrderTracking"//订单跟踪（物流）  XYYOrderTrackViewController
#define kpage_OrderInvoiceList @"page_OrderInvoiceList"//订单发票列表   YBMOrderBillInfoVC
#define kpage_OrderHistoryPrice @"page_OrderHistoryPrice"//订单入库价明细  XYYWarehousingPriceViewController
#define kpage_OrderChooseRefundableItems @"page_OrderChooseRefundableItems"//订单选择退款商品   XYYRefundShopViewController
#define kpage_OrderReviewSubmission @"page_OrderReviewSubmission"//订单评价提交   XYYCommentViewController
#define kpage_OrderReview @"page_OrderReview"//订单评价详情   XYYCommentDetailsViewController
#define kpage_OrderSearch @"page_OrderSearch"//订单搜索    XYYAllShopSearchResultVC
#define kpage_OrderRefundSingle @"page_OrderRefundSingle"//订单退款详情  XYYRefundOrderDetailViewController
#define kpage_OrderRefundGoods @"page_OrderRefundGoods"//订单退款商品明细  XYYRefundOrderProductListViewController

#define kpage_OrderRefundRequest @"page_OrderRefundRequest"//订单申请退款  XYYRefundViewController
#define kpage_OrderRefundPaymentAccount @"page_OrderRefundPaymentAccount"//订单编辑退款收款账户信息  XYYRefundViewController
#define kpage_OrderRefundLogistics @"page_OrderRefundLogistics"//订单填写/编辑退款物流信息  XYYReturnLogisticsController

#define kpage_OrderPostalCertificate @"page_OrderPostalCertificate"//订单资质上传邮寄凭证   mailingCertificateController
#define kpage_OrderTransactionDetail @"page_OrderTransactionDetail"//订单转账说明   XYYPaymentInfoViewController

#define kpage_ConfirmedOrderGoods @"page_ConfirmedOrderGoods"//待确认订单商品明细 XYYProductKindsViewController
#define kpage_ConfirmedOrderSelectAddress @"page_ConfirmedOrderSelectAddress"//待确认订单选择地址   XYYAddressListViewController   -----------

#define kpage_MeSurplusMoney @"page_MeSurplusMoney"//我的余额    XYYMyBalanceViewController
#define kpage_MeNotReceiveMoney @"page_MeNotReceiveMoney"//待领取余额   XYYMyBalanceListViewController --------
#define kpage_MeCumulativeMoney @"page_MeCumulativeMoney"//累计余额  XYYMyBalanceListViewController -------

#define kpage_MeCouponsAvailableGoods @"page_MeCouponsAvailableGoods"//优惠券可用商品  YBMMyCouponSearchController

#define kpage_MeIntegralSignIn @"page_MeIntegralSignIn"//积分签到  XYYCheckInViewController

#define kpage_CommodityDetailsPriceFallingN @"page_CommodityDetailsPriceFallingN"//商品详情页降价通知   XYYReducePriceViewController
#define kpage_VoiceSearch @"page_VoiceSearch"//语音搜索  XYYVoiceSearchVC

#define kpage_AccountManagement @"page_AccountManagement"//账号管理  XYYAccountViewController
#define kpage_AddAccount @"page_AddAccount"//添加账号  AccountAddViewController
#define kpage_Set @"page_Set"//设置 XYYCenterOtherVC
#define kpage_SetChangePassword @"page_SetChangePassword"//设置修改密码  YBMResetPasswordController ------
#define kpage_SetNewPassword @"page_SetNewPassword"//设置新密码  YBMResetPasswordController ------
#define kpage_SetMessageN @"page_SetMessageN"//设置新消息通知  XYYNewMessagePushViewController
#define kpage_SetAboutUs @"page_SetAboutUs"//设置关于我们    XYYAboutVC
#define kpage_Login @"page_Login"//登录  YBMLoginController
#define kpage_Registered @"page_Registered"//注册   YBMRegisterController
#define kpage_ForgotPassword @"page_ForgotPassword"//忘记密码/找回密码  YBMPasswordChangeController

#define kpage_MeElectronicPlanSheetHand @"page_MeElectronicPlanSheetHand"//电子计划单手动登记    XYYEplanNOProductVC
#define kpage_Scan @"page_Scan"//扫一扫      YBMScanningController
#define kpage_MeElectronicPlanSearch @"page_MeElectronicPlanSearch"//电子计划搜索     XYYEplanSearchVC
#define kpage_MeElectronicPlanSearchS @"page_MeElectronicPlanSearchS"//电子计划搜索规格选择     XYYEplanSearchSizeVC

#define kpage_MeOftenBuy @"page_MeOftenBuy"//常用采购列表  XYYCommonBuyVC
#define kpage_MeControlSales @"page_MeControlSales"//我的控销   XYYControlPMainVC
#define kpage_MeCollection @"page_MeCollection"//我的收藏   XYYMyCollectionVC
#define kpage_MeAddressManagement @"page_MeAddressManagement"//地址管理  XYYAddressListViewController      -----------
#define kpage_MeAddressEditor @"page_MeAddressEditor"//地址编辑  XYYEditAddressViewController
#define kpage_MeQualificationAdministration @"page_MeQualificationAdministration"//资质管理 YBMQualificationHomeController
#define kpage_MeSupplier @"page_MeSupplier"//我的供应商列表   XYYSupplierViewController
#define kpage_MeSupplierD @"page_MeSupplierD"//我的供应商详情  XYYQualificationsDetailViewController
#define kpage_MeGiftPackage @"page_MeGiftPackage"//我的礼品包  XYYMyGiftViewController

#define kpage_MePriceProtectionRecord @"page_MePriceProtectionRecord"//保价护航申请记录   XYYInsurancePriceViewController
#define kpage_MePriceProtectionRecordD @"page_MePriceProtectionRecordD"//保价护航申请记录详情  XYYInsurancePriceDetailViewController   -----
#define kpage_MePriceProtectionChooseOrder @"page_MePriceProtectionChooseOrder"//保价护航选择申请订单 XYYInsurancePriceOrderListViewController
#define kpage_MePriceProtectionChooseGoods @"page_MePriceProtectionChooseGoods"//保价护航选择申请商品  XYYChooseAnApplicationViewController
#define kpage_MePriceProtectionApply @"page_MePriceProtectionApply"//申请保价护航   XYYInsurancePriceDetailViewController  ------
#define kpage_MePriceProtectionSearch @"page_MePriceProtectionSearch"//保价护航搜索   XYYInsurancePriceSearchViewController

#define kpage_MeServiceTerms @"page_MeServiceTerms"//服务条款  XYYMeServiceViewController
#define kpage_MeWishListAdd @"page_MeWishListAdd"//添加心愿单  WishListAddViewController
#define kpage_MeCommonProblems @"page_MeCommonProblems"//常见问题列表   XYYCommonProblemViewController
#define kpage_MeInvitedGift @"page_MeInvitedGift"//邀请有礼   XYYInvitedGiftViewController
#define kpage_MeInvitedGiftSMSInviteFriends @"page_MeInvitedGiftSMSInviteFriends"//邀请有礼短信邀请好友   XYYSMSInviteFriendsViewController
#define kpage_MeInvitedGiftRewardPoolInvitationSuccessful @"page_MeInvitedGiftRewardPoolInvitationSuccessful"//邀请有礼奖励池邀请成功   XYYInvitedViewController
#define kpage_MeInvitedGiftRewardPoolRewards @"page_MeInvitedGiftRewardPoolRewards"//邀请有礼奖励池获得奖励   XYYEarnRewardsViewController

#define kpage_Store @"page_Store"//店铺  XYYShopViewController
#define kpage_StoreSearch @"page_StoreSearch"//店铺搜索   XYYStoreSearchViewController

#define kpage_ClinicZone @"page_ClinicZone"//诊所专区  XYYClinicAreaViewController
#define kpage_ExchangePhone @"kpage_ExchangePhone" //更换手机号 XYYChangePhoneVC
#define kpage_RealNameWeb   @"RealNameWeb"   //实名制H5 XYYRealNameWebVC

#define kpage_TransactionSnapshotList @"page_TransactionSnapshotList" //交易快照列表 XYYTradingSnapshotViewController
#define kpage_TransactionSnapshotDetail @"page_TransactionSnapshotDetail" //交易快照详情 XYYTradingSnapshotDetailController
#define kpage_GoodsCorrectionList @"page_GoodsCorrectionList" //商品纠错列表 XYYGoodsCorrectionController
#define kpage_GoodsCorrectionPrice @"page_GoodsCorrectionPrice" //商品纠错价格 XYYGoodsCorrectionDetailController
#define kpage_GoodsCorrectionMessage @"page_GoodsCorrectionMessage" //商品纠错商品信息 XYYGoodsCorrectionDetailController
#define kpage_GoodsCorrectionOther @"page_GoodsCorrectionOther" //商品纠错其他 XYYGoodsCorrectionDetailController



#pragma mark - 点击事件相关
#define kaction_Register @"action_Register"//注册
#define kaction_Activation @"action_Activation"//激活
#define kaction_Login @"action_Login"//登录
#define kaction_Exit @"action_Exit"//退出
//#define kaction_Search @"action_Search"//搜索
#define ksearchTerms @"searchTerms" //搜索词参数名
//#define ksource @"source" //搜索来源(1:输入2:历史3:推荐4:联想5语音 6关键词纠错无结果 7关键词纠错人工映射 8月历史来源)
#define kgoodsNumber @"goodsNumber" //商品总数
#define kposition @"position" //搜索热词，历史，联想点击顺序（第几个）
#define klenovoWord @"lenovoWord" //联系词
#define kkeyWordHitType @"keyWordHitType" //关键词纠错
#define krewriteWord @"rewriteWord" //更改后的关键词

#define kaction_SearchStartup_ShopWindow_Exposure @"action_SearchStartup_ShopWindow_Exposure" //大搜启动页拼团专栏-曝光
#define kaction_SearchStartup_ShopWindow_Click @"action_SearchStartup_ShopWindow_Click" //大搜启动页拼团专栏-点击
#define kaction_SearchStartup_chartCommodity_Exposure @"action_SearchStartup_chartCommodity_Exposure"//大搜启动页榜单商品-曝光
#define kaction_SearchStartup_chart_Click @"action_SearchStartup_chart_Click"//大搜启动页榜单入口-点击
#define kaction_SearchStartup_chartEntrance_Click @"action_SearchStartup_chartEntrance_Click"//大搜启动页榜单品类入口-点击
#define kaction_SearchStartup_chartCommodity_Click @"action_SearchStartup_chartCommodity_Click"//大搜启动页榜单商品-点击



#define kaction_HomePage @"action_HomePage" //首页
#define kaction_AllDrugs @"action_AllDrugs" //全部药品
#define kpaction_Find @"action_Find" //发现
#define kaction_ShoppingCart @"action_ShoppingCart"//采购单点击
#define kpage_ShoppingCart @"page_ShoppingCart"//采购单页面曝光
#define kaction_Me @"action_Me"//我的

#define kaction_CommodityDetails @"action_CommodityDetails"//查看商品详情
#define kaction_AddCart @"action_AddCart"//加入购物车
#define kaction_SubmitOrder @"action_SubmitOrder"//提交订单
#define kaction_SuccessfulPayment @"action_SuccessfulPayment"//付款成功（线上）
#define kcommodityList @"commodityList"//商品list
#define kcommodityName @"commodityName"//商品名称
#define kcommodityCode @"commodityCode"//商品编码
#define kcommodityBrand @"commodityBrand"//商品品牌
#define kcommodityCategory @"commodityCategory"//商品类别
#define kcommodityLevel @"commodityLevel"//商品等级
#define kactivityCategory @"activityCategory"//活动类别
#define kpurchaseQuantity @"purchaseQuantity"//加购数量
#define kgoodsAmount @"goodsAmount"//商品金额
#define kcouponFaceValue @"couponFaceValue"//优惠券面额
#define kfullReduction @"fullReduction"//满减
#define kbalanceDeduction @"balanceDeduction"//余额抵扣
#define krebate @"rebate"//返利
#define kfactoryCost @"factoryCost" //厂家费用
#define ktotalOrderAmount @"totalOrderAmount"//订单总金额
#define krealPayAmount @"realPayAmount"//实付金额
#define kaction_Cancel_Order @"action_Cancel_Order"//取消订单
#define korderId @"orderId"//订单ID
#define korderNo @"orderNo"//订单编号
#define kactionStatus @"actionStatus"//订单状态（1:取消成功，2:取消失败）
#define action_shopShare_click @"action_shopShare_click" //店铺首页分享
#define shopHome_Menu_Click @"shopHome_Menu_Click" //店铺首页tab点击
#define shopHome_Coupon_Click @"shopHome_Coupon_Click"//用户点击领券后，上报该事件
#define shopHome_Coupon_Exposure @"shopHome_Coupon_Exposure" //店铺首页优惠券曝光（移动端）
#define shopHome_FloorTab_Exposure @"shopHome_FloorTab_Exposure" //店铺首页商品楼层曝光（移动端）
#define shopHome_FloorTab_Click @"shopHome_FloorTab_Click" //店铺首页商品楼层Tab点击（移动端

#define kshopHome_open @"shopHome_open" //店铺首页打开
#define kshopHome_Banner_Click @"shopHome_Banner_Click" //店铺首页轮播-点击
#define kshopHome_pictureCard_Click @"shopHome_pictureCard_Click" //店铺首页图片楼层-点击

#define kPage_SelectedShops_Exposure @"page_SelectedShops_Exposure"//首页选项卡店铺曝光
#define kPage_SelectedShopsDrug_Exposure @"page_SelectedShopsDrug_Exposure"//首页选项卡商品曝光
#define kPage_SelectedShops_Click @"page_SelectedShops_Click"//首页选项卡店铺点击
#define kPage_SelectedShopsDrug_Click @"page_SelectedShopsDrug_Click"//首页选项卡商品点击

#define kaction_payPopup_Exposure @"action_payPopup_Exposure" //支付成功发券弹窗曝光（移动端）
#define kaction_payPopup_Click @"action_payPopup_Click" //支付成功发券弹窗点击（移动端


#define kaction_AllDrugs_ClassOne @"action_AllDrugs_ClassOne"//全部药品-一级分类点击
#define kaction_AllDrugs_ClassTwo @"action_AllDrugs_ClassTwo"//全部药品-二级分类点击
#define kaction_AllDrugs_Screen @"action_AllDrugs_Screen"//全部药品-筛选点击
#define kaction_AllDrugs_Search @"action_AllDrugs_Search"//全部药品-搜索框
#define kaction_AllDrugs_Message @"action_AllDrugs_Message"//全部药品-消息
#define kaction_AllDrugs_Scan @"action_AllDrugs_Scan"//全部药品-扫一扫

#define kaction_CommodityDetails_Search @"action_CommodityDetails_Search"//商品详情页-搜索框

#define kaction_Find_PurchaseHistory @"action_Find_PurchaseHistory"//发现-我的清单点击
#define kaction_Find_Recommend @"action_Find_Recommend"//发现-为你推荐点击
#define kaction_Find_CombinedMedication @"action_Find_CombinedMedication"//发现-联合用药点击

#define kaction_Me_RefundAfterSale @"action_Me_RefundAfterSale"//我的-退款/售后
#define kaction_Me_CustomerService @"action_Me_CustomerService"//我的-在线客服
#define kaction_Me_TriadAgreement @"action_Me_TriadAgreement"//我的-三合一协议
#define kaction_Me_ElectronicPlanSheet @"action_Me_ElectronicPlanSheet"//我的-电子计划单
#define kaction_Me_StrategicCooperationAgreement @"action_Me_StrategicCooperationAgreement"//我的-战略合作协议
#define kaction_Me_CollegeOfPharmacy @"action_Me_CollegeOfPharmacy"//我的-药学院
#define kaction_Me_IntegralSignIn @"action_Me_IntegralSignIn"//我的-积分签到
#define kaction_Me_Coupons @"action_Me_Coupons"//我的-优惠券
#define kaction_Me_SurplusMoney @"action_Me_SurplusMoney"//我的-我的余额
#define kaction_Me_Message @"action_Me_Message"//我的-消息中心
#define kpage_orderBoard_Click @"page_orderBoard_Click"//我的-订单通知栏-订单点击事件

#define kaction_Me_NoPaid @"action_Me_NoPaid"//我的-待支付
#define kaction_Me_NoDistribution @"action_Me_NoDistribution"//我的-待配送
#define kaction_Me_InDistribution @"action_Me_InDistribution"//我的-配送中
#define kaction_Me_Evaluation @"action_Me_Evaluation"//我的-待评价
#define kaction_Me_AllOrders @"action_Me_AllOrders"//我的-全部订单
#define kaction_Me_OftenBuy @"action_Me_OftenBuy"//我的-常用采购
#define kaction_Me_ControlSales @"action_Me_ControlSales"//我的-控销专区
#define kaction_Me_Collection @"action_Me_Collection"//我的-我的收藏
#define kaction_Me_AddressManagement @"action_Me_AddressManagement"//我的-地址管理
#define kaction_Me_QualificationAdministration @"action_Me_QualificationAdministration"//我的-战略合作协议/资质管理
#define kaction_Me_QualificationAdministrationDetail @"action_Me_QualificationAdministrationDeatil"//我的-资质管理-资质详情
#define kaction_Me_Supplier @"action_Me_Supplier"//我的-我的供应商
#define kaction_Me_ExclusiveSales @"action_Me_ExclusiveSales"//我的-专属销售
#define kaction_Me_GiftPackage @"action_Me_GiftPackage"//我的-我的礼品包/物料心愿单
#define kaction_Me_PriceProtection @"action_Me_PriceProtection"//我的-保价护航
#define kaction_Me_AfterSalesRules @"action_Me_AfterSalesRules"//我的-售后规则
#define kaction_Me_ControlPriceRules @"action_Me_ControlPriceRules"//我的-控价规则
#define kaction_Me_ServiceTerms @"action_Me_ServiceTerms"//我的-服务条款
#define kaction_Me_Feedback @"action_Me_Feedback"//我的-意见反馈
#define kaction_Me_WishList @"action_Me_WishList"//我的-心愿单
#define kaction_Me_CommonProblems @"action_Me_CommonProblems"//我的-常见问题
#define kaction_Me_CustomerServiceTelephone @"action_Me_CustomerServiceTelephone"//我的-客服电话
#define kaction_Me_InvitedGift @"action_Me_InvitedGift"//我的-邀请有礼
#define kaction_Me_InvitedGiftSMSInviteFriends @"action_Me_InvitedGiftSMSInviteFriends"//邀请有礼-短信邀请好友
#define kaction_Me_InvitedGiftRewardPoolInvitationSuccessful @"action_Me_InvitedGiftRewardPoolInvitationSuccessful"//邀请有礼-奖励池邀请成功
#define kaction_Me_InvitedGiftRewardPoolRewards @"action_Me_InvitedGiftRewardPoolRewards"//邀请有礼-奖励池获得奖励

#define kaction_Home_HotSearchWord @"action_Home_HotSearchWord" //CMS-热搜词点击
#define kaction_Home_Banner @"action_Home_Banner"//首页/CMS-轮播---1004
#define kaction_Home_newBanner @"action_Home_newBanner"//首页/CMS-轮播---1004
#define kaction_Home_Shortcut @"action_Home_Shortcut"//首页/CMS-快捷入口---1001 金刚位
#define kaction_Home_newShortcut @"action_Home_newShortcut"//首页/CMS-快捷入口---1001 金刚位

#define kaction_Home_Streamer1 @"action_Home_Streamer1"//胶囊位(上)点击事件
#define kaction_Home_Streamer2 @"action_Home_Streamer2"//胶囊位(下)点击事件


//定时活动（限时秒杀）曝光&点击事件
#define kHome_timingActivity @"action_Home_timingActivity"//顶栏更多入口
#define kHome_timingActivitymore_click @"action_Home_timingActivitymore_click"//商品后更多入口
#define kHome_timingActivity_Exposure @"action_Home_timingActivity_Exposure"//商品完全露出屏幕
#define kHome_timingActivity_click @"action_Home_timingActivity_click"//打开商品详情页

//精选店铺曝光&点击事件
#define kHome_carefullySelectedShops_click @"action_Home_carefullySelectedShops_click"//精选店铺顶栏更多入口
#define kHome_carefullySelectedShops @"action_Home_carefullySelectedShops"//精选店铺入口
#define kHome_carefullySelectedShops_Exposure @"action_Home_carefullySelectedShops_Exposure"//店铺完全露出屏幕

//商品流选项卡点击事件
#define kHome_recommendTab @"action_Home_recommendTab"//tab被点击
#define kpage_recommendTab_Exposure @"page_recommendTab_Exposure"//店铺完全露出屏幕
//#define kpage_recommendTab_click @"page_recommendTab_click"//打开商品详情页
/// 商品详情页加载完成
#define kPage_CommodityDetails_o @"page_CommodityDetails_o"
#define kOrder_detail @"order_detail"//商品详情页
#define kaction_Sku_Share_Click @"action_Sku_Share_Click" ///商品详情页分享

#define kaction_CommodityDetails_autoPlay @"action_CommodityDetails_autoPlay"//商详视频-自动播放
#define kaction_CommodityDetails_play @"action_CommodityDetails_play"//商详视频-播放
#define kaction_CommodityDetails_fullScreen @"action_CommodityDetails_fullScreen"//商详视频-全屏播放


#define kaction_Home_Image1 @"action_Home_Image_1"//首页/CMS(首页一图，幅广告)-图片模块-1图--1003
#define kaction_Home_Image2_LeftOneRightOne @"action_Home_Image_2_leftOneRightOne"//首页/CMS-图片模块二图左1右1---1003
#define kaction_Home_Image2_TopOneBottomOne @"action_Home_Image_2_topOneBottomOne"//首页-图片模块二图上1下1---1003
#define kaction_Home_Image_3_leftOneRightTwo_LR @"action_Home_Image_3_leftOneRightTwo_LR"//首页/CMS-图片模块三图-左1右2-左右---1003
#define kaction_Home_Image_3_leftTwoRightOne_LR @"action_Home_Image_3_leftTwoRightOne_LR"//首页-图片模块-3图-左2右1-左右---1003
#define kaction_Home_Image_3_leftOneRightTwo_UD @"action_Home_Image_3_leftOneRightTwo_UD"//首页/CMS-图片模块-3图-左1右2-上下---1003
#define kaction_Home_Image_3_leftTwoRightOne_UD @"action_Home_Image_3_leftTwoRightOne_UD"//首页-图片模块-3图-左2右1-上下---1003
#define kaction_Home_Image_3_topTwoBottomOne_UD @"action_Home_Image_3_topTwoBottomOne_UD"//首页-图片模块-3图-上2下1-上下---1003
#define kaction_Home_Image_3_topOneBottomTwo_UD @"action_Home_Image_3_topOneBottomTwo_UD"//首页-图片模块-3图-上1下2-上下---1003
#define kaction_Home_Image_3_topOneBottomTwo_LR @"action_Home_Image_3_topOneBottomTwo_LR"//首页-图片模块-3图-上1下2-左右---1003
#define kaction_Home_Image_3_topTwoBottomOne_LR @"action_Home_Image_3_topTwoBottomOne_LR"//首页-图片模块-3图-上2下1-左右---1003
#define kaction_Home_Image_3_LR @"action_Home_Image_3_LR" //CMS-图片模块-3图-左右
#define kaction_Home_Image4_Square @"action_Home_Image_4_Square"//首页/CMS-图片模块-4图-4宫格 ---1003
#define kaction_Home_Image_4_UD @"action_Home_Image_4_UD"//首页-图片模块-4图-上下---1003
#define kaction_Home_Image_4_LR @"action_Home_Image_4_LR"//首页/CMS-图片模块-4图-4左右---1003

#define kaction_Home_Image_4_leftOneRightThree @"action_Home_Image_4_leftOneRightThree" //CMS-图片模块-4图-左1右3
#define kaction_Home_Image_5_leftOneRightFour @"action_Home_Image_5_leftOneRightFour" //CMS-图片模块-5图-左1右4
#define kaction_Home_TextTitle @"action_Home_TextTitle" //CMS-文字标题-更多活动
#define kaction_Home_ImageTitle @"action_Home_ImageTitle" //CMS-图片标题-精彩活动


#define kaction_Home_Headline @"action_Home_Headline"//首页/CMS-药头条---1002
#define kaction_Home_Main_Ad @"action_Home_Main_Ad"//首页-黄金广告位 /CMS-多图广告位---1005
#define kaction_Home_Seckill @"action_Home_Seckill"//首页/CMS-秒杀---2100
#define kaction_Home_Seckill_Product @"action_Home_Seckill_Product"//首页/CMS-秒杀商品---2100
#define kaction_Home_Collection_Product @"action_Home_Collection_Product"//首页-集合推荐-商品/CMS-横向商品展示-商品---2005
#define kaction_Home_Collection_Product_Title_Image @"action_Home_Collection_Product_Title_Image" //CMS集合推荐头图
#define kaction_Home_ItemShow_Product @"action_Home_ItemShow_Product"//首页-单品展示-商品/CMS-集合推荐-商品---2006
#define kaction_Home_Classification_sliding @"action_Home_Classification_sliding"//首页/CMS-分类滑动-商品---2037
#define kaction_Home_Classification_sliding_loadMore @"action_Home_Classification_sliding_loadMore"//首页/CMS-分类滑动-侧滑加载更多---2037
#define kaction_Home_Product @"action_Home_Product"//首页-商品/CMS-竖向商品列表商品---2001
#define kaction_Home_ActivityZone @"action_Home_ActivityZone" //CMS-活动专区
#define kaction_Home_PurchaseHistory @"action_Home_PurchaseHistory"//首页/CMS-常购清单
#define kaction_Home_Ph_Product @"action_Home_Ph_Product"//首页/CMS-常购-清单商品
#define kaction_Home_Recommend @"action_Home_Recommend"//首页/CMS-为你推荐
#define kaction_Home_R_Product @"action_Home_R_Product"//首页/CMS-为你推荐-商品
#define kaction_Home_Search @"action_Home_Search"//首页/CMS-搜索框
#define kaction_Home_Message @"action_Home_Message"//首页/CMS-消息
#define kaction_Home_Scan @"action_Home_Scan"//首页/CMS-扫一扫
#define kaction_Home_RotaryTable @"action_Home_RotaryTable"//首页-大转盘

#define kaction_Home_Card @"action_Home_Card"//活动入口/导购入口
#define kaction_Home_newCard @"action_Home_newCard"//活动入口/导购入口



#define kaction_Clinic_Banner @"action_Clinic_Banner"//诊所专区-轮播
#define kaction_Clinic_Image1 @"action_Clinic_Image_1"//诊所专区-图片模块一图---1003
#define kaction_Clinic_Image2_LeftOneRightOne @"action_Clinic_Image_2_leftOneRightOne"//诊所专区-图片模块二图左1右1---1003
#define kaction_Clinic_Image2_TopOneBottomOne @"action_Clinic_Image_2_topOneBottomOne"//诊所专区-图片模块二图上1下1---1003
#define kaction_Clinic_Image3_LeftOneRightTwo_LR @"action_Clinic_Image_3_leftOneRightTwo_LR"//诊所专区-图片模块三图-左1右2-左右---1003
#define kaction_Clinic_Image3_LeftTwoRightOne_LR @"action_Clinic_Image_3_leftTwoRightOne_LR"//诊所专区-图片模块三图-左2右1-左右---1003
#define kaction_Clinic_Image3_LeftOneRightTwo_UD @"action_Clinic_Image_3_leftOneRightTwo_UD"//诊所专区-图片模块三图-左1右2-上下---1003
#define kaction_Clinic_Image3_LeftTwoRightOne_UD @"action_Clinic_Image_3_leftTwoRightOne_UD"//诊所专区-图片模块三图-左2右1--上下---1003
#define kaction_Clinic_Image3_TopTwoBottomOne_LR @"action_Clinic_Image_3_topTwoBottomOne_LR"//诊所专区-图片模块三图-上2下1-左右---1003
#define kaction_Clinic_Image3_TopOneBottomTwo_LR @"action_Clinic_Image_3_topOneBottomTwo_LR"//诊所专区-图片模块三图-上1下2-左右---1003
#define kaction_Clinic_Image3_TopOneBottomTwo_UD @"action_Clinic_Image_3_topOneBottomTwo_UD"//诊所专区-图片模块三图-上1下2-上下---1003
#define kaction_Clinic_Image3_TopTwoBottomOne_UD @"action_Clinic_Image_3_topTwoBottomOne_UD"//诊所专区-图片模块三图-上2下1-上下---1003
#define kaction_Clinic_Image4_Square @"action_Clinic_Image_4_square"//诊所专区-图片模块四图-4宫格---1003
#define kaction_Clinic_Image4_LR @"action_Clinic_Image_4_LR"//诊所专区-图片模块四图-4竖排---1003
#define kaction_Clinic_Image4_UD @"action_Clinic_Image_4_UD"//诊所专区-图片模块四图-4横排---1003
#define kaction_Clinic_Product @"action_Clinic_Product"//诊所专区-商品

//搜索相关
#define kaction_Search_Original @"action_Search_Original" //搜索-默认
#define kaction_Search_Classification @"action_Search_Classification" //搜索-分类
#define kaction_Search_Price @"action_Search_Price" //搜索-价格
#define kaction_Search_Factory @"action_Search_Factory" //搜索-厂家
#define kaction_Search_Screen @"action_Search_Screen" //搜索-筛选
#define kaction_Search_New_Arrival @"action_Search_New_Arrival" //搜索-最新
#define kaction_Search_Sales_Volume @"action_Search_Sales_Volume" //搜索-销量
#define kaction_Search_Available @"action_Search_Available" //搜索-有货
#define kaction_Search_On_Sale @"action_Search_On_Sale" //搜索-促销
#define kaction_Search_Self_Support @"action_Search_Self_Support" //搜索-自营
#define kaction_Search_More_History @"action_Search_More_History" //搜索-查看更多

/// 数据统计和每日邮件报表：
#define page_Recommended_List @"page_Recommended_List" /// 推荐清单首
#define page_Recommended_History @"page_Recommended_History" /// 历史推荐清单
#define page_Recommended_Details @"page_Recommended_Details" /// 推荐清单-任务商品商品列表


/// 店铺列表-数据埋点
#define kNavigation_bstoreList @"_anavigation_bstoreList" /// 新版底部导航-“店铺列表”点击
#define kPage_ShopsList_pv @"page_ShopsList_pv" /// 店铺列表页浏览量
#define kPage_ShopsList_Exposure @"page_ShopsList_Exposure" /// 店铺列表-店铺曝光
#define kPgae_ShopsList_click @"pgae_ShopsList_click" /// 店铺列表-店铺点击
#define kPgae_proprietary_click @"pgae_proprietary_click" /// 店铺列表-“自营”tab点击
#define kPgae_businessPartner_click @"pgae_businessPartner_click" /// 店铺列表-“合作商家”tab点击
#define kPgae_default_click @"pgae_default_click" /// 店铺列表-“默认”筛选点击
#define kPgae_newest_click @"pgae_newest_click" /// 店铺列表-“最新”筛选点击
///

///店铺详情页橱窗
#define kPage_CommodityDetails_shopWindow_Exposure @"page_CommodityDetails_shopWindow_Exposure" //商详橱窗-曝光事件
#define kPage_CommodityDetails_shopWindowCommodity_Exposure @"page_CommodityDetails_shopWindowCommodity_Exposure" //商详橱窗中商品-曝光事件
#define kPage_CommodityDetails_shopWindow_Click @"page_CommodityDetails_shopWindow_Click" //商详橱窗-点击事件
#define kPage_CommodityDetails_shopWindowCommodity_Click @"page_CommodityDetails_shopWindowCommodity_Click" //商详橱窗中商品-点击事件


/// 新购物车埋点 9.1
#define kaction_ShoppingCart_PlatformCoupon_click @"action_ShoppingCart_PlatformCoupon_click" //购物车跨店券入口-点击事件
#define kaction_ShoppingCart_PlatformCouponItem_click @"action_ShoppingCart_PlatformCouponItem_click" //跨店券引导去凑单-点击事件
#define kaction_ShoppingCart_details_Click @"action_ShoppingCart_details_Click" //总计明细-点击事件
#define kaction_ShoppingCart_Settlement_Click @"action_ShoppingCart_Settlement_Click" //去结算-点击事件

#define kaction_ShoppingCart_Shop_Click @"action_ShoppingCart_Shop_Click" //购物车店铺入口-点击事件
#define kaction_ShoppingCart_Coupon_Click @"action_ShoppingCart_Coupon_Click" //购物车店铺优惠券入口-点击事件
#define kaction_ShoppingCart_PackUp_Click @"action_ShoppingCart_PackUp_Click" //收起/展开-点击事件
#define kaction_ShoppingCart_Post_Click @"action_ShoppingCart_Post_Click" //包邮注解-点击
#define kaction_ShoppingCart_PostItem_Click @"action_ShoppingCart_PostItem_Click" //包邮去凑单-点击

#define kaction_ShoppingCart_ReturnItem_Exposure @"action_ShoppingCart_ReturnItem_Exposure" //满返活动去凑单-曝光
#define kaction_ShoppingCart_ReturnItem_Click @"action_ShoppingCart_ReturnItem_Click" //满返活动去凑单-点击
#define kaction_ShoppingCart_ReductionItem_Exposure @"action_ShoppingCart_ReductionItem_Exposure" //满类活动去凑单-曝光
#define kaction_ShoppingCart_ReductionItem_Click @"action_ShoppingCart_ReductionItem_Click" //满类活动去凑单-点击

#define kaction_moreDiscount_giftCard_Exposure @"action_moreDiscount_giftCard_Exposure" //更多优惠-赠品卡片曝光
#define kaction_moreDiscount_giftCard_Click @"action_moreDiscount_giftCard_Click" //更多优惠-赠品卡片点击
#define kaction_groupPurchase_giftCard_Exposure @"action_groupPurchase_giftCard_Exposure" //拼团赠品卡片曝光
#define kaction_groupPurchase_giftCard_Click @"action_groupPurchase_giftCard_Click" //拼团赠品卡片点击
#define kpage_CommodityDetails_giftCard_Exposure @"page_CommodityDetails_giftCard_Exposure" //商详-赠品卡片曝光
#define kpage_CommodityDetails_giftCard_Click @"page_CommodityDetails_giftCard_Click" //商详-赠品卡片点击


#define kPintuan_newFloat_Exposure @"pintuan_newFloat_Exposure" //拼团或者批购包邮弹框展示
#define kPintuaNnewFloatExposureButtonClick @"pintuan_newFloat_Exposure_buttonClick"
#define PageShoppingCartCouponCoudanExposure @"page_shoppingCart_coupon_coudan_Exposure"
#define ActionShoppingCartCouponCoudanCategoryFloorExposure  @"action_shoppingCart_coupon_coudan_categoryFloor_Exposure"
#define ActionShoppingCartCouponCoudanCommodityFloorExposure @"action_shoppingCart_coupon_coudan_commodityFloor_Exposure"
#define ActionShoppingCartCouponCoudanCommodityFloorClick @"action_shoppingCart_coupon_coudan_commodityFloor_click"
#define ActionShoppingCartCouponCoudanCartButtonClick @"action_shoppingCart_coupon_coudan_cartButton_click"



#define kpage_CommodityDetails @"page_CommodityDetails" //打开商品详情页PV/UV







//极光埋点事件

#define btn_click                              @"btn_click"
#define btn_exposure                           @"btn_exposure"
// #define product_view_close                     @"product_view_close" // 极光埋点已移除

#define add_to_cart                            @"add_to_cart"
#define tpageview                              @"$pageview"
#define page_close                             @"page_close" // V11.9.17下线
//#define resource_view                          @"resource_view" // V11.9.17下线




//经过夏草，重楼和豆浆商讨以下埋点由后端埋，前端不做修改
//#define order_submit_detail                    @"order_submit_detail"
//#define order_submit                           @"order_submit"
#define resource_click                         @"resource_click"
#define search                                 @"search"

#define shop_view                           @"shop_view"
// 注册
#define register_event                         @"register"
// 登录
#define login_event                            @"login"
#define coupons_get                            @"coupons_get"
#define share_event                            @"share"

/**-----------------------直睿极光新埋点jgspid宏定义--------------------------**/

// APP大搜列表页
#define jgspid_youhuiquan_search            @"4101"
// APP全部商品列表页
#define jgspid_youhuiquan_all_product       @"4102"
// APP店铺商品列表页
#define jgspid_youhuiquan_store_product     @"4103"
// APP商祥页
#define jgspid_youhuiquan_prodcut_detail    @"4104"
// APP购物车右上方入口
#define jgspid_coudanye_cart_top_right      @"4105"
// APP购物车店铺栏右侧入口
#define jgspid_coudanye_cart_store_right    @"4106"
// APP我的页入口
#define jgspid_coudanye_mine                @"4107"
// APP专区搜索列表入口
#define jgspid_youhuiquan_old_search        @"4108"
// APP店铺首页列表入口
#define jgspid_youhuiquan_store_homepage    @"4109"
// APP购物车去凑单入口
#define jgspid_coudanye_cart_to_coudan      @"4120"

// APP订单列表页曝光-底部tab入口
#define jgspid_orderlist_tab_exposure       @"4201"
// APP订单列表页曝光-我的页入口
#define jgspid_orderlist_mine_exposure      @"4202"

/**-----------------------直睿极光新埋点事件ID--------------------------**/
//APP首页顶部热词曝光
#define kApp_page_top_hot_word_exposure      @"app_page_top_hot_word_exposure" // V11.9.17下线
//首页底纹过来搜首页-搜索热词点击
#define kApp_action_top_hot_word_click       @"app_action_top_hot_word_click"
//搜索中间页搜索框sug点击
#define kApp_action_top_search_sug_click        @"app_action_top_search_sug_click"
//搜索页不同搜索点击
#define kApp_action_top_search_click         @"app_action_top_search_click"
//搜索二级动态筛选点击
#define kApp_action_search_dynamic_filter_click          @"app_action_search_dynamic_filter_click"
//搜索二级动态筛选标签按钮曝光
#define kApp_page_search_dynamic_filter_exposure                           @"app_page_search_dynamic_filter_exposure"
 
//#define kApp_page_cms_exposure                                           @"app_page_cms_exposure"
// 原生+活动-CMS页面组件点击（APP、H5）（测试环境已配置）
//#define kApp_page_cms_component_exposure                                 @"app_page_cms_component_exposure"
//#define kApp_action_cms_component_click                                  @"app_action_cms_component_click"

//原生+活动-CMS页面子模块曝光
//#define kApp_page_cms_sub_module_exposure                                      @"app_page_cms_sub_module_exposure"
//原生+活动-CMS页面子模块点击
//#define kApp_action_cms_sub_module_click                                      @"app_action_cms_sub_module_click"

//列表生成
#define kPage_list_build               @"page_list_build"
//商品按钮点击
#define kAction_product_button_click    @"action_product_button_click"
//搜索筛选框点击埋点
#define kApp_action_search_filter_click           @"app_action_search_filter_click"
//搜索列表生成
#define kPage_list_build           @"page_list_build"
//列表商品曝光
#define kPage_list_product_exposure             @"page_list_product_exposure"
//列表商品点击
#define kAction_list_product_click              @"action_list_product_click"
//商品详情
#define kPage_product_detail_exposure           @"page_product_detail_exposure"
//商品按钮点击
#define kAction_product_button_click           @"action_product_button_click"
//商品加入购物车
#define kAdd_to_cart           @"add_to_cart"
// 优惠券曝光
#define kApp_voucher_exposure                   @"app_voucher_exposure"
// 优惠券点击
#define kApp_voucher_click                      @"app_voucher_click"
// 凑单页曝光
#define kApp_page_makeuporder_exposure          @"app_page_makeuporder_exposure"
// 待确认订单页优惠券曝光
#define kApp_tobeconfirmedorder_voucher_exposure            @"app_tobeconfirmedorder_voucher_exposure"
// 待确认订单页优惠券勾选
#define kApp_tobeconfirmedorder_voucher_tick                @"app_tobeconfirmedorder_voucher_tick"

/// APP订单tab点击 - 极光埋点已移除
// #define kApp_tab_order_click                    @"app_tab_order_click"
/// APP订单列表页曝光 - 极光埋点已移除
// #define kApp_orderListPage_view                 @"app_orderListPage_view"

#endif /* YBMAnalyticsEvents_h */
