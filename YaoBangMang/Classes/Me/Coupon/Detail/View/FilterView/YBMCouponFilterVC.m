//
//  YBMCouponFilterVC.m
//  YaoBangMang
//
//  Created by ZB on 2024/8/10.
//  Copyright © 2024 XiaoYaoYao.Ltd. All rights reserved.
//

#import "YBMCouponFilterVC.h"
#import "YBMFilterCollectionReusableView.h"
#import "CHTCollectionViewWaterfallLayout.h"
#import "YBMFilterItemCell.h"
#import "YBMSearchOptionSpecCell.h"
#import "YBMFilterPriceItemCell.h"

#define HEADER_IDENTIFIER @"WaterfallHeader"
#define ItmeWidth(count) (kScreenWidth - (count+1)*10)/count

@interface YBMCouponFilterVC ()<UICollectionViewDataSource, CHTCollectionViewDelegateWaterfallLayout>

@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, strong) NSMutableArray *selectIndexs;  //多选选中的行

@property (nonatomic, strong) UIButton *resetBtn;//重置
@property (nonatomic, strong) UIButton *sureBtn;//确定

@property (nonatomic, copy) NSArray <YBMFilterItemModel *>*titleArray;
@property (nonatomic, copy) NSArray <YBMFilterItemModel *>*serviceArray;//服务
@property (nonatomic, copy) NSArray <YBMFilterItemModel *>*drugTypeArray;//药品

@end

@implementation YBMCouponFilterVC

- (instancetype)init{
    self = [super init];
    if (self) {
        self.view.backgroundColor = UIColor.whiteColor;
        [self setupView]; //创建视图
    }
    return self;
}

//- (void)viewDidLoad {
//    [super viewDidLoad];
//    self.view.backgroundColor = UIColor.whiteColor;
//    [self setupView]; //创建视图
//}

- (void)setupView{
    //section title
    NSMutableArray *array0 = [[NSMutableArray alloc]init];
    NSArray *array00 = @[@"服务", @"药品类型"];
    for (int i = 0; i<array00.count; i++) {
        YBMFilterItemModel *m = [[YBMFilterItemModel alloc]init];
        m.name = array00[i];
        m.isCheck = NO;
        [array0 addObject:m];
    }
    self.titleArray = array0;
    
    //服务model
    NSMutableArray *array1 = [[NSMutableArray alloc]init];
    NSArray *array11 = @[@"仅看有货", @"顺丰快递", @"京东快递"];
//    NSArray *array111 = @[@"in_stock", @"YBM_ST_SERV_LOG_SF", @"YBM_ST_SERV_LOG_JD", @"isAvailableCoupons"];
    for (int i = 0; i<array11.count; i++) {
        YBMFilterItemModel *m = [[YBMFilterItemModel alloc]init];
        m.name = array11[i];
//        m.code = array111[i];
        m.isCheck = NO;
        [array1 addObject:m];
    }
    self.serviceArray = array1;
    
    //药品model
    NSMutableArray *array2 = [[NSMutableArray alloc]init];
    NSArray *array22 = @[@"甲类OTC", @"乙类OTC", @"处方药RX", @"其他"];
    NSArray *array222 = @[@"1", @"2", @"3", @"4"];
    for (int i = 0; i<array22.count; i++) {
        YBMFilterItemModel *m = [[YBMFilterItemModel alloc]init];
        m.name = array22[i];
        m.code = array222[i];
        m.isCheck = NO;
        [array2 addObject:m];
    }
    self.drugTypeArray = array2;
    
    
    UILabel *lab1 = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, 44)];
    lab1.textAlignment = NSTextAlignmentCenter;
    lab1.text = @"筛选";
    lab1.font = [UIFont systemFontOfSize:18];
    lab1.layer.borderWidth = 0.5;
    lab1.layer.borderColor = ColorFromHex(0xE4E4E4).CGColor;
    [self.view addSubview:lab1];
    
    UIButton *closeBtn = [[UIButton alloc] init];
    closeBtn.frame = CGRectMake(kScreenWidth-60, 0, 60, 43.5);
    [closeBtn setImage:[UIImage imageNamed:@"comment_close"] forState:UIControlStateNormal];
    [closeBtn setBackgroundColor:[UIColor whiteColor]];
    [closeBtn addTarget:self action:@selector(dissmissAction) forControlEvents:(UIControlEventTouchUpInside)];
    [self.view addSubview:closeBtn];
    
    
    [self.view addSubview:self.resetBtn];
    [self.view addSubview:self.sureBtn];
    [self.resetBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view.mas_bottom).offset(-kBottomSafeHeight);
        make.leading.equalTo(self.view.mas_leading);
        make.trailing.equalTo(self.sureBtn.mas_leading).offset(0);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.5);
        make.height.equalTo(@50);
    }];
    [self.sureBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view.mas_bottom).offset(-kBottomSafeHeight);
        make.trailing.equalTo(self.view.mas_trailing);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.5);
        make.height.equalTo(@50);
    }];
    
    
    [self.view addSubview:self.collectionView];
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(lab1.mas_bottom);
        make.leading.trailing.mas_equalTo(self.view);
        make.bottom.mas_equalTo(self.resetBtn.mas_top);
    }];
}

// 只看有货
- (void)setIsHasStock:(BOOL)isHasStock{
    _isHasStock = isHasStock;
    self.serviceArray.firstObject.isCheck = isHasStock;
    NSIndexPath *indexP = [NSIndexPath indexPathForItem:0 inSection:0];
    if (isHasStock) {
        [self.selectIndexs addObject:indexP];
    }
//    [self.collectionView reloadItemsAtIndexPaths:@[indexP]];
}

// 顺丰快递
- (void)setIsHasShunfeng:(BOOL)isHasShunfeng{
    _isHasShunfeng = isHasShunfeng;
    self.serviceArray[1].isCheck = isHasShunfeng;
    NSIndexPath *indexP = [NSIndexPath indexPathForItem:1 inSection:0];
    if (isHasShunfeng) {
        [self.selectIndexs addObject:indexP];
    }
//    [self.collectionView reloadItemsAtIndexPaths:@[indexP]];
}

// 京东快递
- (void)setIsHasJingdong:(BOOL)isHasJingdong{
    _isHasJingdong = isHasJingdong;
    self.serviceArray[2].isCheck = isHasJingdong;
    NSIndexPath *indexP = [NSIndexPath indexPathForItem:2 inSection:0];
    if (isHasJingdong) {
        [self.selectIndexs addObject:indexP];
    }
//    [self.collectionView reloadItemsAtIndexPaths:@[indexP]];
}

// 拼团包邮
- (void)setIsGroupBuyingOrWholesale:(BOOL)isGroupBuyingOrWholesale{
    _isGroupBuyingOrWholesale = isGroupBuyingOrWholesale;
    self.serviceArray[3].isCheck = isGroupBuyingOrWholesale;
    NSIndexPath *indexP = [NSIndexPath indexPathForItem:4 inSection:0];
    if (isGroupBuyingOrWholesale) {
        [self.selectIndexs addObject:indexP];
    }
}


// 已选中的 药品类型
- (void)setDrugClassificationsStr:(NSString *)drugClassificationsStr{
    _drugClassificationsStr = drugClassificationsStr;
    
    NSArray *selectArray = [drugClassificationsStr componentsSeparatedByString:@","];
    [self.drugTypeArray enumerateObjectsUsingBlock:^(YBMFilterItemModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([selectArray containsObject:obj.code]) {
            obj.isCheck = YES;
            NSIndexPath *indexPath = [NSIndexPath indexPathForItem:idx inSection:1];
            [self.selectIndexs addObject:indexPath];
        }else{
            obj.isCheck = NO;
        }
    }];
}

#pragma mark - Action
- (void)dissmissAction{
    [self dismissViewControllerAnimated:YES completion:nil];
}

//重置
- (void)resetAction:(UIButton *)sender{
    //section title
    [self.titleArray enumerateObjectsUsingBlock:^(YBMFilterItemModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        obj.code = @"";
    }];
    //服务
    [self.serviceArray enumerateObjectsUsingBlock:^(YBMFilterItemModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        obj.isCheck = NO;
    }];
    
    //药品类型
    [self.drugTypeArray enumerateObjectsUsingBlock:^(YBMFilterItemModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        obj.isCheck = NO;
    }];
    
    [self.selectIndexs removeAllObjects];
    [self.collectionView reloadData];
}

//确定
- (void)sureBtnAction:(UIButton *)sender{
    [self submitData];
    [self dissmissAction];
}

- (void)submitData{
    if (self.filterBlock) {
        NSMutableDictionary *dic = [[NSMutableDictionary alloc]init];
        
        //服务 //仅看有货 顺丰快递 京东快递 可用券、拼团包邮
        [self.serviceArray enumerateObjectsUsingBlock:^(YBMFilterItemModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            switch (idx) {
                case 0:{
                    dic[@"hasStock"] = @(obj.isCheck);
                }break;
                case 1:{
                    dic[@"isSupportSfExpress"] = obj.isCheck ? @1 : nil;
                }break;
                case 2:{
                    dic[@"isSupportJdExpress"] = obj.isCheck ? @1 : nil;
                }break;
                case 3:{
                    dic[@"isGroupBuyingOrWholesale"] = obj.isCheck ? @1 : nil;
                    if(obj.isCheck){
                        // 埋点 - 保留雪地埋点，移除极光埋点
                        NSMutableDictionary *dic = [NSMutableDictionary dictionary];
                        [YBMAnalyticsUtil track:@"action_Search_GroupPurchaseFreeShopping" properties:dic];

                        // 极光埋点已移除 - action_Search_GroupPurchaseFreeShopping
                        // NSMutableDictionary *mutD = [NSMutableDictionary dictionary];
                        // [AnalysysAgent track:@"action_Search_GroupPurchaseFreeShopping" properties:mutD];
                    }
                  
                }break;
                default:
                    break;
            }
        }];
        
        
        //药品类型
        NSMutableArray *arr5 = [NSMutableArray new];
        [self.drugTypeArray enumerateObjectsUsingBlock:^(YBMFilterItemModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (obj.isCheck){
                [arr5 addObject:obj.code];
            }
        }];
        if (arr5.count) {
            dic[@"drugClassificationsStr"] = [arr5 componentsJoinedByString:@","];
        }
        
        self.filterBlock(dic);
    }
}

#pragma mark - HWPanModalPresentable
- (HWBackgroundConfig *)backgroundConfig {
    HWBackgroundConfig *backgroundConfig = [HWBackgroundConfig configWithBehavior:HWBackgroundBehaviorDefault];
    backgroundConfig.backgroundAlpha = 0.1;
    return backgroundConfig;
}

- (BOOL)showDragIndicator {
    return NO;
}

- (UIScrollView *)panScrollable {
    return self.collectionView;
}

- (PanModalHeight)longFormHeight {
    return PanModalHeightMake(PanModalHeightTypeContent, CouponFilterH);
}

#pragma mark - CHTCollectionViewDelegateWaterfallLayout
// 单元格的大小
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath{
    return CGSizeMake(ItmeWidth(4), 30);
}

// 每个section 列数
- (NSInteger)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout columnCountForSection:(NSInteger)section{
    return 4;
}
#pragma mark - UICollectionView
// section
- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView{
    return self.titleArray.count;
}

// item个数
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    switch (section) {
        case 0:return self.serviceArray.count;//服务
        case 1:return self.drugTypeArray.count;//药品类型
        default:break;
    }
    return 1;
}

// headerView
- (UICollectionReusableView *)collectionView:(UICollectionView *)collectionView viewForSupplementaryElementOfKind:(NSString *)kind atIndexPath:(NSIndexPath *)indexPath{
    YBMFilterCollectionReusableView *header = [collectionView dequeueReusableSupplementaryViewOfKind:kind withReuseIdentifier:HEADER_IDENTIFIER forIndexPath:indexPath];
    YBMFilterItemModel *model = self.titleArray[indexPath.section];
    header.titleLab.text = model.name;
    header.tipsLab.text = model.code;
    @weakify(self)
    header.showOrCloseBlock = ^(UIButton * _Nonnull btn) {
        @strongify(self)
        //根据开关状态，修改model的开关状体，刷新当前section，更新行数
        model.isCheck = !model.isCheck;
        
        [CATransaction setDisableActions:YES];
        if (indexPath.section < [self.collectionView numberOfSections]) {
            [self.collectionView reloadSections:[NSIndexSet indexSetWithIndex:indexPath.section]];
        }
        [CATransaction commit];
    };
    header.isShowBtn.hidden = YES;
    header.isShowBtn.selected = model.isCheck;
    return header;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    YBMFilterItemCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"YBMFilterItemCell" forIndexPath:indexPath];
    cell.backgroundColor = [UIColor colorWithHexString:@"#F7F7F8"];
    cell.titleLab.lineBreakMode = NSLineBreakByTruncatingTail;
    cell.titleLab.textAlignment = NSTextAlignmentCenter;
    cell.titleLab.numberOfLines = 1;
    
    switch (indexPath.section) {
        case 0:{//服务
            cell.serviceModel = self.serviceArray[indexPath.row];
        } break;
        case 1:{//药品类型
            cell.drugTypeModel = self.drugTypeArray[indexPath.row];
        } break;
    }
    
    return cell;
}

//选择了某个cell
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    //未选中就加入
    if (![self.selectIndexs containsObject:indexPath]) {
        [self.selectIndexs addObject:indexPath];
    }else{//已选中就删除
        [self.selectIndexs removeObject:indexPath];
    }
    
    NSMutableArray *array = [[NSMutableArray alloc]init];
    switch (indexPath.section) {
        case 0:{//服务
            YBMFilterItemModel *model = self.serviceArray[indexPath.row];
            model.isCheck = !model.isCheck;
        } break;
        case 1:{//药品类型
            YBMFilterItemModel *model = self.drugTypeArray[indexPath.row];
            model.isCheck = !model.isCheck;
        } break;
    }

    //修改hader数据源
    NSString *string = [array componentsJoinedByString:@","];
    if (string.length) {
        string = [@"已选：" stringByAppendingString:string];
    }
    self.titleArray[indexPath.section].code = string;

    //闪烁：是因为CALayer有一个隐式动画，只要在调用reloadData刷新时，关闭隐式动画就可以避免了
    [CATransaction setDisableActions:YES];
    if (indexPath.section < [self.collectionView numberOfSections]) {
        [self.collectionView reloadSections:[NSIndexSet indexSetWithIndex:indexPath.section]];
    }
    [CATransaction commit];
}

#pragma mark - lazy
- (UICollectionView *)collectionView{
    if (!_collectionView) {
        CHTCollectionViewWaterfallLayout *layout = [[CHTCollectionViewWaterfallLayout alloc] init];
        layout.sectionInset = UIEdgeInsetsMake(10, 10, 10, 10);
        layout.headerHeight = 50;
        layout.minimumColumnSpacing = 10;//列间距
        layout.minimumInteritemSpacing = 10;//行间距
        
        _collectionView = [[UICollectionView alloc]initWithFrame:CGRectMake(0, 44, kScreenWidth, 500) collectionViewLayout:layout];
        
        _collectionView.delegate = self;// 设置代理
        _collectionView.dataSource = self; // 设置数据源代理
        _collectionView.showsHorizontalScrollIndicator = NO;
        _collectionView.backgroundColor = UIColor.whiteColor;
                
        [_collectionView registerClass:YBMFilterCollectionReusableView.class forSupplementaryViewOfKind:CHTCollectionElementKindSectionHeader withReuseIdentifier:HEADER_IDENTIFIER];

        [_collectionView registerClass:[YBMFilterItemCell class] forCellWithReuseIdentifier:@"YBMFilterItemCell"];
        [_collectionView registerClass:[YBMSearchOptionSpecCell class] forCellWithReuseIdentifier:@"YBMSearchOptionSpecCell"];//
        [_collectionView registerClass:[YBMFilterPriceItemCell class] forCellWithReuseIdentifier:@"YBMFilterPriceItemCell"];
    }
    return _collectionView;
}

- (UIButton *)resetBtn{
    if(!_resetBtn){
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        [btn setTitle:@"重置" forState:UIControlStateNormal];
        [btn setTitleColor:ColorText_292933 forState:UIControlStateNormal];
        btn.titleLabel.font = [UIFont fontWithName:FontTypePingFangRe size:16];
        btn.backgroundColor = [UIColor whiteColor];
        btn.layer.borderWidth = 0.5;
        btn.layer.borderColor = ColorFromHex(0xE4E4E4).CGColor;
        [btn addTarget:self action:@selector(resetAction:) forControlEvents:UIControlEventTouchUpInside];
        _resetBtn = btn;
    }
    return _resetBtn;
}

- (UIButton *)sureBtn{
    if (!_sureBtn) {
        UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
        [btn setTitle:@"确定" forState:UIControlStateNormal];
        [btn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        btn.titleLabel.font = [UIFont fontWithName:FontTypePingFangRe size:16];
        btn.backgroundColor = ColorGreen_00B377;
        [btn addTarget:self action:@selector(sureBtnAction:) forControlEvents:UIControlEventTouchUpInside];
        _sureBtn = btn;
    }
    return _sureBtn;
}

- (NSMutableArray *)selectIndexs{
    if (!_selectIndexs) {
        _selectIndexs = [[NSMutableArray alloc]init];
    }
    return _selectIndexs;
}

@end
