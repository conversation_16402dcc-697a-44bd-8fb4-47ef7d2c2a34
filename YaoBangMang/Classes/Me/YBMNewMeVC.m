//
//  YBMNewMeVC.m
//  YaoBangMang
//
//  Created by ZB on 2022/8/22.
//  Copyright © 2022 XiaoYaoYao.Ltd. All rights reserved.
//

#import "YBMNewMeVC.h"
#import "YBMMeHeaderView.h"
#import "YBMFindSimCollectionCell.h"
#import "YBMWaterFallLayout.h"
#import "YBMEventButton.h"
#import "UIButton+Badge.h"


#import "YBMAlertView.h"
#import "XYYMeServiceViewController.h"
#import "XYYCommonProblemViewController.h"
#import "XYYWebViewVC.h"
#import "YBMMeViewModel.h"
#import "YBMUsetInfoRootModel.h"
#import "YBMQualificationHomeBaseInfoViewController.h"
#import "XYYCenterOtherVC.h"
#import "YBMAlertView.h"
#import "UIImage+GradientColor.h"
#import "XYYNavigationController.h"
#import "YBMNotificationKey.h"
#import "YBMAuthorizeOrderRootModel.h"
#import "YBMQualificationListController.h"
#import "YBMCommonToolsRootModel.h"
#import "YBMWhiteBarMyFinanceController.h"
#import "XYYMyBalanceViewController.h"
#import "XYYMeItem.h"
#import "YBMRecommendRequest.h"
#import "YBMRecommendV2Request.h"
#import "YBMBaseTableView.h"
#import "YBMSearchAnalysisHeader.h"
#import "YBMBottomRecommendRootModel.h"
#import "YBMMeCommondModel.h"
#import "YBMCommonManager.h"
#import "DataTimeUtils.h"
#import "YBMMyWealthVC.h"//我的财富
#import "YBMVirtualGoldVC.h" // 购物金
#import "YBMMyWealthModel.h"

#import "ZBGuideMaskView.h"//新手引导遮照层
#import "YBMRefundSalesListVC.h"//退款/售后
#import "List+Exposure.h"
#import "YBMGoldPopRequest.h"

@interface YBMNewMeVC ()<YBMHiddenNavigationBarProtocol, UICollectionViewDelegate, UICollectionViewDataSource,UICollectionViewDelegateFlowLayout, YBMMeHeaderViewDelegate, YBMMeViewModelDelegate, YBMFindSimCollectionCellDelegate, YBMExposureDelegate>

@property (strong, nonatomic) IBOutlet UICollectionView *collectionView;
@property (strong, nonatomic) YBMWaterFallLayout *flowLayout;
@property (strong, nonatomic) YBMMeHeaderView *headerView;

@property (strong, nonatomic) UIView *navView;
@property (nonatomic, strong) YBMEventButton *messageButton;
@property (nonatomic, strong) YBMEventButton *setupButton;

///// 为你推荐
@property (nonatomic, copy) NSArray<YBMProductItemModel *> *recommendDataArray;

@property (nonatomic, strong) YBMMeViewModel *viewModel;

@property (nonatomic, strong) YBMUsetInfoDataModel *model;

/// 常够清单的数据，这个字段在YBMFindSimCollectionCell有用到，修改时一定要去检查
@property (nonatomic, strong) YBMRecommendDataModel *recommendModel;

@end

@implementation YBMNewMeVC

- (void)viewDidLoad{
    [super viewDidLoad];
    self.navigationBar.hidden = YES;

    //tabbar 切换时，所有组件QT曝光
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(tabBarDidSelect:) name:@"tabBarDidSelect" object:nil];
    
    if (@available(iOS 11.0, *)) {
        self.collectionView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    
    [self.view addSubview:self.collectionView];
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.view);
    }];
    
    [self configNavViewUI];
    [self setupRecommendDataisFirst:YES];
    self.entrance = @"我的";
    
    [self trackMePageExposure];//页面曝光
    [self trackTabbarComponentExposure];//tabbar组件曝光
}

- (void)configNavViewUI {
    [self.view addSubview:self.navView];
    [self.navView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.leading.trailing.mas_equalTo(self.view);
        CGFloat height = kNavBarHeight;
        make.height.mas_equalTo(height);
    }];

    [self.view addSubview:self.messageButton];
    [self.view addSubview:self.setupButton];
    [self.messageButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(30, 30));
        CGFloat top = 27 + kTopBarDifHeight;
        make.top.mas_equalTo(@(top));
        make.trailing.mas_equalTo(-10);
    }];
    [self.setupButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(30, 30));
        make.centerY.mas_equalTo(self.messageButton.mas_centerY);
        make.trailing.mas_equalTo(self.messageButton.mas_leading).offset(-10);
    }];
}

#pragma mark -
- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    /// 雪地埋点 行为-我的点击
    [YBMAnalyticsUtil track:kaction_Me];

    /// 每次更新右上角消息未读
    [self setupMessageUnread];
    
    /// 常用工具+banner+帮助中心+客服电话
    @weakify(self);
    [self.viewModel requestUserInfoWithParams:@{} myToolsWithParams:@{@"codemap":@"USE_TOOLS"} bannerPosition:@{@"sceneType" : @"3"} onlineServiceCount:@{@"messageType":@"2"} orderNum:@{} complete:^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
        @strongify(self);
        if (success && response) {
            NSArray *tmp = response;
            self.headerView.dataSource = tmp;
        }
    }];
    [self getRechargeDiscount];
}
// app端获取红包阶梯金额
- (void)getRechargeDiscount{
    @weakify(self)
    YBMGoldPopRequest *rq = [[YBMGoldPopRequest alloc]init];
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"merchantId"] = [[XYYUserData ShareUserData] merchantId];
    rq.addParams(params).request(^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
        @strongify(self)
        if (success) {
            YBMGoldPopRespond *respond = (YBMGoldPopRespond *)response;
            [self.headerView setGoldModel:respond];
        }
    });
}
//热销精选请求
- (void)setupRecommendDataisFirst:(BOOL)isFirst {
    NSMutableDictionary *dic = [[NSMutableDictionary alloc]init];
    dic[@"sptype"] = @"3";//1-搜索列表页，2-分类列表页，3-推荐页面
    dic[@"pageType"] = @"7";//7-我的个人中心页内推荐
    if (!isFirst) {
        [dic addEntriesFromDictionary:self.recommendModel.requestParam];
        if ([self.recommendModel.requestParam.allKeys containsObject:@"sptype"]) {
            // 埋点检测 sptype
            [YBMAnalyticsCheckUtil checkSpTypeField:self.recommendModel.requestParam[@"sptype"] andParams:self.recommendModel.requestParam andIsFromTrack:NO];
        }
    }
    YBMRecommendV2Request *rq = [[YBMRecommendV2Request alloc]init];
    rq.addParams(dic).request(^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
        if (success && response) {
            YBMRecommendRootDataModel *rModel = response;
            self.recommendModel = rModel.data;
            
            // 埋点参数
            self.sptype = self.recommendModel.sptype;
            self.spid = self.recommendModel.spid;
            self.sid = self.recommendModel.sid;
            if (isFirst) {
                [self snowActionOpenList];
                self.recommendDataArray = @[];
            }
            self.headerView.sptype = self.sptype;
            self.headerView.spid = self.spid;
            self.headerView.sid = self.sid;
            
            NSArray *rows = self.recommendModel.rows;
            NSMutableArray *array = [NSMutableArray array];
            if (self.recommendDataArray.count > 0) {
                [array addObjectsFromArray:self.recommendDataArray];
            }
            [array addObjectsFromArray:rows];
            self.recommendDataArray = array;
            
            if (rows.count == 0 && isFirst) {
                self.collectionView.mj_footer.hidden = YES;
            } else {
                self.collectionView.mj_footer.hidden = NO;
            }
            if (self.recommendModel.isEnd) {
                [self.collectionView.mj_footer endRefreshingWithNoMoreData];
            } else {
                [self.collectionView.mj_footer endRefreshing];
            }
            
            // 请求折后价
            NSMutableArray *arr = [NSMutableArray array];
            for (YBMProductItemModel *m in self.recommendModel.rows) {
                if (m.ID.stringValue.length) {
                    [arr addObject:m.ID];
                }
            }
            NSString *ids = [arr componentsJoinedByString:@","];
            [self requestDiscountData:ids];
        }else {
            [self.collectionView.mj_footer endRefreshing];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.collectionView reloadData];
        });
        if (![[NSUserDefaults standardUserDefaults] boolForKey:@"hiddenGuideMask"]){
            [self requestMyWealths];
        }
//        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//            [self.collectionView reloadData];
////            [self configSetupUISize];
//        });
    });
}

- (void)requestMyWealths{
    [self.view showSpinner];
    @weakify(self)
    [XYYHttpApi postUrl:API_Interface(@"/app/queryMyWealths") Param:nil SuccessBlock:^(NSDictionary *resultData) {
        @strongify(self)
        [self.view hideSpinner];
        NSArray <YBMMyWealthModel *> *array = [YBMMyWealthModel mj_objectArrayWithKeyValuesArray:resultData];
        __block BOOL isHasPingan = NO;
        [array enumerateObjectsUsingBlock:^(YBMMyWealthModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (obj.itemType.intValue == 4){
                isHasPingan = YES;
                *stop = YES;
            }
        }];
        
        [self configSetupUISize:isHasPingan];
    } FailureBlock:^(NSDictionary *resultData) {
        [self.view hideSpinner];
        MBShowFailureMessage(resultData[@"msg"]);
    }];
}

- (void)configSetupUISize:(BOOL)isHasPingan{
    __block CGRect rect1;
    __block CGRect rect2;
    [self.headerView.subviews enumerateObjectsUsingBlock:^(__kindof UIView * _Nonnull view, NSUInteger idx, BOOL * _Nonnull stop) {
        if (view.tag == 4){
            [view.subviews enumerateObjectsUsingBlock:^(__kindof UIView * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                if (obj.tag == 0){
                    CGRect frame = obj.frame;
                    frame.size.width = CGRectGetWidth(obj.frame) + 10;
                    frame.size.height = CGRectGetHeight(obj.frame) + 10;
                    
                    frame.origin.x = obj.frame.origin.x + 5;
                    frame.origin.y = view.frame.origin.y - 2;
                    rect2 = frame;
                    
//                    UIView *v = [[UIView alloc]initWithFrame:rect2];
//                    v.backgroundColor = [UIColor colorWithRed:0.5 green:0.5 blue:0.5 alpha:0.5];
//                    [self.view addSubview:v];
                }
            }];
        }else if (view.tag == 5){//5是整个UICollectionView
            CGRect frame = view.frame;
            frame.size.width = CGRectGetWidth(view.frame)/5 + 8;
            frame.size.height = 66 + 8;
            frame.origin.x = CGRectGetWidth(frame) * 2 - 8;
            frame.origin.y = CGRectGetMinY(view.frame) - 4;
            rect1 = frame;
            
//            UIView *v = [[UIView alloc]initWithFrame:rect1];
//            v.backgroundColor = [UIColor colorWithRed:0.5 green:0.5 blue:0.5 alpha:0.5];
//            [self.view addSubview:v];
        }
    }];
    
//    if (![[NSUserDefaults standardUserDefaults] boolForKey:@"hiddenGuideMask"] && [self.ybm_viewController isKindOfClass:YBMNewMeVC.class] ){
//        [self.collectionView setContentOffset:CGPointZero];
//        [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"hiddenGuideMask"];
//        if (isHasPingan){
//            [self setupGuideView:rect1 :rect2];
//        }else{
//            [self setupGuideView:rect2];
//        }
//    }
}

- (void)setupGuideView:(CGRect)rect1 :(CGRect)rect2{
    NSArray *imageArr = @[@"me_Mantle1_logo",@"me_Mantle1_step",
                          @"me_Mantle2_logo",@"me_Mantle2_step",
                          @"me_Mantle2_logo",@"me_Mantle3_step"];

    CGFloat x = CGRectGetMinX(rect2) + (CGRectGetWidth(rect2)-111.5)/2;
    NSArray *imgFrameArr = @[
        [NSValue valueWithCGRect:CGRectMake(CGRectGetMinX(rect1), CGRectGetMinY(rect1), 82, 82)],
        [NSValue valueWithCGRect:CGRectMake(CGRectGetMidX(rect1)-327/4, CGRectGetMaxY(rect1)+5, 327/2, 317/2)],
        
        [NSValue valueWithCGRect:CGRectMake(x, CGRectGetMinY(rect2), 111.5, 81)],
        [NSValue valueWithCGRect:CGRectMake(CGRectGetMaxX(rect2) + 5, CGRectGetMinY(rect2), 453/2, 269/2)],
        
        [NSValue valueWithCGRect:CGRectMake(x, CGRectGetMinY(rect2), 111.5, 81)],
        [NSValue valueWithCGRect:CGRectMake(CGRectGetMidX(rect2)-10, CGRectGetMaxY(rect2)+5, 500/2, 269/2)],
    ];
//    NSArray *transparentRectArr = @[[NSValue valueWithCGRect:rect1],[NSValue valueWithCGRect:rect2]];
    // @[@3]
    // @[@1,@1,@1]
    NSArray *orderArr = @[@2,@2,@2];
    ZBGuideMaskView *maskView = [ZBGuideMaskView new];
    [maskView addImages:imageArr imageFrame:imgFrameArr TransparentRect:nil orderArr:orderArr];
    [maskView showMaskViewInView:nil];
}

- (void)setupGuideView:(CGRect)rect2{
    NSArray *imageArr = @[@"me_Mantle2_logo",@"me_Mantle3_step"];

    CGFloat x = CGRectGetMinX(rect2) + (CGRectGetWidth(rect2)-111.5)/2;
    NSArray *imgFrameArr = @[
        [NSValue valueWithCGRect:CGRectMake(x, CGRectGetMinY(rect2), 111.5, 81)],
        [NSValue valueWithCGRect:CGRectMake(CGRectGetMidX(rect2)-10, CGRectGetMaxY(rect2)+5, 500/2, 269/2)],
    ];
    NSArray *orderArr = @[@2];
    ZBGuideMaskView *maskView = [ZBGuideMaskView new];
    [maskView addImages:imageArr imageFrame:imgFrameArr TransparentRect:nil orderArr:orderArr];
    [maskView showMaskViewInView:nil];
}

/// 请求折后价数据
- (void)requestDiscountData:(NSString *)skuIds {
    @weakify(self)
    [YBMCommonManager.shared requestDiscountDataWithSkuIds:skuIds complete:^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
        @strongify(self)
        YBMProductDiscountRootModel *rootModel = response;
        if (success && rootModel.data) {
            [rootModel.data enumerateObjectsUsingBlock:^(YBMProductDiscountModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                [self.recommendDataArray enumerateObjectsUsingBlock:^(YBMProductItemModel * _Nonnull m, NSUInteger idx, BOOL * _Nonnull stop) {
                    if (m.ID.integerValue == obj.skuId.integerValue) {
                        m.discountPrice = obj.price;
                        m.inHandPrice = obj.inHandPrice;
                        // 去掉这一行，因为self.recommendDataArray可能会存在多条同样的数据
//                        *stop = YES;
                    }
                }];
            }];
        }
        [self.collectionView setIgnoreReload:@"app/marketing/discount/satisfactoryInHandPrice"];
        [self.collectionView reloadData];
    }];
}

// 雪地埋点 行为-列表打开事件page_CommodityList
- (void)snowActionOpenList{
    NSMutableDictionary *snowDic = [NSMutableDictionary dictionary];
    snowDic[@"sptype"] = self.sptype;
    snowDic[@"spid"] = self.spid;
    snowDic[@"sid"] = self.sid;
    [YBMAnalyticsUtil track:page_CommodityList properties:snowDic];
    
    // 埋点检测 sptype
    [YBMAnalyticsCheckUtil checkSpTypeField:self.sptype andParams:snowDic andIsFromTrack:YES];
}

- (void)setupMessageUnread {
    @weakify(self)
    [YBMCommonManager.shared requestMessageUnReadCountComplete:^(BOOL success, id  _Nullable response, NSError * _Nullable error) {
        @strongify(self)
        if (success && response) {
            YBMMessageUnReadCountModel *model = response;
            [self setMessageUnread:model.unReadCount];
        }
    }];
}

/// 消息未读数
- (void)setMessageUnread:(NSNumber *)messageUnread {
    if (messageUnread.integerValue > 9) {
        [self.messageButton setBadgeValue:@"9+"];
    } else {
        [self.messageButton setBadgeValue:messageUnread.stringValue];
    }
    self.messageButton.badgePadding = 5;
    self.messageButton.badgeOriginY = -3;
    self.messageButton.badgeOriginX = 33;
    self.messageButton.badgeFont = [UIFont systemFontOfSize:11];
}

#pragma mark - Action
//底部tabbar点击后
- (void)tabBarDidSelect:(NSNotification *)notification {
    NSDictionary *userInfo = notification.object;
    NSString *title = userInfo[@"title"];
    NSNumber *fromIndex = userInfo[@"fromIndex"];
    NSNumber *toIndex = userInfo[@"toIndex"];
    
    BOOL isFromSelf = (fromIndex.integerValue == YBMTabbarTypeMe);
    BOOL isToSelf = (toIndex.integerValue == YBMTabbarTypeMe);

    // 从当前去其他 tab，tab点击埋点
    if (isFromSelf && !isToSelf) {
        //tab点击埋点
        [self trackTabbarComponentClickWithIndex:toIndex.intValue title:title];
    }
    
    //位置只能放在这里，上下不能换位置
    [YBMQTTrack reloadSpmEForPage:self];

    // 其他tab 回当前页，曝光逻辑
    if (isToSelf) {
        dispatch_async(dispatch_get_main_queue(), ^{
            //页面曝光
            [self trackMePageExposure];
            //tabbar组件曝光
            [self trackTabbarComponentExposure];
            //商品重新曝光
            for (YBMProductItemModel *model in self.recommendDataArray) {
                model.isLoad = NO;
            }
        });
    }
}

/// 消息中心
- (void)messageActionButton {
    // 雪地埋点 行为-我的-消息中心
    [YBMAnalyticsUtil track:kaction_Me_Message];
    
    NSString *head = API_Interface(@"/static/xyyvue/dist");
    NSString *str = [NSString stringWithFormat:@"%@/#/messagecenter",head];
    NSString *strRouter = [NSString stringWithFormat:@"ybmpage://commonh5activity?umkey=1111hd1&url=%@?merchantId=%@&ybm_title=消息中心&head_menu=0",str,[XYYUserData ShareUserData].merchantId];
    UIViewController *messageVC = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:strRouter];
    [self.navigationController pushViewController:messageVC animated:YES];
}

/// 设置
- (void)setupActionButton {
    //点击我的-设置icon
    [YBMAnalyticsUtil track:@"action_Me_Install"];

    XYYCenterOtherVC *vc = [[XYYCenterOtherVC alloc] init];
    [self.navigationController pushViewController:vc animated:YES];
}

/// 为你推荐加载更多
- (void)ybm_meViewLoadMoreRecommendData {
    [self setupRecommendDataisFirst:NO];
}

#pragma mark - YBMMeHeaderViewDelegate
/// 切换账号
- (void)ybm_meViewDidCutAccount {
    YBMQualificationHomeBaseInfoViewController *vc = [[YBMQualificationHomeBaseInfoViewController alloc]init];
    [self.navigationController pushViewController:vc animated:YES];
    
    //点击我的-更多账号信息
    [YBMAnalyticsUtil track:@"action_Me_MoreAccount"];
}

/// 我的资产：购物金、红包、优惠券、平安贷
/// 10.9.0 改为
/// 我的财富、资质管理、优惠券、红包（ 购物金、红包、优惠券、平安贷）
- (void)ybm_meViewMyAssetsSelect:(NSInteger)index{
    [self trackWealthClickIndex:index];
    
    switch (index) {
        case 1:{ // 购物金
            //资质管理
//            [self auroraAnalysisWithButtonName:@"资质管理" module:@"上服务区"];
//            UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:@"ybmpage://aptitude"];
//            [AppConfigue pushVC:vc];
            
            // 购物金
            YBMVirtualGoldVC *vc = [[YBMVirtualGoldVC alloc] initWithNibName:@"YBMVirtualGoldVC" bundle:nil];
            [self.navigationController pushViewController:vc animated:YES];
        }
            break;
        case 2:{
            /// 我的优惠券
            UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:@"ybmpage://couponmeber"];
            [self.navigationController pushViewController:vc animated:YES];
            
            //点击我的-优惠券入口
            [YBMAnalyticsUtil track:kaction_Me_Coupons];
        }
            break;
        case 3:{//我的红包
            XYYMyBalanceViewController *vc = [[XYYMyBalanceViewController alloc]init];
            [self.navigationController pushViewController:vc animated:YES];
            
            //点击我的-红包入口
            [YBMAnalyticsUtil track:@"action_Me_RedPacket"];
        }
            break;
        case 4:{//我的财富
            YBMMyWealthVC *vc = [[YBMMyWealthVC alloc]init];
            [self.navigationController pushViewController:vc animated:YES];
            // 埋点
            [YBMAnalyticsUtil track:@"my_wealth_click"];
        }
            break;
        default:
            break;
    }
}

/// 全部订单
- (void)ybm_meViewDidAllOrder {
    [self trackOrderIconClickIndex:0];
    
    /// 雪地埋点 行为-我的-全部订单
    [YBMAnalyticsUtil track:kaction_Me_AllOrders];
//    UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:@"ybmpage://myorderlist?goToStatus=4"];
//    [self.navigationController pushViewController:vc animated:YES];
    
    UITabBarController *tabBarVC = self.ybm_viewController.tabBarController;
    tabBarVC.selectedIndex = YBMTabbarTypeOrder;
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//        UIViewController *vc = self.ybm_viewController;
        UIViewController *vc = [(UINavigationController *)tabBarVC.selectedViewController topViewController];
        if ([vc.class isEqual:NSClassFromString(@"XYYAllShopViewController")]) {
            [vc setValue:@"0" forKeyPath:@"statusType"];
        }
    });
    
    // 极光埋点-“我的订单页”曝光
    [self auroraAnalysisOrderListPageExposure];
}

/// 订单点击
- (void)ybm_meViewDidSelectIndex:(NSInteger)index {
    [self trackOrderIconClickIndex:index+1];
    
    NSString *indexString;
    switch (index) {
        case 0:
            indexString = @"10";//待支付
            // 雪地埋点 行为-我的-待支付
            [YBMAnalyticsUtil track:kaction_Me_NoPaid];
            break;
        case 1:
            indexString = @"1";//配送中
            // 雪地埋点 行为-我的-待配送
            [YBMAnalyticsUtil track:kaction_Me_NoDistribution];
            break;
        case 2:
            indexString = @"2";//配送中
            // 雪地埋点 行为-我的-配送中
            [YBMAnalyticsUtil track:kaction_Me_InDistribution];
            break;
        case 3:{
            indexString = @"90";//售后退款
            // 雪地埋点 行为-我的-售后退款
            [YBMAnalyticsUtil track:kaction_Me_RefundAfterSale];
            YBMRefundSalesListVC *vc = [[YBMRefundSalesListVC alloc]init];
            [self.navigationController pushViewController:vc animated:YES];
            return;
        }
            break;
        default:
            indexString = @"5";//待评价
            // 雪地埋点 行为-我的-待评价
            [YBMAnalyticsUtil track:kaction_Me_Evaluation];
            break;
    }
    UITabBarController *tabBarVC = self.ybm_viewController.tabBarController;
    tabBarVC.selectedIndex = YBMTabbarTypeOrder;
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        UIViewController *vc = [(UINavigationController *)tabBarVC.selectedViewController topViewController];
//        UIViewController *vc = self.ybm_viewController;
        if ([vc.class isEqual:NSClassFromString(@"XYYAllShopViewController")]) {
            [vc setValue:indexString forKeyPath:@"statusType"];
        }
    });
    
    // 极光埋点-“我的订单页”曝光
    [self auroraAnalysisOrderListPageExposure];
    
//    NSString *url = [NSString stringWithFormat:@"ybmpage://myorderlist?order_state=%@&goToStatus=4",indexString];
//    UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:url];
//    [self.navigationController pushViewController:vc animated:YES];
}

/// 常用工具
- (void)ybm_meViewDidSelectOftenTools:(YBMCommonToolsModel *)model index:(NSInteger)index{
    
    [self trackOftenToolsClick:model index:index];
    UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:model.url?:@""];
    [AppConfigue pushVC:vc];
}

/// 客服电话
- (void)ybm_meViewDidSelectServicePhone {
    /// 雪地埋点 行为-我的-客服电话
    [YBMAnalyticsUtil track:kaction_Me_CustomerServiceTelephone];
    YBMAlertView *alert = [[YBMAlertView alloc] initWithTitle:@"4000505111" message:@"" cancelButtonTitle:@"取消" otherButtonTitle:@"呼叫"];
    alert.completionBlock = ^(NSInteger index) {
        if (index == 1) {
            NSMutableString *str = [[NSMutableString alloc] initWithFormat:@"tel:%@",@"4000505111"];
            if (SystemVersion >= 10.0) {
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:str] options:@{} completionHandler:nil];
            } else {
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:str]];
            }
        }
    };
    [alert show];
}

/// 购物金-去充值
- (void)ybm_meHeaderView:(YBMMeHeaderView *)headerView clickTopUpNow:(UIButton *)sender {
    [self trackWealthClickIndex:5];
    // 购物金
    YBMVirtualGoldVC *vc = [[YBMVirtualGoldVC alloc] initWithNibName:@"YBMVirtualGoldVC" bundle:nil];
    vc.showRechargeView = YES;
    [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark - UICollectionView
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    CGFloat offsetY = scrollView.contentOffset.y;
    CGFloat alpha = 1 - (kNavBarHeight - offsetY) / kNavBarHeight;
    self.navView.alpha = alpha;
}

//item个数
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return self.recommendDataArray.count;
}

// cell
- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    YBMFindSimCollectionCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"YBMFindSimCollectionCell" forIndexPath:indexPath];
    cell.index = [NSString stringWithFormat:@"%ld",indexPath.row + 1];
    cell.rank = indexPath.row;
    cell.delegate = self;
    if (self.recommendDataArray.count > indexPath.row) {
        YBMProductItemModel *model = self.recommendDataArray[indexPath.row];
        //    YBMLog(@"row:%ld, item:%ld",indexPath.row, indexPath.item);
        model.index = indexPath.row + 1;
        cell.model = model;
        return cell;
    }
    return UICollectionViewCell.new;
}

- (void)collectionView:(UICollectionView *)collectionView willDisplayCell:(UICollectionViewCell *)cell forItemAtIndexPath:(NSIndexPath *)indexPath{
    YBMProductItemModel *model = self.recommendDataArray[indexPath.row];
    if (!model.isLoad) {
        [self trackProducItemExposure:model];
        model.isLoad = YES;
    }
    
    
    //埋点：商品曝光
    NSMutableDictionary *snowDic = [NSMutableDictionary dictionary];
    snowDic[@"sptype"] = model.sptype;
    snowDic[@"spid"] = model.spid;
    snowDic[@"sid"] = model.sid;
    snowDic[@"source"] = model.sourceType;
    snowDic[@"index"] = @(indexPath.row + 1);
    snowDic[@"commodityId"] = model.ID;
    snowDic[@"commodityName"] = model.showName;
    [snowDic setValue:model.shopCode forKey:@"shop_code"];
    [YBMAnalyticsUtil track:page_ListPage_Exposure properties:snowDic];
    // 埋点检测 sptype
    [YBMAnalyticsCheckUtil checkSpTypeField:model.sptype andParams:snowDic andIsFromTrack:YES];
}

//选择了某个cell
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    YBMProductItemModel *model = self.recommendDataArray[indexPath.row];
    [self trackProducItemClick:model];

    NSString *strUrl = [NSString stringWithFormat:@"ybmpage://productdetail?product_id=%@&sptype=%@&spid=%@&sid=%@&source=%@&index=%ld&jgEntrance=我的(常购精选)",model.ID, model.sptype, model.spid, model.sid, model.sourceType, indexPath.row + 1];

    //点击进入商品详情
    UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:strUrl];
    if (vc) {
        [AppConfigue pushVC:vc];
    }
    [YBMAnalyticsPages trackClickModel:model index:indexPath.row controller:self];
}



#pragma mark - YBMMeHeaderViewModelDelegate

/// 请求个人信息
- (void)requestUserInfoSuccess:(id)responseObject {
    YBMUsetInfoDataModel *model = responseObject;
    _model = model;
    self.headerView.dataModel = model;
    /// 雪地埋点 用户-我的-更新用户信息
    /// 记录药店类型
    [XYYUserData ShareUserData].businessType = model.baseInfo.businessType;
    /// 记录药店类型名称
    [XYYUserData ShareUserData].businessTypeName = model.baseInfo.businessTypeName;
    /// 记录药店地址
    [XYYUserData ShareUserData].address = model.baseInfo.address;
    /// 记录省市区id 和名称
    [XYYUserData ShareUserData].provinceId = model.baseInfo.provinceCode;
    [XYYUserData ShareUserData].provinceName = model.baseInfo.province;
    [XYYUserData ShareUserData].cityId = model.baseInfo.cityCode;
    [XYYUserData ShareUserData].cityName = model.baseInfo.city;
    [XYYUserData ShareUserData].districtId = model.baseInfo.areaCode;
    [XYYUserData ShareUserData].districtName = model.baseInfo.district;
    /// 记录注册时间
    [XYYUserData ShareUserData].registeredDate = model.baseInfo.createTime;
    /// 记录当前渠道
    [XYYUserData ShareUserData].channelNames = model.baseInfo.channelNames;
    /// 是否是KA
    [XYYUserData ShareUserData].isKa = model.isKa;
    /// 用户资质状态
    [XYYUserData ShareUserData].licenseStatus = model.baseInfo.licenseStatus;
    /// 客户资质是否临期
    [XYYUserData ShareUserData].validity = model.validity;
    /// 雪地埋点 用户识别
    NSString *merchantId =  [NSString stringWithFormat:@"%@",[XYYUserData ShareUserData].merchantId];
    NSString *userId = [YBMAnalyticsUtil getUserId];
    /// 如果是同一用户就不切换
    if (![userId isEqualToString: merchantId] ) {
        [YBMAnalyticsUtil identify];
        [[NSNotificationCenter defaultCenter] postNotificationName:kMerchantBaseInfoSuccessNotifi object:nil];
    }
    /// 给极光打tags
    if (model.tagList != nil) {
        NSArray * dataArr = model.tagList;
        NSMutableArray * dealDataArr = [NSMutableArray array];
        for (id value in dataArr) {
            if ([value isKindOfClass:[NSNumber class]] && value != nil) {
                NSNumber * valueNumber = value;
                [dealDataArr addObject:valueNumber.stringValue];
            }
        }
        
        NSMutableDictionary * param = [NSMutableDictionary dictionary];
        if ([[NSUserDefaults standardUserDefaults] objectForKey:@"deviceTokenString"] != nil) {
            [param setValue:[[NSUserDefaults standardUserDefaults] objectForKey:@"deviceTokenString"] forKey:@"registrationId"];
        }
        [self.viewModel refreshBindJpushWithParams:param complete:^(BOOL success) {
            if (success) {
                NSSet * set = [[NSSet alloc] initWithArray:dealDataArr];
                [JPUSHService setTags:set completion:^(NSInteger iResCode, NSSet *iTags, NSInteger seq) {
                } seq:0];
            }
        }];
    }
}

/// 请求订单数量
- (void)requestOrderNumberSuccess:(id)responseObject {
    YBMOrderNumDataModel *model = responseObject;
    self.headerView.model = model;
}

/// 专属销售
- (void)requestSepcSellSuccess:(id)responseObject {
    if (!kStringIsEmpty(responseObject[@"phone"])) {
        YBMAlertView *alert = [[YBMAlertView alloc] initWithTitle:responseObject[@"phone"] message:@"" cancelButtonTitle:@"取消" otherButtonTitle:@"呼叫"];
        alert.completionBlock = ^(NSInteger index) {
            if (index == 1) {
                NSMutableString *str = [[NSMutableString alloc] initWithFormat:@"tel:%@",responseObject[@"phone"]];
                if (SystemVersion >= 10.0) {
                    [[UIApplication sharedApplication] openURL:[NSURL URLWithString:str] options:@{} completionHandler:nil];
                } else {
                    [[UIApplication sharedApplication] openURL:[NSURL URLWithString:str]];
                }
            }
        };
        [alert show];
    }
}

/// 在线客服
- (void)requestServiceSuccess:(id)responseObject {
    XYYWebViewVC *vc = [[XYYWebViewVC alloc] init];
    NSString *url = [NSString stringWithFormat:@"%@&userid=%@&sc=%@&portalType=%@",responseObject[@"IM_PACK_URL"],[XYYUserData ShareUserData].merchantId,@"1003",@"1"];
    vc.urlSting = url;
    [vc setRightButtonEnabled:YES];
    [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark - old

/// 帮助中心
- (void)ybm_meView:(YBMMeHeaderView *)view didSelectHelpCenter:(NSInteger)index {
    switch (index) {
        case 0:{
            /// 雪地埋点 行为-我的-售后规则
            [YBMAnalyticsUtil track:kaction_Me_AfterSalesRules];
            NSString *urlString = [[AppConfigue shared] appURL:@"/static/xyyvue/temp/afterTerm.html"];
            NSString *routerString = [NSString stringWithFormat:@"ybmpage://commonh5activity?url=%@",urlString];
            UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:routerString];
            [self.navigationController pushViewController:vc animated:YES];
            break;
        }
        case 1:{
            /// 雪地埋点 行为-我的-控价规则
            [YBMAnalyticsUtil track:kaction_Me_ControlPriceRules];
            NSString *urlString = [[AppConfigue shared] appURL:@"/static/xyyvue/dist/#/koujiades?ybm_title=控价规则"];
            NSString *routerString = [NSString stringWithFormat:@"ybmpage://commonh5activity?url=%@",urlString];
            UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:routerString];
            [self.navigationController pushViewController:vc animated:YES];
            break;
        }
        case 2:{
            /// 雪地埋点 行为-我的-意见反馈
            [YBMAnalyticsUtil track:kaction_Me_Feedback];
            NSString *head = API_Interface(@"/static/xyyvue/dist");
            NSString *str = [NSString stringWithFormat:@"%@/#/feedback",head];
            NSString *strRouter = [NSString stringWithFormat:@"ybmpage://commonh5activity?url=%@?ybm_title=意见反馈&head_menu=0",str];
            UIViewController *messageVC = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:strRouter];
            [self.navigationController pushViewController:messageVC animated:YES];
            break;
        }
        case 3:{
            /// 雪地埋点 行为-我的-在线客服
            [YBMAnalyticsUtil track:kaction_Me_CustomerService];
            [self.viewModel refreshNowServiceWithParams:@{} complete:^(BOOL success) {
            }];
            break;
        }
        case 4:{
            /// 雪地埋点 行为-我的-常见问题
            [YBMAnalyticsUtil track:kaction_Me_CommonProblems];
            [self.navigationController pushViewController:[[XYYCommonProblemViewController alloc] init] animated:YES];
            break;
        }
        case 5:{
            /// 雪地埋点 行为-我的-专属销售
            [YBMAnalyticsUtil track:kaction_Me_ExclusiveSales];
            [self.viewModel refreshSepcSellWithParams:@{} complete:^(BOOL success) {
            }];
            break;
        }
        case 6:{
            /// 小药药资质
            [self.navigationController pushViewController:[[YBMQualificationListController alloc] init] animated:YES];
            break;
        }
        default:
            break;
    }
}

/// 签到
- (void)ybm_meViewDidSignCenter:(YBMMeHeaderView *)view {
    // 雪地埋点 行为-我的-积分签到
    [YBMAnalyticsUtil track:kaction_Me_IntegralSignIn];
    UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:@"ybmpage://membersignactivity"];;
    [self presentViewController:[[XYYNavigationController alloc] initWithRootViewController:vc] animated:YES completion:nil];
}

#pragma mark - YBMFindSimCollectionCellDelegate
/// cell里各个按钮点击
- (void)ybm_findSimCollectionCell:(YBMFindSimCollectionCell *)cell productButtonClick:(YBMProductItemModel *)model{
    [self trackProducItemClick:model];
}

#pragma mark - YBMExposureDelegate
- (void)exposureBuriedPointWithIndexPath:(NSIndexPath *)indexPath {
    // 商品曝光回调
    YBMProductItemModel * model = [self.recommendDataArray objectAtIndex:indexPath.row];
    
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    [dic setValue:self.recommendModel.sptype forKey:@"sptype"];
    [dic setValue:self.recommendModel.spid forKey:@"spid"];
    [dic setValue:self.recommendModel.sid forKey:@"sid"];
    [dic setValue:model.ID forKey:@"commodityId"];
    [dic setValue:model.showName forKey:@"commodityName"];
    /// 当前列表位置
    [dic setValue:@(indexPath.row) forKey:@"position"];
    /// 上级列表展示的文案
    [dic setValue:@"" forKey:@"cate_text"];
    /// 上级列表的位置
    [dic setValue:@(-1) forKey:@"cate_position"];
    
    [YBMAnalyticsUtil track:PAGE_PRODUCT_EXPOSURE properties:dic];
    
    NSLog(@"debug exposure : 我的 - %@", model.ID);
}

#pragma mark - 极光埋点

/// “我的订单页面”曝光
- (void)auroraAnalysisOrderListPageExposure {
    NSMutableDictionary *mutD = [NSMutableDictionary dictionary];
    mutD[@"account_id"] = XYYUserData.ShareUserData.accountId;
    mutD[@"merchant_id"] = [NSString stringWithFormat:@"%@", [XYYUserData ShareUserData].merchantId];
    mutD[@"jgspid"] = jgspid_orderlist_mine_exposure;
    // 极光埋点已移除 - app_orderListPage_view
    // [AnalysysAgent track:kApp_orderListPage_view properties:mutD];
}

#pragma mark - lazy
- (UICollectionView *)collectionView{
    if (!_collectionView) {
        //创建瀑布流布局
        YBMWaterFallLayout *layout = [YBMWaterFallLayout waterFallLayoutWithColumnCount:2];
        
        //设置各属性的值
        //    waterfall.rowSpacing = 10;
        //    waterfall.columnSpacing = 10;
        //    waterfall.sectionInset = UIEdgeInsetsMake(10, 10, 10, 10);
        
        //或者一次性设置
        [layout setColumnSpacing:10 rowSpacing:10 sectionInset:UIEdgeInsetsMake(100, 10, 0, 10)];
        
         //或者设置block
        @weakify(self)
        [layout setItemHeightBlock:^CGFloat(CGFloat itemWidth, NSIndexPath *indexPath) {
            @strongify(self)
            if (self.recommendDataArray.count > indexPath.row) {
                //根据图片的原始尺寸，及显示宽度，等比例缩放来计算显示高度
                YBMProductItemModel *model = self.recommendDataArray[indexPath.row];
                return model.cellHeight;
            }
            return 0;
        }];
        self.flowLayout = layout;
        
//        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
//        layout.sectionInset = UIEdgeInsetsMake(0, 10, 0, 10);
//        self.flowLayout = layout;
//        layout.scrollDirection = UICollectionViewScrollDirectionVertical;
//        layout.estimatedItemSize = CGSizeMake((kScreenWidth - 30)/2, 150);
        
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:self.flowLayout];
        _collectionView.backgroundColor = ColorFromHex(0xF7F7F8);
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
        _collectionView.exposureDelegate = self;
        _collectionView.alwaysBounceVertical = YES;
        
        [_collectionView registerNib:[UINib nibWithNibName:@"YBMFindSimCollectionCell" bundle:nil] forCellWithReuseIdentifier:@"YBMFindSimCollectionCell"];
        _collectionView.mj_footer = [YBMRefreshFooter footerWithRefreshingTarget:self refreshingAction:@selector(ybm_meViewLoadMoreRecommendData)];

        self.headerView.frame = CGRectMake(0, 0, kScreenWidth, 500);
        _collectionView.contentOffset = CGPointMake(0, 500);
        [_collectionView addSubview:self.headerView];
    }
    return _collectionView;
}

- (YBMMeHeaderView *)headerView{
    if (!_headerView) {
        YBMMeHeaderView *header = [[[NSBundle mainBundle] loadNibNamed:@"YBMMeHeaderView" owner:nil options:nil] lastObject];
//        header.frame = CGRectMake(0, 0, kScreenWidth, 500);
//        self.collectionView.contentOffset = CGPointMake(0, 500);
        header.delegate = self;
        header.vc = self;
        @weakify(self)
        header.blockHeight = ^(CGFloat height) {
            @strongify(self)
            self.headerView.frame = CGRectMake(0, 0, kScreenWidth, height);
            self.flowLayout.sectionInset = UIEdgeInsetsMake(height, 10, 0, 10);
            
            [self.collectionView setIgnoreReload:@"header_changed"];
            [self.collectionView reloadData];
        };
        _headerView = header;
    }
    return _headerView;
}

- (UIView *)navView {
    if (!_navView) {
        _navView = [UIView new];
        _navView.backgroundColor = UIColor.whiteColor;
        _navView.alpha = 0;
        
        UILabel *lab = [UILabel new];
        lab.textAlignment = NSTextAlignmentCenter;
        lab.textColor = ColorFromHex(0x292933);
        lab.font = [UIFont boldSystemFontOfSize:17.0f];
        lab.text = @"我的";
        [_navView addSubview:lab];
        [lab mas_makeConstraints:^(MASConstraintMaker *make) {
            CGFloat top = 27 + kTopBarDifHeight;
            make.top.mas_equalTo(@(top));
            make.height.mas_equalTo(30);
            make.centerX.mas_equalTo(_navView);
        }];
    }
    return _navView;
}

- (YBMEventButton *)messageButton {
    if (!_messageButton) {
        _messageButton = [YBMEventButton buttonWithType:UIButtonTypeCustom];
        [_messageButton setImage:[UIImage imageNamed:@"nav_me_message_black"] forState:UIControlStateNormal];
        [_messageButton addTarget:self action:@selector(messageActionButton) forControlEvents:UIControlEventTouchUpInside];
    }
    return _messageButton;
}

- (YBMEventButton *)setupButton {
    if (!_setupButton) {
        _setupButton = [YBMEventButton buttonWithType:UIButtonTypeCustom];
        [_setupButton setImage:[UIImage imageNamed:@"me_nav_shezhi_black"] forState:UIControlStateNormal];
        [_setupButton addTarget:self action:@selector(setupActionButton) forControlEvents:UIControlEventTouchUpInside];
    }
    return _setupButton;
}

- (YBMMeViewModel *)viewModel {
    if (!_viewModel) {
        _viewModel = [YBMMeViewModel new];
        _viewModel.delegate = self;
    }
    return _viewModel;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - 埋点
//页页面曝光
- (void)trackMePageExposure{
    YBMCMSTrackDataModel *trackData = [[YBMCMSTrackDataModel alloc]init];
    YBMQTSpmModel *spmEntity = [[YBMQTSpmModel alloc]init];
    spmEntity.spmB = @"me_0-0_0";
    trackData.spmEntity = spmEntity;
    NSString *spm = [YBMQTTrack createSpmWithModel:trackData vc:self];
    NSMutableDictionary *qtDic = NSMutableDictionary.new;
    qtDic[@"spm_cnt"] = spm;
    [YBMQTTrack track:kQTPageExposure attributes:qtDic];
}

//Tabbar 组件曝光
- (void)trackTabbarComponentExposure{
    YBMCMSTrackDataModel *trackData = [[YBMCMSTrackDataModel alloc]init];
    YBMQTSpmModel *spmEntity = [[YBMQTSpmModel alloc]init];
    spmEntity.spmB = @"me_0-0_0";
    spmEntity.spmC = @"newIndexTabbar@Z";
    trackData.spmEntity = spmEntity;
    NSString *spm = [YBMQTTrack createSpmWithModel:trackData vc:self];
    NSMutableDictionary *qtDic = NSMutableDictionary.new;
    qtDic[@"spm_cnt"] = spm;
    [YBMQTTrack track:kQTPageComponentExposure attributes:qtDic];
}

//Tabbar 子模块点击
- (void)trackTabbarComponentClickWithIndex:(NSInteger)index title:(NSString *)title{
    YBMCMSTrackDataModel *trackData = [[YBMCMSTrackDataModel alloc]init];
    YBMQTSpmModel *spmEntity = [[YBMQTSpmModel alloc]init];
    spmEntity.spmB = @"me_0-0_0";
    spmEntity.spmC = @"newIndexTabbar@Z";
    spmEntity.spmD = [NSString stringWithFormat:@"icon@%ld", index+1];
    trackData.spmEntity = spmEntity;

    YBMQTScmModel *scmEntity = [[YBMQTScmModel alloc]init];
    scmEntity.scmA = @"cms";
    scmEntity.scmC = @"all_0";
    scmEntity.scmD = title;
    trackData.scmEntity = scmEntity;
    
    NSString *spm = [YBMQTTrack createSpmWithModel:trackData vc:self];
    NSString *scm = [YBMQTTrack createSpmOrScmWithCmsModel:trackData trackType:YBMTrackTypeSCM];
    NSMutableDictionary *qtDic = NSMutableDictionary.new;
    qtDic[@"spm_cnt"] = spm;
    qtDic[@"scm_cnt"] = scm;
    [YBMQTTrack track:kQTActionSubModuleClick attributes:qtDic];
}

// 财富栏子模块点击
- (void)trackWealthClickIndex:(NSInteger)index{
    NSDictionary *dic = @{
        @1 : @"购物金",
        @2 : @"优惠券",
        @3 : @"红包",
        @4 : @"我的财富",
        @5 : @"去充值",
    };
    NSString *text = dic[@(index)];
    
    YBMCMSTrackDataModel *trackData = [[YBMCMSTrackDataModel alloc]init];
    YBMQTSpmModel *spmEntity = [[YBMQTSpmModel alloc]init];
    spmEntity.spmB = @"me_0-0_0";
    spmEntity.spmC = @"wealth@2";
    spmEntity.spmD = [NSString stringWithFormat:@"btn@%ld",index];
    trackData.spmEntity = spmEntity;

    YBMQTScmModel *scmEntity = [[YBMQTScmModel alloc]init];
    scmEntity.scmA = @"appFE";
    scmEntity.scmC = @"all_0";
    scmEntity.scmD = [NSString stringWithFormat:@"text-%@", text];
    trackData.scmEntity = scmEntity;
    
    NSString *spm = [YBMQTTrack createSpmWithModel:trackData vc:self];
    NSString *scm = [YBMQTTrack createSpmOrScmWithCmsModel:trackData trackType:YBMTrackTypeSCM];
    NSMutableDictionary *qtDic = NSMutableDictionary.new;
    qtDic[@"spm_cnt"] = spm;
    qtDic[@"scm_cnt"] = scm;
    [YBMQTTrack track:kQTActionSubModuleClick attributes:qtDic];
}

// 订单栏子模块点击
- (void)trackOrderIconClickIndex:(NSInteger)index{
    NSDictionary *dic = @{
        @0 : @"全部",
        @1 : @"待支付",
        @2 : @"待配送",
        @3 : @"配送中",
        @4 : @"退款/售后",
        @5 : @"待评价",
    };
    NSString *text = dic[@(index)];
    
    YBMCMSTrackDataModel *trackData = [[YBMCMSTrackDataModel alloc]init];
    YBMQTSpmModel *spmEntity = [[YBMQTSpmModel alloc]init];
    spmEntity.spmB = @"me_0-0_0";
    spmEntity.spmC = @"orderIcon@3";
    if (index == 0) {//全部
        spmEntity.spmD = @"subTitle@1";
    }else{
        spmEntity.spmD = [NSString stringWithFormat:@"icon@%ld",index];
    }
    trackData.spmEntity = spmEntity;

    YBMQTScmModel *scmEntity = [[YBMQTScmModel alloc]init];
    scmEntity.scmA = @"order";
    scmEntity.scmC = @"all_0";
    scmEntity.scmD = [NSString stringWithFormat:@"text-%@", text];
    trackData.scmEntity = scmEntity;
    
    NSString *spm = [YBMQTTrack createSpmWithModel:trackData vc:self];
    NSString *scm = [YBMQTTrack createSpmOrScmWithCmsModel:trackData trackType:YBMTrackTypeSCM];
    NSMutableDictionary *qtDic = NSMutableDictionary.new;
    qtDic[@"spm_cnt"] = spm;
    qtDic[@"scm_cnt"] = scm;
    [YBMQTTrack track:kQTActionSubModuleClick attributes:qtDic];
}

// 我的页功能栏组件子模块点击
- (void)trackOftenToolsClick:(YBMCommonToolsModel *)model index:(NSInteger)index{
    YBMCMSTrackDataModel *trackData = [[YBMCMSTrackDataModel alloc]init];
    YBMQTSpmModel *spmEntity = [[YBMQTSpmModel alloc]init];
    spmEntity.spmB = @"me_0-0_0";
    spmEntity.spmC = @"functionIcon@5";
    spmEntity.spmD = [NSString stringWithFormat:@"icon@%ld",index + 1];
    trackData.spmEntity = spmEntity;

    YBMQTScmModel *scmEntity = [[YBMQTScmModel alloc]init];
    scmEntity.scmA = @"admin";
    scmEntity.scmC = @"all_0";
    scmEntity.scmD = [NSString stringWithFormat:@"text-%@", model.name];
    trackData.scmEntity = scmEntity;
    
    NSString *spm = [YBMQTTrack createSpmWithModel:trackData vc:self];
    NSString *scm = [YBMQTTrack createSpmOrScmWithCmsModel:trackData trackType:YBMTrackTypeSCM];
    NSMutableDictionary *qtDic = NSMutableDictionary.new;
    qtDic[@"spm_cnt"] = spm;
    qtDic[@"scm_cnt"] = scm;
    [YBMQTTrack track:kQTActionSubModuleClick attributes:qtDic];
}

//单商品点击、曝光，上报的参数一样
- (NSMutableDictionary *)configProductItem:(YBMProductItemModel *)model isClick:(BOOL)isClick{
    YBMCMSTrackDataModel *trackData = [[YBMCMSTrackDataModel alloc]init];
    YBMQTSpmModel *spmEntity = [[YBMQTSpmModel alloc]init];
    spmEntity.spmB = @"me_0-0_0";
    spmEntity.spmC = @"recommendList@6";
    spmEntity.spmD = [NSString stringWithFormat:@"prod@%ld", model.index];
    trackData.spmEntity = spmEntity;
    
    YBMQTScmModel *scmEntity = [[YBMQTScmModel alloc]init];
    scmEntity.scmA = @"recommend";
    scmEntity.scmB = self.recommendModel.expId;
    scmEntity.scmC = @"all_0";
    scmEntity.scmD = [NSString stringWithFormat:@"prod-%@", model.ID];
    if (isClick) {
        NSString *scme = model.trackData.scmEntity.scmE;
        if (scme.length) {
            scmEntity.scmE = scme;
            model.trackData = nil;
        } else {
            scmEntity.scmE = [NSString stringWithFormat:@"%@%@", self.recommendModel.scmId, [YBMQTTrack.sharedInstance generateRandomStrLength:6]];
        }
    }else{
        scmEntity.scmE = self.recommendModel.scmId;
    }
    trackData.scmEntity = scmEntity;
    
    NSString *spm = [YBMQTTrack createSpmWithModel:trackData vc:self];
    NSString *scm = [YBMQTTrack createSpmOrScmWithCmsModel:trackData trackType:YBMTrackTypeSCM];
    
    NSMutableDictionary *qtDic = NSMutableDictionary.new;
//    qtDic[@"result_cnt"] = @(self.queryModel.totalCount);//列表总条数
    qtDic[@"product_id"] = model.ID;
    qtDic[@"product_name"] = model.showName;
    qtDic[@"qt_sku_data"] = model.qtSkuData;
    qtDic[@"qt_list_data"] = model.qtListData;
    qtDic[@"spm_cnt"] = spm;
    qtDic[@"scm_cnt"] = scm;
    return qtDic;
}

//单商品曝光
- (void)trackProducItemExposure:(YBMProductItemModel *)model{
    NSMutableDictionary *qtDic = [self configProductItem:model isClick:NO];
    [YBMQTTrack track:kQTPageListProductExposure attributes:qtDic];
}

//单商品点击
- (void)trackProducItemClick:(YBMProductItemModel *)model{
    NSMutableDictionary *qtDic = [self configProductItem:model isClick:YES];
    [YBMQTTrack track:kQTActionListProductClick attributes:qtDic];
}

@end
