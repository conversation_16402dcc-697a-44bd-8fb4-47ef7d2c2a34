//
//  NSObject+Controller.m
//  YaoBangMang
//
//  Created by 骆阳光 on 2020/3/4.
//  Copyright © 2020 XiaoYaoYao.Ltd. All rights reserved.
//

#import "NSObject+Controller.h"
#import "YBMHomeV3Controller.h"

@implementation NSObject (Controller)

- (UIViewController *)ybm_viewController {
    UIWindow* window = [UIApplication sharedApplication].windows.firstObject;
    if (window.windowLevel != UIWindowLevelNormal){
        NSArray *windows = [[UIApplication sharedApplication] windows];
        for(UIWindow * tmpWin in windows){
            if (tmpWin.windowLevel == UIWindowLevelNormal) {
                window = tmpWin;
                break;
            }
        }
    }
    UIViewController *result = window.rootViewController;
    while (result.presentedViewController) {
        result = result.presentedViewController;
    }
    if ([result isKindOfClass:[UITabBarController class]]) {
        result = [(UITabBarController *)result selectedViewController];
    }
    if ([result isKindOfClass:[UINavigationController class]]) {
        result = [(UINavigationController *)result topViewController];
    }
    return result;
}

// 判断当前页面是否是首页
- (BOOL)isHomePage {
    UIViewController *vc = self.ybm_viewController;
    if ([vc isKindOfClass:[YBMHomeV3Controller class]]) {
        return YES;
    } else {
        return NO;
    }
}
 
@end
