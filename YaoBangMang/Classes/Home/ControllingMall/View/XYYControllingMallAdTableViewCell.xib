<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="12120" systemVersion="16E195" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="12088"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" rowHeight="90" id="KGk-i7-Jjw" customClass="XYYControllingMallAdTableViewCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="90"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="89.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" alwaysBounceHorizontal="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="d7A-4f-REg">
                        <rect key="frame" x="10" y="10" width="300" height="79.5"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                        <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="7" minimumInteritemSpacing="7" id="EmD-pv-j7i">
                            <size key="itemSize" width="50" height="50"/>
                            <size key="headerReferenceSize" width="0.0" height="0.0"/>
                            <size key="footerReferenceSize" width="0.0" height="0.0"/>
                            <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                        </collectionViewFlowLayout>
                    </collectionView>
                </subviews>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="d7A-4f-REg" secondAttribute="trailing" constant="10" id="8cO-En-Zhc"/>
                    <constraint firstAttribute="bottom" secondItem="d7A-4f-REg" secondAttribute="bottom" id="GXB-U1-7pP"/>
                    <constraint firstItem="d7A-4f-REg" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="10" id="Lg7-Zd-g1r"/>
                    <constraint firstItem="d7A-4f-REg" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="10" id="MrF-Jd-DfL"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="myContentCollectionView" destination="d7A-4f-REg" id="4Ue-6Q-Hdv"/>
            </connections>
            <point key="canvasLocation" x="34" y="77"/>
        </tableViewCell>
    </objects>
</document>
