<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="13771" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13772"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" id="KGk-i7-Jjw" customClass="XYYControllingMallProductCollectionTableViewCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="150"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="149.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="qPU-kr-Jql">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="149.5"/>
                        <color key="backgroundColor" red="0.98039215686274506" green="0.98039215686274506" blue="0.98039215686274506" alpha="1" colorSpace="calibratedRGB"/>
                        <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="7" minimumInteritemSpacing="7" id="CWV-lW-wJt">
                            <size key="itemSize" width="50" height="50"/>
                            <size key="headerReferenceSize" width="0.0" height="0.0"/>
                            <size key="footerReferenceSize" width="0.0" height="0.0"/>
                            <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                        </collectionViewFlowLayout>
                    </collectionView>
                </subviews>
                <constraints>
                    <constraint firstItem="qPU-kr-Jql" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="cUM-XE-2aw"/>
                    <constraint firstAttribute="trailing" secondItem="qPU-kr-Jql" secondAttribute="trailing" id="iMG-yF-MkN"/>
                    <constraint firstAttribute="bottom" secondItem="qPU-kr-Jql" secondAttribute="bottom" id="wVe-y8-MNk"/>
                    <constraint firstItem="qPU-kr-Jql" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="zPg-v1-MxQ"/>
                </constraints>
            </tableViewCellContentView>
            <connections>
                <outlet property="myCollectionView" destination="qPU-kr-Jql" id="Jro-rJ-6lq"/>
            </connections>
        </tableViewCell>
    </objects>
</document>
