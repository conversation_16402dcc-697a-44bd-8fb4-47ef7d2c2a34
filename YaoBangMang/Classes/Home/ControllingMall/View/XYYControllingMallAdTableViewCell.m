//
//  XYYControllingMallAdTableViewCell.m
//  YaoBangMang
//
//  Created by 钱伟龙 on 2017/5/19.
//  Copyright © 2017年 XiaoYaoYao.Ltd. All rights reserved.
//

#import "XYYControllingMallAdTableViewCell.h"
#import "YBMTabBarController.h"

@interface XYYControllingMallAdCollectionViewCell : UICollectionViewCell

@property (nonatomic,strong,readonly) UIImageView * imageView;

- (void)updateCellWithInfo:(NSDictionary *)info context:(id)context;

@end

@implementation XYYControllingMallAdCollectionViewCell

@synthesize imageView = _imageView;

- (UIImageView *)imageView{
    if (!_imageView) {
        _imageView = [[UIImageView alloc] initWithFrame:self.contentView.bounds];
        _imageView.contentMode = UIViewContentModeScaleAspectFill;
        _imageView.layer.cornerRadius = 5.f;
        _imageView.clipsToBounds = YES;
        _imageView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        [self.contentView addSubview:_imageView];
    }
    return _imageView;
}

- (void)updateCellWithInfo:(NSDictionary *)info context:(id)context
{
    if ([context[@"imgUrl"] length]) {
         [self.imageView sd_setImageWithURL:[NSURL URLWithString:[[XYYManageImg stringByAppendingString:context[@"imgUrl"]] stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding]] placeholderImage:nil];
    }
}


@end

@interface XYYControllingMallAdTableViewCell ()<UICollectionViewDelegate,UICollectionViewDataSource>

@property (weak, nonatomic) IBOutlet UICollectionView *myContentCollectionView;
@property (nonatomic,strong) NSArray * dataArr;


@end

@implementation XYYControllingMallAdTableViewCell

- (void)awakeFromNib {
    [super awakeFromNib];
    
    self.myContentCollectionView.delegate = self;
    self.myContentCollectionView.dataSource = self;
    
    [self.myContentCollectionView registerClass:[XYYControllingMallAdCollectionViewCell class] forCellWithReuseIdentifier:@"XYYControllingMallAdCollectionViewCell"];
}

- (void)updateCellWithInfo:(NSDictionary *)info context:(id)context
{
    self.dataArr = context[@"items"];
    [self.myContentCollectionView reloadData];
}

#pragma mark - 

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath
{
    CGFloat cellWidth = (CGRectGetWidth(collectionView.bounds) - (([self.dataArr count]) - 1) * 7.f) / [self.dataArr count];
    CGFloat cellHeight = cellWidth * 15 / 19.f;
    return CGSizeMake(cellWidth, cellHeight);
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section
{
    return self.dataArr.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath
{
    XYYControllingMallAdCollectionViewCell * cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"XYYControllingMallAdCollectionViewCell" forIndexPath:indexPath];
    [cell updateCellWithInfo:nil context:self.dataArr[indexPath.item]];
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath
{
    NSDictionary * info = self.dataArr[indexPath.row];
    UIViewController* rootViewController = [UIApplication sharedApplication].delegate.window.rootViewController;
    if ([rootViewController isKindOfClass:[YBMTabBarController class]]) {
        UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:info[@"action"]];
        if (vc != nil) {
            UITabBarController *tab = (UITabBarController *)rootViewController;
            UINavigationController *nav = tab.selectedViewController;
            [nav pushViewController:vc animated:YES];
        }
    }
}

@end
