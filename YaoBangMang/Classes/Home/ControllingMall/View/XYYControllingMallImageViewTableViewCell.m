//
//  XYYControllingMallImageViewTableViewCell.m
//  YaoBangMang
//
//  Created by 钱伟龙 on 2017/5/24.
//  Copyright © 2017年 XiaoYaoYao.Ltd. All rights reserved.
//

#import "XYYControllingMallImageViewTableViewCell.h"

@interface XYYControllingMallImageViewTableViewCell ()

@property (weak, nonatomic) IBOutlet UIImageView *contentImageView;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bottonDistance;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *leftDistance;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *rightDistance;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *topDistance;

@end

@implementation XYYControllingMallImageViewTableViewCell


- (void)awakeFromNib {
    [super awakeFromNib];
    
    
}

- (void)updateCellWithInfo:(NSDictionary *)info context:(id)context{
    NSDictionary * infoDic = context;
    NSArray * itemsArr = infoDic[@"items"];
    if ([itemsArr count]) {
        NSDictionary * itemInfo = itemsArr[0];
        if ([itemInfo[@"imgUrl"] length]) {
            [self.contentImageView sd_setImageWithURL:[NSURL URLWithString:[[XYYManageImg stringByAppendingString:itemInfo[@"imgUrl"]] stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding]] placeholderImage:nil];
        }
    }
    NSArray * paddingArr = infoDic[@"padding"];
    if ([paddingArr count] == 4) {
        self.leftDistance.constant = [paddingArr[0] doubleValue] ;
        self.topDistance.constant = [paddingArr[1] doubleValue];
        self.rightDistance.constant = [paddingArr[2] doubleValue];
        self.bottonDistance.constant = [paddingArr[3] doubleValue];
    }
}

@end
