<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="XYYControllingMallProductCollectionViewCell">
            <rect key="frame" x="0.0" y="0.0" width="194" height="322"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="194" height="322"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MXf-ED-fCr">
                        <rect key="frame" x="0.0" y="0.0" width="194" height="194"/>
                        <subviews>
                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="ev9-ra-410">
                                <rect key="frame" x="10" y="10" width="174" height="174"/>
                            </imageView>
                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="M7D-0p-gfQ">
                                <rect key="frame" x="67" y="67" width="60" height="60"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="60" id="52e-Ie-BQ5"/>
                                    <constraint firstAttribute="width" constant="60" id="GsK-WQ-vKw"/>
                                </constraints>
                            </imageView>
                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="kN0-Xa-duY">
                                <rect key="frame" x="0.0" y="0.0" width="194" height="194"/>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="¥12.50" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="5" translatesAutoresizingMaskIntoConstraints="NO" id="7gL-3T-wQF">
                                <rect key="frame" x="47" y="180" width="41" height="14"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="14" id="24A-3q-eiX"/>
                                    <constraint firstAttribute="width" constant="41" id="rgp-uQ-KXH"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="10"/>
                                <color key="textColor" red="0.4039215686" green="0.4039215686" blue="0.45098039220000002" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="color" keyPath="ibBorderColor">
                                        <color key="value" red="0.16862745098039217" green="0.20392156862745098" blue="0.24705882352941178" alpha="1" colorSpace="calibratedRGB"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="ibCornerRadius">
                                        <real key="value" value="2"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="控销价" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="UvD-Vp-nNa">
                                <rect key="frame" x="10" y="180" width="38" height="14"/>
                                <color key="backgroundColor" red="0.16862745098039217" green="0.20392156862745098" blue="0.24705882352941178" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="14" id="FCw-Ft-cJZ"/>
                                    <constraint firstAttribute="width" constant="38" id="u6r-3F-frR"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="11"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="41%" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="5" translatesAutoresizingMaskIntoConstraints="NO" id="pzk-A3-DnK">
                                <rect key="frame" x="134" y="180" width="41" height="14"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="14" id="rOl-PT-o5k"/>
                                    <constraint firstAttribute="width" priority="999" constant="41" id="vAH-E6-Wy6"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="10"/>
                                <color key="textColor" red="0.4039215686" green="0.4039215686" blue="0.45098039220000002" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="color" keyPath="ibBorderColor">
                                        <color key="value" red="0.16862745098039217" green="0.20392156862745098" blue="0.24705882352941178" alpha="1" colorSpace="calibratedRGB"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="ibCornerRadius">
                                        <real key="value" value="2"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="毛利" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="PMg-hZ-bhc">
                                <rect key="frame" x="97" y="180" width="38" height="14"/>
                                <color key="backgroundColor" red="0.16862745098039217" green="0.20392156862745098" blue="0.24705882352941178" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="14" id="Cfi-0o-mod"/>
                                    <constraint firstAttribute="width" constant="38" id="LgH-m3-avC"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="11"/>
                                <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                        <constraints>
                            <constraint firstItem="PMg-hZ-bhc" firstAttribute="leading" secondItem="7gL-3T-wQF" secondAttribute="trailing" constant="9" id="3wt-SE-KcB"/>
                            <constraint firstItem="kN0-Xa-duY" firstAttribute="top" secondItem="MXf-ED-fCr" secondAttribute="top" id="6dI-O5-Hu9"/>
                            <constraint firstAttribute="width" secondItem="MXf-ED-fCr" secondAttribute="height" multiplier="1:1" id="9AA-YI-JVA"/>
                            <constraint firstItem="UvD-Vp-nNa" firstAttribute="leading" secondItem="MXf-ED-fCr" secondAttribute="leading" constant="10" id="AL3-b0-8jS"/>
                            <constraint firstItem="M7D-0p-gfQ" firstAttribute="centerY" secondItem="MXf-ED-fCr" secondAttribute="centerY" id="C27-1y-Mv0"/>
                            <constraint firstItem="ev9-ra-410" firstAttribute="top" secondItem="MXf-ED-fCr" secondAttribute="top" constant="10" id="CHR-Ye-UfB"/>
                            <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="pzk-A3-DnK" secondAttribute="trailing" id="FfJ-gi-JD1"/>
                            <constraint firstAttribute="bottom" secondItem="pzk-A3-DnK" secondAttribute="bottom" id="NIb-ZR-60v"/>
                            <constraint firstItem="M7D-0p-gfQ" firstAttribute="centerX" secondItem="MXf-ED-fCr" secondAttribute="centerX" id="Nnw-dw-IfE"/>
                            <constraint firstAttribute="bottom" secondItem="7gL-3T-wQF" secondAttribute="bottom" id="Pzg-RJ-Hv2"/>
                            <constraint firstAttribute="trailing" secondItem="kN0-Xa-duY" secondAttribute="trailing" id="a8u-7K-FKp"/>
                            <constraint firstItem="7gL-3T-wQF" firstAttribute="leading" secondItem="UvD-Vp-nNa" secondAttribute="trailing" constant="-1" id="dS3-2f-eGk"/>
                            <constraint firstItem="pzk-A3-DnK" firstAttribute="leading" secondItem="PMg-hZ-bhc" secondAttribute="trailing" constant="-1" id="g4m-4p-VMc"/>
                            <constraint firstItem="ev9-ra-410" firstAttribute="leading" secondItem="MXf-ED-fCr" secondAttribute="leading" constant="10" id="geN-S9-vtY"/>
                            <constraint firstAttribute="bottom" secondItem="PMg-hZ-bhc" secondAttribute="bottom" id="jOy-r1-JL2"/>
                            <constraint firstItem="kN0-Xa-duY" firstAttribute="leading" secondItem="MXf-ED-fCr" secondAttribute="leading" id="l58-5d-mER"/>
                            <constraint firstAttribute="trailing" secondItem="ev9-ra-410" secondAttribute="trailing" constant="10" id="pZH-5s-W1X"/>
                            <constraint firstAttribute="bottom" secondItem="kN0-Xa-duY" secondAttribute="bottom" id="ptr-6D-f5X"/>
                            <constraint firstAttribute="bottom" secondItem="ev9-ra-410" secondAttribute="bottom" constant="10" id="tiD-Nu-9Et"/>
                            <constraint firstAttribute="bottom" secondItem="UvD-Vp-nNa" secondAttribute="bottom" id="vR5-Fb-uBV"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="ibCornerRadius">
                                <real key="value" value="5"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="感冒药感冒药感冒药感冒药" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Xam-pi-aC9">
                        <rect key="frame" x="10" y="200" width="174" height="19.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                        <color key="textColor" red="0.16078431369999999" green="0.16078431369999999" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                        <color key="highlightedColor" red="0.16078431369999999" green="0.16078431369999999" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="¥10.00" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="12" translatesAutoresizingMaskIntoConstraints="NO" id="Xum-CV-Mib">
                        <rect key="frame" x="10" y="270" width="52.5" height="21"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <color key="textColor" red="1" green="0.12941176470588234" blue="0.12941176470588234" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0.4克*36粒0.4克*36粒粒" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="s30-fk-hgK">
                        <rect key="frame" x="10" y="225.5" width="160" height="17"/>
                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                        <color key="textColor" red="0.58039215690000001" green="0.58039215690000001" blue="0.65098039220000004" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="DLv-Si-B30" customClass="XYYAddAndReduceView">
                        <rect key="frame" x="90" y="290" width="94" height="27"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="27" id="IDa-C3-0eh"/>
                            <constraint firstAttribute="width" constant="94" id="ioc-xt-ejn"/>
                        </constraints>
                    </view>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bCD-ug-uNk">
                        <rect key="frame" x="0.0" y="286.5" width="44" height="34"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="44" id="0VN-4x-9wf"/>
                        </constraints>
                    </button>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" horizontalCompressionResistancePriority="749" text="暂无购买权限" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Al1-Uw-eqG">
                        <rect key="frame" x="92" y="294.5" width="92" height="18"/>
                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                        <color key="textColor" red="0.95294117649999999" green="0.59607843140000005" blue="0.0" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Mum-ul-MCP" customClass="XYYTagsLabel">
                        <rect key="frame" x="10" y="248.5" width="30.5" height="15.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="11"/>
                        <color key="textColor" red="0.16078431369999999" green="0.16078431369999999" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                        <color key="highlightedColor" red="0.16078431369999999" green="0.16078431369999999" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ifu-nm-jma" customClass="XYYTagsLabel">
                        <rect key="frame" x="45.5" y="248.5" width="30.5" height="15.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="11"/>
                        <color key="textColor" red="0.16078431369999999" green="0.16078431369999999" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                        <color key="highlightedColor" red="0.16078431369999999" green="0.16078431369999999" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2dM-Ed-qPT" customClass="XYYTagsLabel">
                        <rect key="frame" x="81" y="248.5" width="30.5" height="15.5"/>
                        <fontDescription key="fontDescription" type="system" pointSize="11"/>
                        <color key="textColor" red="0.16078431369999999" green="0.16078431369999999" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                        <color key="highlightedColor" red="0.16078431369999999" green="0.16078431369999999" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                    </label>
                </subviews>
            </view>
            <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
            <constraints>
                <constraint firstItem="Mum-ul-MCP" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="10" id="0Li-Vq-Zqb"/>
                <constraint firstItem="MXf-ED-fCr" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="1GB-QK-NIH"/>
                <constraint firstItem="Mum-ul-MCP" firstAttribute="top" secondItem="s30-fk-hgK" secondAttribute="bottom" constant="6" id="3CU-qU-LhB"/>
                <constraint firstItem="Xam-pi-aC9" firstAttribute="top" secondItem="MXf-ED-fCr" secondAttribute="bottom" constant="6" id="5Mn-a7-vs9"/>
                <constraint firstAttribute="trailing" secondItem="MXf-ED-fCr" secondAttribute="trailing" id="5r2-Cr-Mmw"/>
                <constraint firstAttribute="trailing" secondItem="Al1-Uw-eqG" secondAttribute="trailing" constant="10" id="Aie-6N-GeI"/>
                <constraint firstItem="Al1-Uw-eqG" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="bCD-ug-uNk" secondAttribute="trailing" id="B2U-TB-XR6"/>
                <constraint firstItem="Xum-CV-Mib" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="10" id="DEU-5b-yYG"/>
                <constraint firstItem="ifu-nm-jma" firstAttribute="leading" secondItem="Mum-ul-MCP" secondAttribute="trailing" constant="5" id="EGd-Ck-xR3"/>
                <constraint firstItem="MXf-ED-fCr" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="HeS-ph-z6c"/>
                <constraint firstAttribute="trailing" secondItem="DLv-Si-B30" secondAttribute="trailing" constant="10" id="IMk-6L-UoJ"/>
                <constraint firstItem="s30-fk-hgK" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="10" id="RLF-2b-L5X"/>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="Xam-pi-aC9" secondAttribute="trailing" constant="10" id="TiD-ta-Dhh"/>
                <constraint firstItem="Xum-CV-Mib" firstAttribute="top" secondItem="Mum-ul-MCP" secondAttribute="bottom" constant="6" id="Us5-DD-DrA"/>
                <constraint firstItem="Xam-pi-aC9" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="10" id="atn-Dr-YVv"/>
                <constraint firstItem="s30-fk-hgK" firstAttribute="top" secondItem="Xam-pi-aC9" secondAttribute="bottom" constant="6" id="dww-pZ-sJ0"/>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="Xum-CV-Mib" secondAttribute="trailing" constant="10" id="fOB-Bh-iKm"/>
                <constraint firstItem="bCD-ug-uNk" firstAttribute="centerY" secondItem="DLv-Si-B30" secondAttribute="centerY" id="he1-v4-SbR"/>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="s30-fk-hgK" secondAttribute="trailing" constant="10" id="hhw-Q9-7PV"/>
                <constraint firstAttribute="bottom" secondItem="DLv-Si-B30" secondAttribute="bottom" constant="5" id="ho4-8I-v4e"/>
                <constraint firstItem="2dM-Ed-qPT" firstAttribute="top" secondItem="ifu-nm-jma" secondAttribute="top" id="kOt-GW-f3F"/>
                <constraint firstItem="Al1-Uw-eqG" firstAttribute="centerY" secondItem="bCD-ug-uNk" secondAttribute="centerY" id="o4L-4n-jvy"/>
                <constraint firstItem="ifu-nm-jma" firstAttribute="top" secondItem="Mum-ul-MCP" secondAttribute="top" id="ome-Y3-BNz"/>
                <constraint firstItem="2dM-Ed-qPT" firstAttribute="leading" secondItem="ifu-nm-jma" secondAttribute="trailing" constant="5" id="qBE-0f-uHk"/>
                <constraint firstItem="bCD-ug-uNk" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="wUg-O3-h23"/>
            </constraints>
            <size key="customSize" width="194" height="322"/>
            <connections>
                <outlet property="addAndReduceView" destination="DLv-Si-B30" id="ASM-pK-v35"/>
                <outlet property="canBuyProductLabel" destination="Al1-Uw-eqG" id="OAv-WZ-lDB"/>
                <outlet property="controlEarnLabel" destination="pzk-A3-DnK" id="D0v-Sr-Od6"/>
                <outlet property="controlEarnTitleLabel" destination="PMg-hZ-bhc" id="YKs-CT-BEf"/>
                <outlet property="controlPriceLabel" destination="7gL-3T-wQF" id="ULC-Xh-7dG"/>
                <outlet property="controlPriceTitleLabel" destination="UvD-Vp-nNa" id="dMh-CP-JGU"/>
                <outlet property="iconImageView" destination="kN0-Xa-duY" id="gnx-O9-hOe"/>
                <outlet property="productDescLabel" destination="s30-fk-hgK" id="m3C-7v-cIO"/>
                <outlet property="productImageView" destination="ev9-ra-410" id="fRG-mh-EaH"/>
                <outlet property="productNameLabel" destination="Xam-pi-aC9" id="z6b-FZ-nEB"/>
                <outlet property="productPriceLabel" destination="Xum-CV-Mib" id="Uw2-CD-E5k"/>
                <outlet property="stateImageView" destination="M7D-0p-gfQ" id="oxk-Xi-MpY"/>
                <outlet property="tagsLabelOne" destination="Mum-ul-MCP" id="piF-Y6-PNc"/>
                <outlet property="tagsLabelThree" destination="2dM-Ed-qPT" id="nI4-k0-zXj"/>
                <outlet property="tagsLabelTwo" destination="ifu-nm-jma" id="YRg-La-7hG"/>
            </connections>
            <point key="canvasLocation" x="56" y="74"/>
        </collectionViewCell>
    </objects>
</document>
