//
//  XYYControllingMallProductCollectionTableViewCell.m
//  YaoBangMang
//
//  Created by 钱伟龙 on 2017/5/20.
//  Copyright © 2017年 XiaoYaoYao.Ltd. All rights reserved.
//

#import "XYYControllingMallProductCollectionTableViewCell.h"
#import "XYYControllingMallProductCollectionViewCell.h"
#import "YBMTabBarController.h"
#import "XYYControllingMallProductModel.h"

@interface XYYControllingMallProductCollectionTableViewCell ()<UICollectionViewDelegate,UICollectionViewDataSource>

@property (weak, nonatomic) IBOutlet UICollectionView *myCollectionView;

@property (nonatomic,strong) NSArray * dataArr;
@end

@implementation XYYControllingMallProductCollectionTableViewCell

- (void)awakeFromNib {
    [super awakeFromNib];
    
    self.myCollectionView.delegate = self;
    self.myCollectionView.dataSource = self;
    
    [self.myCollectionView registerNib:[UINib nibWithNibName:@"XYYControllingMallProductCollectionViewCell" bundle:nil] forCellWithReuseIdentifier:@"XYYControllingMallProductCollectionViewCellReuse"];
}

- (void)updateCellWithInfo:(NSDictionary *)info context:(id)context
{
    self.dataArr = context[@"cellData"];
    [self.myCollectionView reloadData];
}

#pragma mark -

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath
{
    CGFloat cellWidth = (CGRectGetWidth(collectionView.bounds) - 7) / 2.f;
    CGFloat cellHeight = cellWidth + 135.f;
    return CGSizeMake(cellWidth, cellHeight);
}


- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section
{
    return self.dataArr.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath
{
    XYYControllingMallProductCollectionViewCell * cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"XYYControllingMallProductCollectionViewCellReuse" forIndexPath:indexPath];
    [cell updateCellWithInfo:nil context:self.dataArr[indexPath.item]];
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath
{
    XYYControllingMallProductModel * model = self.dataArr[indexPath.item];
    UIViewController* rootViewController = [UIApplication sharedApplication].delegate.window.rootViewController;
    NSString *urlString = [NSString stringWithFormat:@"ybmpage://productdetail?product_id=%@",model.id];
    if ([rootViewController isKindOfClass:[YBMTabBarController class]]) {
        UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:urlString];
        if (vc != nil) {
            UITabBarController *tab = (UITabBarController *)rootViewController;
            UINavigationController *nav = tab.selectedViewController;
            [nav pushViewController:vc animated:YES];
        }
    }
}

@end
