<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="12120" systemVersion="16E195" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="12088"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="88" id="KGk-i7-Jjw" customClass="XYYControllingMallImageViewTableViewCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="88"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="87.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <imageView userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="mTQ-VV-sLO">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="87.5"/>
                    </imageView>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="mTQ-VV-sLO" secondAttribute="bottom" id="Pw4-xs-W2n"/>
                    <constraint firstItem="mTQ-VV-sLO" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="dqe-0D-9El"/>
                    <constraint firstAttribute="trailing" secondItem="mTQ-VV-sLO" secondAttribute="trailing" id="g4l-X5-95k"/>
                    <constraint firstItem="mTQ-VV-sLO" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="pZQ-XQ-UM7"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
            <connections>
                <outlet property="bottonDistance" destination="Pw4-xs-W2n" id="8xA-tH-ALA"/>
                <outlet property="contentImageView" destination="mTQ-VV-sLO" id="GPf-zO-Zd7"/>
                <outlet property="leftDistance" destination="dqe-0D-9El" id="vIj-8R-7bw"/>
                <outlet property="rightDistance" destination="g4l-X5-95k" id="VW5-x2-f4h"/>
                <outlet property="topDistance" destination="pZQ-XQ-UM7" id="Ivu-5s-BmQ"/>
            </connections>
            <point key="canvasLocation" x="34" y="76"/>
        </tableViewCell>
    </objects>
</document>
