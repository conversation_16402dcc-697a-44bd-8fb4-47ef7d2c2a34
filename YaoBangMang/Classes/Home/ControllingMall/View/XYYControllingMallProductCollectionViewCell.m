//
//  XYYControllingMallProductCollectionViewCell.m
//  YaoBangMang
//
//  Created by 钱伟龙 on 2017/5/20.
//  Copyright © 2017年 XiaoYaoYao.Ltd. All rights reserved.
//

#import "XYYControllingMallProductCollectionViewCell.h"
#import "XYYAddAndReduceView.h"
#import "XYYControllingMallProductModel.h"
#import "ShareSingleTon.h"
#import "XYYTagsLabel.h"
#import "YBMProductTagModel.h"
#import "UILabel+LineSpace.h"

@interface XYYControllingMallProductCollectionViewCell ()<XYYAddAndReduceViewDelegate>

@property (weak, nonatomic) IBOutlet UIImageView *productImageView;
@property (weak, nonatomic) IBOutlet UIImageView *stateImageView;
@property (weak, nonatomic) IBOutlet UIImageView *iconImageView;

@property (weak, nonatomic) IBOutlet UILabel *productNameLabel;

@property (weak, nonatomic) IBOutlet UILabel *productPriceLabel;

@property (weak, nonatomic) IBOutlet UILabel *productDescLabel;

@property (weak, nonatomic) IBOutlet XYYAddAndReduceView *addAndReduceView;

@property (nonatomic,strong) XYYControllingMallProductModel * productModel;
@property (weak, nonatomic) IBOutlet UILabel *canBuyProductLabel;

@property (weak, nonatomic) IBOutlet UILabel *controlPriceTitleLabel;
@property (weak, nonatomic) IBOutlet UILabel *controlPriceLabel;
@property (weak, nonatomic) IBOutlet UILabel *controlEarnTitleLabel;
@property (weak, nonatomic) IBOutlet UILabel *controlEarnLabel;

@property (weak, nonatomic) IBOutlet XYYTagsLabel *tagsLabelOne;
@property (weak, nonatomic) IBOutlet XYYTagsLabel *tagsLabelTwo;
@property (weak, nonatomic) IBOutlet XYYTagsLabel *tagsLabelThree;

@end

@implementation XYYControllingMallProductCollectionViewCell

- (void)awakeFromNib {
    [super awakeFromNib];
    
    self.addAndReduceView.delegate = self;
    self.controlPriceLabel.layer.borderWidth = PiexlToPoint(1.f);
    self.controlEarnLabel.layer.borderWidth = PiexlToPoint(1.f);
    
    UIBezierPath * bezierPath1 = [UIBezierPath bezierPathWithRoundedRect:self.controlPriceTitleLabel.bounds   byRoundingCorners:UIRectCornerTopLeft | UIRectCornerBottomLeft cornerRadii:CGSizeMake(2.f, 2.f)];
    CAShapeLayer * shapeLayer1 = [CAShapeLayer layer];
    shapeLayer1.frame = self.controlPriceTitleLabel.bounds;
    shapeLayer1.path = bezierPath1.CGPath;
    self.controlPriceTitleLabel.layer.mask = shapeLayer1;
    
    UIBezierPath * bezierPath2 = [UIBezierPath bezierPathWithRoundedRect:self.controlEarnTitleLabel.bounds   byRoundingCorners:UIRectCornerTopLeft | UIRectCornerBottomLeft cornerRadii:CGSizeMake(2.f, 2.f)];
    CAShapeLayer * shapeLayer2 = [CAShapeLayer layer];
    shapeLayer2.frame = self.controlEarnTitleLabel.bounds;
    shapeLayer2.path = bezierPath2.CGPath;
    self.controlEarnTitleLabel.layer.mask = shapeLayer2;
}

- (void)addAndReduceViewChangeProductNumber:(NSInteger)number toCount:(NSInteger)productCount{
    if (productCount == 0) {
        [XYYWindow showSpinner:@"加载中..."];
        [InterfaceManager shoppingCartRemoveProductsFromCartWithProductIDs:self.productModel.id block:^(BOOL isSuccess, NSString *msg, id data) {
            [XYYWindow hideSpinner];
            if (isSuccess) {
                self.addAndReduceView.animationType = HideAnimation;
                [self.addAndReduceView updateViewWithInfo:nil context:[NSString stringWithFormat:@"%ld",productCount]];
                [[ShareSingleTon ShareSingleTonManager] updateShoppingNumber:@"0" changeKey:self.productModel.id];
                [[NSNotificationCenter defaultCenter] postNotificationName:@"ControllingMallUpdateNum" object:nil];
            }else{
                MBShowFailureMessage(msg);
                [self.addAndReduceView updateViewWithInfo:nil context:[NSString stringWithFormat:@"%ld",number]];
            }
        }];
    }else{
        [XYYWindow showSpinner:@"加载中..."];
        [InterfaceManager addSubProductBagByID:self.productModel.id Count:[NSString stringWithFormat:@"%ld",productCount] Completion:^(BOOL isSuccess, NSString *msg, id data) {
            [[UIApplication sharedApplication].keyWindow hideSpinner];
            self.addAndReduceView.animationType = NoAnimation;
            if (isSuccess) {
                if (number == 0) {
                    self.addAndReduceView.animationType = ShowAnimation;
                    [[NSNotificationCenter defaultCenter] postNotificationName:@"ControllingMallUpdateNum" object:nil];
                }
                if ([data integerValue] == 0) {
                    self.addAndReduceView.animationType = NoAnimation;
                }
                [[ShareSingleTon ShareSingleTonManager] updateShoppingNumber:[data stringValue] changeKey:self.productModel.id];
                [self.addAndReduceView updateViewWithInfo:nil context:[NSString stringWithFormat:@"%ld",[data integerValue]]];
            }else{
                MBShowFailureMessage(msg);
                [self.addAndReduceView updateViewWithInfo:nil context:[NSString stringWithFormat:@"%ld",number]];
            }
        }];
    }
}

- (void)updateCellWithInfo:(NSDictionary *)info context:(id)context
{
    self.productModel = context;
    
    NSMutableAttributedString * attributedStringDesc = [[NSMutableAttributedString alloc] initWithString:self.productModel.spec attributes:@{NSForegroundColorAttributeName : ColorText_9494A6}];
    if (self.productModel.mediumPackageTitle.length) {
        [attributedStringDesc appendAttributedString:[[NSAttributedString alloc] initWithString:@"  "]];
        [attributedStringDesc appendAttributedString:[[NSAttributedString alloc] initWithString:self.productModel.mediumPackageTitle attributes:@{NSForegroundColorAttributeName : ColorText_9494A6 , NSFontAttributeName : [UIFont systemFontOfSize:11.f]}]];
    }
    self.productDescLabel.attributedText = attributedStringDesc;
    self.productPriceLabel.text = [NSString stringWithFormat:@"¥%.2f",[self.productModel.fob floatValue]];
    
//    self.productDescLabel.text = self.productModel.spec;
//    if (self.productModel.mediumPackageTitle.length) {
//        self.mediumPackageNumLabel.hidden = NO;
//        self.mediumPackageNumLabel.text = self.productModel.mediumPackageTitle;
//    }else{
//        self.mediumPackageNumLabel.hidden = YES;
//    }
    
    self.addAndReduceView.productCount = [[[ShareSingleTon ShareSingleTonManager] shoppingNumberData:self.productModel.id] integerValue];
    /*
    if (self.productModel.isSplit) {
        self.addAndReduceView.multipleNumber = 0;
    }else{
        self.addAndReduceView.multipleNumber = self.productModel.mediumPackageNum;
    }
    self.addAndReduceView.isSplit = self.productModel.isSplit;
    */
    self.addAndReduceView.multipleNumber = self.productModel.mediumPackageNum;
    self.addAndReduceView.isSplit = self.productModel.isSplit;
    
    [self.productImageView sd_setImageWithURL:[NSURL URLWithString:[[NSString stringWithFormat:@"%@%@",XYYSmallImagUrl,self.productModel.imageUrl] stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding]] placeholderImage:ImageWithName(@"placeholderImage")];
    if ([self.productModel.status isEqualToString:@"3"]) {
        self.stateImageView.hidden = YES;
    }else if ([self.productModel.status isEqualToString:@"2"]){
        self.stateImageView.hidden = NO;
        self.stateImageView.image = ImageWithName(@"caigoudan_yishouqing_icon");
    }else if ([self.productModel.status isEqualToString:@"4"]){
        self.stateImageView.hidden = NO;
        self.stateImageView.image = ImageWithName(@"caigoudan_yixiajia_icon");
    }else{
        self.stateImageView.hidden = YES;
    }

//    if ([self.productModel.isControl isEqualToString:@"1"]) {
//        if (self.productModel.isPurchase) {
//            //OEM判断
//            if (self.productModel.isOEM) {
//                if (self.productModel.signStatus ==0) {
//                    self.productPriceLabel.text = @"价格签署协议可见";
//
//                }
//            }
//            //是否符合协议标准展示价格
//            if ([self.productModel.showAgree isEqualToString:@"0"]) {
//                 self.productPriceLabel.text = @"价格签署协议可见";
//            }
//            self.canBuyProductLabel.hidden = YES;
//            self.addAndReduceView.hidden = NO;
//
//            self.controlEarnTitleLabel.hidden = NO;
//            self.controlEarnLabel.hidden = NO;
//            self.controlPriceTitleLabel.hidden = NO;
//            self.controlPriceLabel.hidden = NO;
//        }else{
//            self.canBuyProductLabel.hidden = NO;
//            self.addAndReduceView.hidden = YES;
//            self.productPriceLabel.text = @"";
//
//            self.controlEarnTitleLabel.hidden = YES;
//            self.controlEarnLabel.hidden = YES;
//            self.controlPriceTitleLabel.hidden = YES;
//            self.controlPriceLabel.hidden = YES;
//        }
//    }else{
//        //OEM判断
//        if (self.productModel.isOEM) {
//            if (self.productModel.signStatus ==0) {
//                self.productPriceLabel.text = @"价格签署协议可见";
//
//            }
//        }
//        //是否符合协议标准展示价格
//        if ([self.productModel.showAgree isEqualToString:@"0"]) {
//            self.productPriceLabel.text = @"价格签署协议可见";
//        }
//        self.canBuyProductLabel.hidden = YES;
//        self.addAndReduceView.hidden = NO;
//
//        self.controlEarnTitleLabel.hidden = NO;
//        self.controlEarnLabel.hidden = NO;
//        self.controlPriceTitleLabel.hidden = NO;
//        self.controlPriceLabel.hidden = NO;
//    }
    
    // 如果价格不可见
    if (self.productModel.controlTitle.length) {
        self.canBuyProductLabel.hidden = YES;
        self.addAndReduceView.hidden = NO;
        
        self.controlEarnTitleLabel.hidden = NO;
        self.controlEarnLabel.hidden = NO;
        self.controlPriceTitleLabel.hidden = NO;
        self.controlPriceLabel.hidden = NO;
        self.productPriceLabel.text = self.productModel.controlTitle;
    }else{
        self.canBuyProductLabel.hidden = NO;
        self.addAndReduceView.hidden = YES;
        //        self.productPriceLabel.text = @"";
        
        self.controlEarnTitleLabel.hidden = YES;
        self.controlEarnLabel.hidden = YES;
        self.controlPriceTitleLabel.hidden = YES;
        self.controlPriceLabel.hidden = YES;
    }
    
    
    
    if (self.productModel.uniformPrice.length > 0) {
        self.controlPriceLabel.text = [NSString stringWithFormat:@"￥%.2f",[self.productModel.uniformPrice floatValue]];
        self.controlPriceTitleLabel.text = @"控销价";
        self.controlEarnLabel.text = [NSString stringWithFormat:@"%.2f%@",self.productModel.grossMargin==nil?0:[self.productModel.grossMargin floatValue],@"%"];
    }else if (self.productModel.suggestPrice.length > 0){
        BOOL isKA = [XYYUserData ShareUserData].isKa;
        if (isKA) {
            self.controlPriceLabel.text = [NSString stringWithFormat:@"￥%@",self.productModel.suggestPrice];
        } else {
            self.controlPriceLabel.text = [NSString stringWithFormat:@"￥%.2f",[self.productModel.suggestPrice floatValue]];
        }
        self.controlPriceTitleLabel.text = @"零售价";
        self.controlEarnLabel.text = [NSString stringWithFormat:@"%.2f%@",self.productModel.grossMargin==nil?0:[self.productModel.grossMargin floatValue],@"%"];
    }else{
        self.controlEarnTitleLabel.hidden = YES;
        self.controlEarnLabel.hidden = YES;
        self.controlPriceTitleLabel.hidden = YES;
        self.controlPriceLabel.hidden = YES;
    }
    
    // 独家标签
    NSMutableArray *images = [NSMutableArray array];
    if ([self.productModel.agent isEqualToString:@"1"]) {
        [images addObject:[UIImage imageNamed:@"ic_tag_dujia"]];
        if (self.productModel.IsUsableMedicalStr == 1) {
            [images addObject:[UIImage imageNamed:@"ic_tag_yibao"]];
        }
    } else if (self.productModel.IsUsableMedicalStr == 1) {
        [images removeAllObjects];
        [images addObject:[UIImage imageNamed:@"ic_tag_yibao"]];
    }
    
    [self.productNameLabel setText:self.productModel.commonName frontImages:images imageSpan:2];
    
    if (self.productModel.tagList.count == 0) {
        self.tagsLabelOne.hidden = YES;
        self.tagsLabelTwo.hidden = YES;
        self.tagsLabelThree.hidden = YES;
        
    }else if (self.productModel.tagList.count == 1){
        self.tagsLabelOne.hidden = NO;
        self.tagsLabelTwo.hidden = YES;
        self.tagsLabelThree.hidden = YES;
        
        YBMProductTagModel * oneModel = self.productModel.tagList[0];
        [self.tagsLabelOne tagsStyleWithType:oneModel.uiType.stringValue];
        self.tagsLabelOne.text = oneModel.name;
        
    }else if (self.productModel.tagList.count == 2){
        self.tagsLabelOne.hidden = NO;
        self.tagsLabelTwo.hidden = NO;
        self.tagsLabelThree.hidden = YES;
        
        YBMProductTagModel * oneModel = self.productModel.tagList[0];
        [self.tagsLabelOne tagsStyleWithType:oneModel.uiType.stringValue];
        self.tagsLabelOne.text = oneModel.name;
        
        YBMProductTagModel * twoModel = self.productModel.tagList[1];
        [self.tagsLabelTwo tagsStyleWithType:twoModel.uiType.stringValue];
        self.tagsLabelTwo.text = twoModel.name;
        
    }else{
        self.tagsLabelOne.hidden = NO;
        self.tagsLabelTwo.hidden = NO;
        self.tagsLabelThree.hidden = NO;
        
        YBMProductTagModel * oneModel = self.productModel.tagList[0];
        [self.tagsLabelOne tagsStyleWithType:oneModel.uiType.stringValue];
        self.tagsLabelOne.text = oneModel.name;
        
        YBMProductTagModel * twoModel = self.productModel.tagList[1];
        [self.tagsLabelTwo tagsStyleWithType:twoModel.uiType.stringValue];
        self.tagsLabelTwo.text = twoModel.name;
        
        YBMProductTagModel * threeModel = self.productModel.tagList[2];
        [self.tagsLabelThree tagsStyleWithType:threeModel.uiType.stringValue];
        self.tagsLabelThree.text = threeModel.name;
    }
    
    if (self.productModel.markerUrl.length) {
        self.iconImageView.hidden = NO;
        [self.iconImageView sd_setImageWithURL:[NSURL URLWithString:[[NSString stringWithFormat:@"%@%@",XYYManageImg,self.productModel.markerUrl] stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding]]];
    }else{
        self.iconImageView.hidden = YES;
    }

}

@end
