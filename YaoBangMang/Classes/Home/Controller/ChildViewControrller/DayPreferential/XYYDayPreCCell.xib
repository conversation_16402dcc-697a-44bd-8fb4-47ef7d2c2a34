<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="XYYDayPreCCell" id="gTV-IL-0wX" customClass="XYYDayPreCCell">
            <rect key="frame" x="0.0" y="0.0" width="125" height="180"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="125" height="180"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vuA-o3-ibi">
                        <rect key="frame" x="8" y="7" width="112" height="112"/>
                        <subviews>
                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="placeholderImage.png" translatesAutoresizingMaskIntoConstraints="NO" id="FSk-xP-LOS">
                                <rect key="frame" x="5" y="5" width="102" height="102"/>
                            </imageView>
                            <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="RZf-ZE-EOS">
                                <rect key="frame" x="32.5" y="32.5" width="47" height="47"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="47" id="4Kf-Pu-A2A"/>
                                    <constraint firstAttribute="width" constant="47" id="ViX-KO-f8E"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="FSk-xP-LOS" secondAttribute="trailing" constant="5" id="3RO-PG-5Iy"/>
                            <constraint firstAttribute="bottom" secondItem="FSk-xP-LOS" secondAttribute="bottom" constant="5" id="5AK-aQ-ZCZ"/>
                            <constraint firstAttribute="width" secondItem="vuA-o3-ibi" secondAttribute="height" multiplier="1:1" id="BoE-cI-EdP"/>
                            <constraint firstItem="RZf-ZE-EOS" firstAttribute="centerY" secondItem="FSk-xP-LOS" secondAttribute="centerY" id="VE0-1o-iZS"/>
                            <constraint firstItem="FSk-xP-LOS" firstAttribute="leading" secondItem="vuA-o3-ibi" secondAttribute="leading" constant="5" id="WUY-30-0xO"/>
                            <constraint firstItem="RZf-ZE-EOS" firstAttribute="centerX" secondItem="FSk-xP-LOS" secondAttribute="centerX" id="vHg-qP-Z4U"/>
                            <constraint firstItem="FSk-xP-LOS" firstAttribute="top" secondItem="vuA-o3-ibi" secondAttribute="top" constant="5" id="vYl-V9-FFf"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="ibCornerRadius">
                                <real key="value" value="5"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="color" keyPath="ibBorderColor">
                                <color key="value" red="0.93333333333333335" green="0.93333333333333335" blue="0.93333333333333335" alpha="1" colorSpace="calibratedRGB"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="ibBorderWidth">
                                <real key="value" value="0.5"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="感冒药" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ql6-BK-TrD">
                        <rect key="frame" x="10" y="123" width="103" height="17"/>
                        <fontDescription key="fontDescription" name="PingFangSC-Medium" family="PingFang SC" pointSize="12"/>
                        <color key="textColor" red="0.16078431369999999" green="0.16078431369999999" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="ibBorderWidth">
                                <real key="value" value="0.0"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="1000" text="￥19.00" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ghf-VU-Vn6">
                        <rect key="frame" x="10" y="140" width="103" height="20"/>
                        <fontDescription key="fontDescription" name="PingFangSC-Regular" family="PingFang SC" pointSize="14"/>
                        <color key="textColor" red="1" green="0.12941176469999999" blue="0.12941176469999999" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="home_collage" translatesAutoresizingMaskIntoConstraints="NO" id="zpm-iU-Dcb">
                        <rect key="frame" x="10" y="157" width="110" height="18"/>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4cK-wN-NhS">
                        <rect key="frame" x="52" y="157" width="68" height="18"/>
                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                        <color key="textColor" red="1" green="0.12941176470588234" blue="0.12941176470588234" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DhI-1O-gsB">
                        <rect key="frame" x="10" y="157" width="42" height="18"/>
                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="4毫克*7片" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="YgT-Zs-pCy">
                        <rect key="frame" x="10" y="158" width="110" height="17"/>
                        <fontDescription key="fontDescription" name="PingFangSC-Regular" family="PingFang SC" pointSize="12"/>
                        <color key="textColor" red="0.58039215690000001" green="0.58039215690000001" blue="0.65098039220000004" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="odz-3H-EyH">
                        <rect key="frame" x="8" y="7" width="112" height="112"/>
                    </imageView>
                </subviews>
            </view>
            <color key="backgroundColor" red="1" green="0.99997437000274658" blue="0.99999129772186279" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="ghf-VU-Vn6" secondAttribute="trailing" constant="12" id="014-PE-JSG"/>
                <constraint firstItem="YgT-Zs-pCy" firstAttribute="leading" secondItem="zpm-iU-Dcb" secondAttribute="leading" id="4Ex-qn-jgp"/>
                <constraint firstItem="zpm-iU-Dcb" firstAttribute="leading" secondItem="DhI-1O-gsB" secondAttribute="leading" id="AgH-qD-esi"/>
                <constraint firstItem="zpm-iU-Dcb" firstAttribute="bottom" secondItem="4cK-wN-NhS" secondAttribute="bottom" id="EBi-xP-MuU"/>
                <constraint firstItem="4cK-wN-NhS" firstAttribute="leading" secondItem="DhI-1O-gsB" secondAttribute="trailing" id="HUl-Fa-zRH"/>
                <constraint firstItem="odz-3H-EyH" firstAttribute="top" secondItem="vuA-o3-ibi" secondAttribute="top" id="I2M-XU-tYI"/>
                <constraint firstItem="zpm-iU-Dcb" firstAttribute="top" secondItem="DhI-1O-gsB" secondAttribute="top" id="KVQ-eR-1Iz"/>
                <constraint firstItem="YgT-Zs-pCy" firstAttribute="bottom" secondItem="zpm-iU-Dcb" secondAttribute="bottom" id="NuV-66-YtM"/>
                <constraint firstItem="ql6-BK-TrD" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="10" id="ORl-XZ-fT8"/>
                <constraint firstItem="YgT-Zs-pCy" firstAttribute="trailing" secondItem="zpm-iU-Dcb" secondAttribute="trailing" id="PVR-mc-Ib6"/>
                <constraint firstItem="vuA-o3-ibi" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="8" id="UU9-D0-NgO"/>
                <constraint firstItem="odz-3H-EyH" firstAttribute="bottom" secondItem="vuA-o3-ibi" secondAttribute="bottom" id="YpF-94-3i5"/>
                <constraint firstItem="DhI-1O-gsB" firstAttribute="width" secondItem="zpm-iU-Dcb" secondAttribute="width" multiplier="0.377273" constant="0.34999999999999998" id="Z06-WU-mCC"/>
                <constraint firstAttribute="bottom" secondItem="YgT-Zs-pCy" secondAttribute="bottom" constant="5" id="cMP-oI-qNO"/>
                <constraint firstAttribute="trailing" secondItem="YgT-Zs-pCy" secondAttribute="trailing" constant="5" id="dDe-mO-s8b"/>
                <constraint firstItem="ghf-VU-Vn6" firstAttribute="top" secondItem="ql6-BK-TrD" secondAttribute="bottom" id="dl7-68-2Y9"/>
                <constraint firstAttribute="trailing" secondItem="vuA-o3-ibi" secondAttribute="trailing" constant="5" id="dyF-zD-FH7"/>
                <constraint firstItem="YgT-Zs-pCy" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="10" id="ga5-2b-leJ"/>
                <constraint firstItem="zpm-iU-Dcb" firstAttribute="bottom" secondItem="DhI-1O-gsB" secondAttribute="bottom" id="hao-ix-dK6"/>
                <constraint firstItem="odz-3H-EyH" firstAttribute="leading" secondItem="vuA-o3-ibi" secondAttribute="leading" id="iXn-bS-gaT"/>
                <constraint firstItem="ghf-VU-Vn6" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="10" id="mHO-mX-x5H"/>
                <constraint firstItem="zpm-iU-Dcb" firstAttribute="trailing" secondItem="4cK-wN-NhS" secondAttribute="trailing" id="oFR-dZ-dL5"/>
                <constraint firstItem="zpm-iU-Dcb" firstAttribute="top" secondItem="4cK-wN-NhS" secondAttribute="top" id="peP-fd-eby"/>
                <constraint firstAttribute="trailing" secondItem="ql6-BK-TrD" secondAttribute="trailing" constant="12" id="xig-O1-bjO"/>
                <constraint firstItem="ql6-BK-TrD" firstAttribute="top" secondItem="vuA-o3-ibi" secondAttribute="bottom" constant="4" id="yA4-Xr-asO"/>
                <constraint firstItem="odz-3H-EyH" firstAttribute="trailing" secondItem="vuA-o3-ibi" secondAttribute="trailing" id="z1u-yv-z1L"/>
                <constraint firstItem="vuA-o3-ibi" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="7" id="zG4-bV-MgH"/>
            </constraints>
            <size key="customSize" width="146" height="142"/>
            <connections>
                <outlet property="assembleImage" destination="zpm-iU-Dcb" id="qZi-pO-ARM"/>
                <outlet property="assembleLeftLab" destination="DhI-1O-gsB" id="7zg-vT-SPc"/>
                <outlet property="assembleRightLab" destination="4cK-wN-NhS" id="6m2-6w-erx"/>
                <outlet property="backview" destination="vuA-o3-ibi" id="1aC-p4-Gpk"/>
                <outlet property="imageMark" destination="odz-3H-EyH" id="nrz-2T-s0R"/>
                <outlet property="imageProduct" destination="FSk-xP-LOS" id="wLH-90-CJk"/>
                <outlet property="imageStatus" destination="RZf-ZE-EOS" id="e2G-xF-Dm8"/>
                <outlet property="labName" destination="ql6-BK-TrD" id="7tf-eW-nlc"/>
                <outlet property="labPrice" destination="ghf-VU-Vn6" id="uCr-zk-rRB"/>
                <outlet property="labSize" destination="YgT-Zs-pCy" id="G3M-5t-syX"/>
            </connections>
            <point key="canvasLocation" x="562.39999999999998" y="228.48575712143929"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="home_collage" width="84" height="17.5"/>
        <image name="placeholderImage.png" width="360" height="360"/>
    </resources>
</document>
