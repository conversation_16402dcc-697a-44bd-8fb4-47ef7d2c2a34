//
//  XYYDayPreCCell.m
//  YaoBangMang
//
//  Created by <PERSON><PERSON><PERSON> on 16/11/30.
//  Copyright © 2016年 XiaoYaoYao.Ltd. All rights reserved.
//

#import "XYYDayPreCCell.h"
#import "XYYDayPProductM.h"
#import "XYYPriceRangM.h"
#import "YBMProductModel.h"


@interface XYYDayPreCCell ()

@property (weak, nonatomic) IBOutlet UILabel *assembleRightLab;
@property (weak, nonatomic) IBOutlet UIImageView *assembleImage;
@property (weak, nonatomic) IBOutlet UILabel *assembleLeftLab;

@end

@implementation XYYDayPreCCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.backview.layer.cornerRadius = 5;
    self.backview.layer.borderWidth = 0.5;
    self.backview.layer.borderColor = ColorBack_EEEEEE.CGColor;
    self.assembleLeftLab.font = [UIFont fontWithName:FontTypePingFangMe size:8];
    self.assembleLeftLab.textColor = [UIColor whiteColor];
    // Initialization code
}

#pragma mark - 赋值
- (void)setModel:(XYYDayPProductM *)model{
    _model = model;
    // icon
    NSURL *url = [NSURL URLWithString:[XYYSmallImagUrl stringByAppendingString:[(model.imageUrl?:@"") stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding]]];
    [self.imageProduct sd_setImageWithURL:url placeholderImage:[UIImage imageNamed:@"placeholderImage"]];
    // 商品名称
    self.labName.text = model.commonName;
    // 商品价格 labPrice
    self.labSize.text = [NSString stringWithFormat:@"￥%.2f",[model.fob doubleValue]];
    self.labSize.font = [UIFont fontWithName:FontTypePingFangMe size:14];
    self.labSize.textColor = ColorText_FF2121;
    
    if (model.levelPriceDTO) {
        self.labSize.text = model.levelPriceDTO.startingPriceShowText;
    }
    //区间价格-fob
    else if (model.priceType == 2 && model.productPriceRanges.count >0) {
        NSString *priceOne = model.productPriceRanges.firstObject.price;
        NSString *priceTwo = model.productPriceRanges.lastObject.price;
        double fpriceOne = [priceOne doubleValue];
        double fpriceTwo = [priceTwo doubleValue];
        if (fpriceOne >fpriceTwo) {
            self.labSize.text = [NSString stringWithFormat:@"￥%.2f-%.2f",fpriceTwo,fpriceOne];
        }
        else {
            self.labSize.text = [NSString stringWithFormat:@"￥%.2f-%.2f",fpriceOne,fpriceTwo];
        }
    }
    else {
        self.labSize.text =  [NSString stringWithFormat:@"￥%.2f",[model.fob doubleValue]];
    }
    
    // 描述
    self.labPrice.text = model.spec;
    self.labPrice.textColor = ColorText_9494A6;
    self.labPrice.font = [UIFont fontWithName:FontTypePingFangRe size:11];
    
    
    if ([model.status isEqualToString:@"3"]) {
        self.imageStatus.hidden = YES;
    }
    else if ([model.status isEqualToString:@"2"]){
        self.imageStatus.hidden = NO;
        self.imageStatus.image = [UIImage imageNamed:@"caigoudan_yishouqing_icon"];
    }
    else if ([model.status isEqualToString:@"4"]){
        self.imageStatus.hidden = NO;
        self.imageStatus.image = [UIImage imageNamed:@"caigoudan_yixiajia_icon" ];
    }
    else {
        self.imageStatus.hidden = YES;
    }
    if ([model.availableqty integerValue] == 0) {
        self.imageStatus.hidden = NO;
        self.imageStatus.image = [UIImage imageNamed:@"caigoudan_yishouqing_icon"];
    }
    
    
    if(model.markerUrl!=nil){
        NSString * makeUrl = [[XYYManageImg stringByAppendingString:model.markerUrl] stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
        self.imageMark.image = nil;
        if (![model.markerUrl isKindOfClass:[NSNull class]] &&model.markerUrl.length>5) {
            [self.imageMark sd_setImageWithURL:[NSURL URLWithString:makeUrl] completed:^(UIImage *image, NSError *error, SDImageCacheType cacheType, NSURL *imageURL) {
                
            }];
        }
        
    }
//    if (model.isOEM) {
//        if (model.signStatus == 0) {
//            self.labSize.text = @"价格签署协议可见";
//            self.labSize.font = [UIFont systemFontOfSize:12];
//        } else {
//            self.labSize.font = [UIFont systemFontOfSize:14];
//        }
//    } else {
//        self.labSize.font = [UIFont systemFontOfSize:14];
//    }
//    //是否符合协议标准展示价格
//    if ([model.showAgree isEqualToString:@"0"]) {
//        self.labSize.text = @"价格签署协议可见";
//        self.labSize.font = [UIFont systemFontOfSize:12];
//    }
//    if (([XYYUserData ShareUserData].licenseStatus.integerValue == 1) || ([XYYUserData ShareUserData].licenseStatus.integerValue == 5)) {
//        [YBMAuthenticateManager setLabelStyle:self.labSize withFont:14];
//    }
    
    // 如果价格不可见
    if (model.controlTitle.length) {
        self.labSize.font = [UIFont systemFontOfSize:12];
        self.labSize.text = model.controlTitle;
    }
    if(model.actPgby || model.actPt){
        NSString *assemblePrice = nil;
        if(model.actPgby){
            self.assembleLeftLab.text = @"包邮价";
            assemblePrice = [NSString stringWithFormat:@"¥%.2f",model.actPgby.assemblePrice.doubleValue];
        }else{
            self.assembleLeftLab.text = @"拼团价";
            if (model.actPt.minSkuPrice.floatValue > 0) {//阶梯价格
                assemblePrice = [NSString stringWithFormat:@"¥%.2f起",model.actPt.minSkuPrice.floatValue];
            }else{
                assemblePrice = [NSString stringWithFormat:@"¥%.2f",model.actPt.assemblePrice.doubleValue];
            }
        }
        UIFont *la = [UIFont boldSystemFontOfSize:14];
        UIFont *sm = [UIFont boldSystemFontOfSize:10];
        NSMutableAttributedString *atrString = [Tools formatPriceStr:assemblePrice largeFont:la smallFont:sm];
        self.assembleRightLab.attributedText = atrString;
        
        self.assembleImage.hidden = NO;
        self.assembleLeftLab.hidden = NO;
        self.assembleRightLab.hidden = NO;
        self.labSize.hidden = YES;
    }else{
        self.assembleImage.hidden = YES;
        self.assembleLeftLab.hidden = YES;
        self.assembleRightLab.hidden = YES;
        self.labSize.hidden = NO;
    }
}

- (void)setRecModel:(YBMProductModel *)recModel {
    _recModel = recModel;
    // icon
    NSURL *url = [NSURL URLWithString:[XYYSmallImagUrl stringByAppendingString:[(recModel.imageUrl?:@"") stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding]]];
    [self.imageProduct sd_setImageWithURL:url placeholderImage:[UIImage imageNamed:@"placeholderImage"]];
    // 商品名称
    self.labName.text = recModel.commonName;
    // 商品价格 labPrice
    self.labSize.text = [NSString stringWithFormat:@"￥%.2f",[recModel.fob doubleValue]];
    self.labSize.font = [UIFont fontWithName:FontTypePingFangMe size:14];
    self.labSize.textColor = ColorText_FF2121;
    
    if (recModel.levelPriceDTO) {
        self.labSize.text = recModel.levelPriceDTO.startingPriceShowText;
    }
    //区间价格-fob
    else if (recModel.priceType.integerValue == 2 && recModel.skuPriceRangeList.count >0) {
        NSString *priceOne = recModel.skuPriceRangeList.firstObject.price;
        NSString *priceTwo = recModel.skuPriceRangeList.lastObject.price;
        double fpriceOne = [priceOne doubleValue];
        double fpriceTwo = [priceTwo doubleValue];
        if (fpriceOne >fpriceTwo) {
            self.labSize.text = [NSString stringWithFormat:@"￥%.2f-%.2f",fpriceTwo,fpriceOne];
        }
        else {
            self.labSize.text = [NSString stringWithFormat:@"￥%.2f-%.2f",fpriceOne,fpriceTwo];
        }
    }
    else {
        self.labSize.text =  [NSString stringWithFormat:@"￥%.2f",[recModel.fob doubleValue]];
    }
    
    // 描述
    self.labPrice.text = recModel.spec;
    self.labPrice.textColor = ColorText_9494A6;
    self.labPrice.font = [UIFont fontWithName:FontTypePingFangRe size:11];
    
    
    if (recModel.status.integerValue == 3) {
        self.imageStatus.hidden = YES;
    }
    else if (recModel.status.integerValue == 2){
        self.imageStatus.hidden = NO;
        self.imageStatus.image = [UIImage imageNamed:@"caigoudan_yishouqing_icon"];
    }
    else if (recModel.status.integerValue == 4){
        self.imageStatus.hidden = NO;
        self.imageStatus.image = [UIImage imageNamed:@"caigoudan_yixiajia_icon" ];
    }
    else {
        self.imageStatus.hidden = YES;
    }
    if (recModel.availableQty.integerValue == 0) {
        self.imageStatus.hidden = NO;
        self.imageStatus.image = [UIImage imageNamed:@"caigoudan_yishouqing_icon"];
    }

    if (recModel.isOEM) {
        if (recModel.signStatus.integerValue == 0) {
            self.labSize.text = @"价格签署协议可见";
            self.labSize.font = [UIFont systemFontOfSize:12];
        } else {
            self.labSize.font = [UIFont systemFontOfSize:14];
        }
    } else {
        self.labSize.font = [UIFont systemFontOfSize:14];
    }
    //是否符合协议标准展示价格
    if (recModel.showAgree.integerValue == 0) {
        self.labSize.text = @"价格签署协议可见";
        self.labSize.font = [UIFont systemFontOfSize:12];
    }
    if (([XYYUserData ShareUserData].licenseStatus.integerValue == 1) || ([XYYUserData ShareUserData].licenseStatus.integerValue == 5)) {
        [YBMAuthenticateManager setLabelStyle:self.labSize withFont:14];
    }
    
    if (recModel.isControl.integerValue == 1 && !recModel.isPurchase) {
        self.labSize.text = @"暂无购买权限";
    }
    
    if(recModel.actPgby || recModel.actPt){
        NSString *assemblePrice = nil;
        if(recModel.actPgby){
            self.assembleLeftLab.text = @"包邮价";
            assemblePrice = [NSString stringWithFormat:@"¥%.2f",recModel.actPgby.assemblePrice.doubleValue];
        }else{
            self.assembleLeftLab.text = @"拼团价";
            if (recModel.actPt.minSkuPrice.floatValue > 0) {//阶梯价格
                assemblePrice = [NSString stringWithFormat:@"¥%.2f起",recModel.actPt.minSkuPrice.floatValue];
            }else{
                assemblePrice = [NSString stringWithFormat:@"¥%.2f",recModel.actPt.assemblePrice.doubleValue];
            }
        }
        UIFont *la = [UIFont boldSystemFontOfSize:14];
        UIFont *sm = [UIFont boldSystemFontOfSize:10];
        NSMutableAttributedString *atrString = [Tools formatPriceStr:assemblePrice largeFont:la smallFont:sm];
        self.assembleRightLab.attributedText = atrString;
        
        self.assembleImage.hidden = NO;
        self.assembleLeftLab.hidden = NO;
        self.assembleRightLab.hidden = NO;
        self.labSize.hidden = YES;
    }else{
        self.assembleImage.hidden = YES;
        self.assembleLeftLab.hidden = YES;
        self.assembleRightLab.hidden = YES;
        self.labSize.hidden = NO;
    }
}

@end
