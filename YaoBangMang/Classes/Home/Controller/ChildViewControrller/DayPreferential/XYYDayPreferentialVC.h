//
//  XYYDayPreferentialVC.h
//  YaoBangMang
//
//  Created by <PERSON><PERSON><PERSON> on 16/11/30.
//  Copyright © 2016年 XiaoYaoYao.Ltd. All rights reserved.
//

#import "XYYBaseVC.h"

@class XYYXDayPProductVM;
@class XYYTimeCountLab;
@interface XYYDayPreferentialVC : XYYBaseVC <UICollectionViewDelegate,UICollectionViewDataSource,UICollectionViewDelegateFlowLayout>

@property (nonatomic ,strong) UICollectionView *collectionView;
@property (nonatomic ,assign) CGFloat HeadSizeH;    //头部图片高度
@property (nonatomic ,assign) CGFloat SectionSizeH; //section高度

@property (nonatomic ,strong) XYYXDayPProductVM *model0;
@property (nonatomic ,strong) XYYXDayPProductVM *model1;
@property (nonatomic ,strong) XYYXDayPProductVM *model2;

@property (nonatomic ,strong) XYYTimeCountLab *lab0;
@property (nonatomic ,strong) UIImageView *head0View;

@end
