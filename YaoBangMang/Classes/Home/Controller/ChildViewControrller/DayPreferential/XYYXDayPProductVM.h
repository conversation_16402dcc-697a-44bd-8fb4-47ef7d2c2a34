//
//  XYYXDayPProductVM.h
//  YaoBangMang
//
//  Created by <PERSON><PERSON><PERSON> on 16/12/1.
//  Copyright © 2016年 XiaoYaoYao.Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>

@class XYYDayPProductM;
@interface XYYXDayPProductVM : NSObject

@property (nonatomic , strong) NSMutableArray <XYYDayPProductM *>  *arrProduct;
@property (nonatomic ,copy) NSString *startTime;
@property (nonatomic ,copy) NSString *endTime;
@property (nonatomic ,copy) NSString *endTimestamp;

+ (instancetype)modelWithData:(NSDictionary *)data;

@end
