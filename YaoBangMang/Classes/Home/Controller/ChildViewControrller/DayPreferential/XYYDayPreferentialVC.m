//
//  XYYDayPreferentialVC.m
//  YaoBangMang
//
//  Created by <PERSON><PERSON><PERSON> on 16/11/30.
//  Copyright © 2016年 XiaoYaoYao.Ltd. All rights reserved.
//

#import "XYYDayPreferentialVC.h"
#import "Masonry.h"
#import "XYYDayPreCCell.h"
#import "UIColor+Convert.h"
#import "InterfaceManager.h"
#import "XYYXDayPProductVM.h"
#import "XYYDayPProductM.h"
#import "XYYTimeCountLab.h"
#import "MBProgressHUD.h"
#import "UIButton+Badge.h"
#import "YBMShoppingCartAdapter.h"


@interface XYYDayPreferentialVC ()

@property (nonatomic, strong) UIButton *badgeButton;

@end

@implementation XYYDayPreferentialVC

#pragma mark - life cyc

-  (void)viewDidLoad
{
    [super viewDidLoad];
    [self initUI];
    [self setUpCommodityTypeNumberData];
    
}
- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
}
- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
}
- (void)initData
{
    WEAKSELF
    [InterfaceManager getDayPreferentialDataOffset:@"0" Completion:^(BOOL isSuccess, NSString *msg, id data) {
        if (isSuccess) {
            weakSelf.model0 = data;
        }
         [weakSelf.collectionView reloadData];
    }];
    [InterfaceManager getDayPreferentialDataOffset:@"1" Completion:^(BOOL isSuccess, NSString *msg, id data) {
        if (isSuccess) {
            weakSelf.model1 = data;
        }
        [weakSelf.collectionView reloadData];
    }];
    [InterfaceManager getDayPreferentialDataOffset:@"2" Completion:^(BOOL isSuccess, NSString *msg, id data) {
        if (isSuccess) {
            [weakSelf.collectionView.mj_header endRefreshing];
            weakSelf.model2 = data;
        }
        [weakSelf.collectionView reloadData];
    }];
}

#pragma mark - 请求购物车数量
- (void)setUpCommodityTypeNumberData {
    WEAKSELF
    [InterfaceManager getShoppingCartCountWithCompletion:^(BOOL isSuccess, NSString *msg, id data) {
        if (isSuccess) {
            NSUInteger shoppingCartCount = [data[@"num"] integerValue];
            [[NSNotificationCenter defaultCenter] postNotificationName:@"setUpShoppingNumber" object:[data[@"num"] stringValue]];
            if (shoppingCartCount <= 99) {
                [weakSelf.badgeButton setBadgeValue:[NSString stringWithFormat:@"%ld",shoppingCartCount]];
            }else{
                [weakSelf.badgeButton setBadgeValue:@"99+"];
            }
        }
    }];
    
}


- (void)initUI
{
    self.HeadSizeH = (XYYScreenW *(171.0/375.0));
    self.SectionSizeH = (XYYScreenW *(68.0/(375.0-20.0)));
    self.title = @"每日特惠";
    // 设置导航条返回按钮
    self.navigationItem.leftBarButtonItem = [UIBarButtonItem itemWithImage:[UIImage imageNamed:@"nav_back_gray"] highImage:[UIImage imageNamed:@"nav_back_gray"] targer:self action:@selector(clickReturnButton)];
    self.view.backgroundColor = [UIColor whiteColor];
    UICollectionViewFlowLayout *datelayout = [[UICollectionViewFlowLayout alloc] init];
    datelayout.itemSize = CGSizeMake((XYYScreenW - 12.0)/3, XYYScreenW *0.48);
    self.collectionView = [[UICollectionView alloc] initWithFrame:CGRectMake(0, 0, 0, 0) collectionViewLayout:datelayout];
    [self.collectionView registerNib:[UINib nibWithNibName:@"XYYDayPreCCell" bundle:nil] forCellWithReuseIdentifier:@"XYYDayPreCCell"];
    [self.collectionView registerClass:[UICollectionReusableView class] forSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:@"Bimagesection"];
    [self.collectionView registerClass:[UICollectionReusableView class] forSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:@"Simagesection"];
    self.collectionView.backgroundColor = [UIColor colorByByteWithRed:252 green:209 blue:35 andFloatAlpha:1];
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;
    [self.view addSubview:self.collectionView];
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    WEAKSELF
    self.collectionView.mj_header = [YBMRefreshHeader headerWithRefreshingBlock:^{
        [weakSelf initData];
    }];
    [weakSelf.collectionView.mj_header beginRefreshing];
    
    // 设置导航条采购单按钮
    UIImage *image = [UIImage imageNamed:@"caigoudannew"];
    
    self.badgeButton = [UIButton buttonWithType:(UIButtonTypeCustom)];
    
    self.badgeButton.frame = CGRectMake(0, 0, 60, 40);
    
    [self.badgeButton setImage:image forState:(UIControlStateNormal)];
    
    self.badgeButton.badgeBGColor = ColorText_FF2121;
    
    self.badgeButton.badgeTextColor = [UIColor whiteColor];
    
    [self.badgeButton addTarget:self action:@selector(clickButtonPlanView) forControlEvents:(UIControlEventTouchUpInside)];
    
    UIBarButtonItem *navButton = [[UIBarButtonItem alloc] initWithCustomView:self.badgeButton];
    
    self.navigationItem.rightBarButtonItem = navButton;
}

#pragma mark - 跳转采购单
- (void)clickButtonPlanView {
    UIViewController *vc = YBMShoppingCartAdapter.getShoppingCartInstantiationVC;
    [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark - UICollectionViewDelegate,UICollectionViewDataSource

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView
{
    return 3;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section
{
    switch (section) {
        case 0:
            return [self.model0.arrProduct count];
            break;
        case 1:
            return  [self.model1.arrProduct count];
            break;
        case 2:
            return [self.model2.arrProduct count];
            break;
        default:
            return 0;
            break;
    }
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath
{
    XYYDayPreCCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"XYYDayPreCCell" forIndexPath:indexPath];
    switch (indexPath.section) {
        case 0:
        {
            cell.model = self.model0.arrProduct[indexPath.row];
        }
            break;
        case 1:
        {
            cell.model = self.model1.arrProduct[indexPath.row];
        }
            break;
        case 2:
        {
            cell.model = self.model2.arrProduct[indexPath.row];
        }
            break;
        default:
            break;
    }
    return cell;
}

- (UICollectionReusableView *)collectionView:(UICollectionView *)collectionView viewForSupplementaryElementOfKind:(NSString *)kind atIndexPath:(NSIndexPath *)indexPath
{ 
    UICollectionReusableView *reusableView = nil;
    WEAKSELF
    if (kind == UICollectionElementKindSectionHeader){
        if (indexPath.section == 0) {
            UICollectionReusableView *headerView = [collectionView dequeueReusableSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:@"Bimagesection" forIndexPath:indexPath];
            if ([headerView viewWithTag:100] == nil) {
                UIImageView *imageview = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"head"]];
                [headerView addSubview:imageview];
                imageview.tag = 100;
                [imageview mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.top.and.left.and.right.equalTo(headerView);
                    make.height.equalTo(@(weakSelf.HeadSizeH));
                }];
                UIView *view = [[UIView alloc] init];
                view.backgroundColor = [UIColor clearColor];
                [headerView addSubview:view];
                [view mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(imageview.mas_bottom);
                    make.left.and.right.equalTo(headerView);
                    make.height.equalTo(@(weakSelf.SectionSizeH));
                }];
                UIImageView *section0image = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"section0"]];
                self.head0View = section0image;
                [view addSubview:section0image];
                [section0image mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.left.equalTo(view.mas_left).offset(10);
                    make.right.equalTo(view.mas_right).offset(-10);
                    make.top.equalTo(view.mas_top).offset(6);
                    make.bottom.equalTo(view.mas_bottom).offset(-6);
                }];
                if ([self.model0.arrProduct count] == 0) {
                    section0image.hidden = YES;
                }
                XYYTimeCountLab *lab = [[XYYTimeCountLab alloc] initWithFont:[UIFont systemFontOfSize:12] TextColor:[UIColor whiteColor] Alignment:NSTextAlignmentCenter CountTime:[self.model0.endTimestamp integerValue]/1000];
                self.lab0 = lab;
                [section0image addSubview:lab];
                [lab mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.bottom.equalTo(section0image.mas_bottom).offset([self offsetByDevice]);
                    make.centerX.equalTo(section0image.mas_centerX);
                }];
                if ([lab count] >0) {
                    [lab startCount];
                }
            }
            else {
                self.lab0.count = [self.model0.endTimestamp integerValue]/1000;
                if (self.lab0.count >0) {
                    [self.lab0 startCount];
                }
                if ([self.model0.arrProduct count] >0) {
                    self.head0View.hidden = NO;
                }
                else self.head0View.hidden = YES;
            }
            reusableView = headerView;
        }
        if (indexPath.section == 1 || indexPath.section == 2) {
            UICollectionReusableView *headerView = [collectionView dequeueReusableSupplementaryViewOfKind:UICollectionElementKindSectionHeader withReuseIdentifier:@"Simagesection" forIndexPath:indexPath];
            if ([headerView viewWithTag:100] == nil) {
                UIView *sectionView = [self sectionView:indexPath.section];
                [headerView addSubview:sectionView];
                [sectionView mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.edges.equalTo(headerView);
                }];
            }
            else {
                UIImageView *iamgeview = [[headerView viewWithTag:100] viewWithTag:100];
                iamgeview.image = [UIImage imageNamed:indexPath.section ==1? @"section1":@"section2"];
                NSInteger end = indexPath.section ==1?[self.model1.endTimestamp integerValue]/1000:[self.model2.endTimestamp integerValue]/1000;
                XYYTimeCountLab *lab = [iamgeview viewWithTag:1000];
                if (indexPath.section == 1 && self.model1.endTimestamp == nil) {
                    lab.text = @"无特惠商品";
                    lab.count = 0;
                }
                if (indexPath.section == 2 && self.model2.endTimestamp == nil) {
                    lab.text = @"无特惠商品";
                    lab.count = 0;
                }
                lab.count = end;
                if (end>0) {
                    [lab startCount];
                }
            }
            reusableView = headerView;
        }
        
    }
    
    return reusableView;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout referenceSizeForHeaderInSection:(NSInteger)section
{
    if (section == 0) {
        return [self.model0.arrProduct count] == 0?CGSizeMake(XYYScreenW, self.HeadSizeH): CGSizeMake(XYYScreenW, self.HeadSizeH +self.SectionSizeH);
    }
    else if (section == 1){
        return [self.model1.arrProduct count] == 0?CGSizeMake(0, 0):CGSizeMake(XYYScreenW, self.SectionSizeH);
    }
    else {
        return [self.model2.arrProduct count] == 0?CGSizeMake(0, 0):CGSizeMake(XYYScreenW, self.SectionSizeH);
    }
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section
{
    return 3;
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section
{
    return 3;
}
- (UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout insetForSectionAtIndex:(NSInteger)section
{
    return UIEdgeInsetsMake(3, 3, 3,3);
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath
{
    XYYDayPreCCell *cell = (XYYDayPreCCell *)[collectionView cellForItemAtIndexPath:indexPath];
//    if ([cell.model.status isEqualToString:@"2"]) {//售罄
//        MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:[UIApplication sharedApplication].keyWindow animated:YES];
//        hud.mode = MBProgressHUDModeText;
//        hud.labelText = @"已售罄";
//        hud.opacity = 0.5f;
//        [self.view addSubview:hud];
//        [hud hide:YES afterDelay:1.6];
//        return;
//    }
    if ([cell.model.status isEqualToString:@"4"]) {//已下架
        MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:XYYWindow animated:YES];
        hud.mode = MBProgressHUDModeText;
        hud.labelText = @"已下架";
        hud.opacity = 0.5f;
        [self.view addSubview:hud];
        [hud hide:YES afterDelay:1.6];
        return;
    }
    NSString *strurl = [NSString stringWithFormat:@"ybmpage://productdetail?product_id=%@",cell.model.id];
    UIViewController *vc = [[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:strurl];
    [self.navigationController pushViewController:vc animated:YES];
}


#pragma mark - custom api

- (UIView *)sectionView:(NSInteger)section
{
    UIView *view = [[UIView alloc] init];
    view.backgroundColor = [UIColor clearColor];
    UIImageView *section0image = [[UIImageView alloc] initWithImage:[UIImage imageNamed:section ==1? @"section1":@"section2"]];
    [view addSubview:section0image];
    section0image.tag = 100;
    [section0image mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(view.mas_left).offset(10);
        make.right.equalTo(view.mas_right).offset(-10);
        make.top.equalTo(view.mas_top).offset(6);
        make.bottom.equalTo(view.mas_bottom).offset(-6);
    }];
    NSInteger end = section ==1?[self.model1.endTimestamp integerValue]/1000:[self.model2.endTimestamp integerValue]/1000;
    XYYTimeCountLab *lab = [[XYYTimeCountLab alloc] initWithFont:[UIFont systemFontOfSize:12] TextColor:[UIColor whiteColor] Alignment:NSTextAlignmentCenter CountTime:end];
    [section0image addSubview:lab];
    lab.tag = 1000;
    [lab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(section0image.mas_bottom).offset([self offsetByDevice]);
        make.centerX.equalTo(section0image.mas_centerX);
    }];
    if ([lab count] >0) {
        [lab startCount];
    }
    return view;

}


- (CGFloat)offsetByDevice
{
    if (iPhone6) {
        return -11.0;
    }
    if (iPhone6Plus) {
        return -13.0f;
    }
    if ((iPhone5) || (iPhone4)) {
        return -7;
    }
    return -4;
}

#pragma mark - 返回按钮
- (void)clickReturnButton {
    
    [self.navigationController popViewControllerAnimated:YES];
}

@end
