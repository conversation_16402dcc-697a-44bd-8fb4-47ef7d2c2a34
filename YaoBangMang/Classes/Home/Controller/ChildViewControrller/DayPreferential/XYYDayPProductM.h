//
//  XYYDayPProductM.h
//  YaoBangMang
//
//  Created by <PERSON><PERSON><PERSON> on 16/12/1.
//  Copyright © 2016年 XiaoYaoYao.Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "YBMProductAssembleInfoModel.h"
#import "YBMActPgbyModel.h"
#import "YBMLevelPriceModel.h"

@class XYYPriceRangM;
@interface XYYDayPProductM : NSObject
//判断逻辑:  1、isOEM--true是OEM协议商品，为空或者false为非OEM协议商品
//2、signStatus--1是已签署，是普通商品或者(OEM协议商品且已签署协议)才会显示价格
//3、agreementEffective--1是已生效，是普通商品或者(OEM协议商品且已签署协议且协议处于已生效状态)才可加入购物车
@property (nonatomic,assign) BOOL isOEM;//
@property (nonatomic,copy) NSString * showAgree; //是否符合协议标准展示价格,1:符合0:不符合
@property (nonatomic,assign) NSInteger signStatus;// OEM协议 是否签署
@property (nonatomic,assign) NSInteger agreementEffective;//
@property (nonatomic ,copy) NSString *id;   //商品id
@property (nonatomic ,copy) NSString *code;    //69码
@property (nonatomic ,copy) NSString *commonName;   //商品名字
@property (nonatomic ,copy) NSString *spec;     //规格
@property (nonatomic ,copy) NSString *status;   //状态  3正常 2售罄 4下架
@property (nonatomic ,copy) NSString *fob;      //价格
/// 阶梯价
@property (nonatomic, strong) YBMLevelPriceModel *levelPriceDTO;
@property (nonatomic ,copy) NSString *availableqty;  //库存
@property (nonatomic ,copy) NSString *imageUrl;     //图片
@property (nonatomic ,copy) NSString *approvalNumber;   //国药准字Z35020240
@property (nonatomic ,copy) NSString *markerUrl;//活动标识图片
@property (nonatomic ,strong) NSArray <XYYPriceRangM *>* productPriceRanges ;
@property (nonatomic ,assign) NSInteger isControlPriceToMe;
@property (nonatomic ,assign) NSInteger priceType;//价格类型（1：fob;2:价格区间

/// true:可购买 false:不可买  可购买，需要先判断是否是控销产品再判断这个字段
@property (nonatomic, assign) BOOL isPurchase;

/// 1:控销 0:正常
@property (nonatomic, strong) NSNumber *isControl;

@property (assign, nonatomic) BOOL isControlAgreement;

/// 控销价格展示文案
@property (copy, nonatomic) NSString *controlTitle;

/// 拼团信息，无此字段表示无拼团
@property (strong, nonatomic) YBMProductAssembleInfoModel *actPt;
//批购包邮信息
@property(strong,nonatomic)YBMActPgbyModel *actPgby;

@end
