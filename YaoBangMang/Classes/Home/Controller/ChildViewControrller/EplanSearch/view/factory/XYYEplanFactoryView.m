//
//  XYYEplanFactoryView.m
//  YaoBangMang
//
//  Created by <PERSON><PERSON><PERSON> on 2017/6/21.
//  Copyright © 2017年 XiaoYaoYao.Ltd. All rights reserved.
//

#import "XYYEplanFactoryView.h"
#import "XYYFactoryCell.h"
#import "XYYFactoryM.h"
#import "BMChineseSort.h"
#import "XYYFactoryIndexView.h"
#import "UIImage+GradientColor.h"
#import "UITableView+Help.h"

@interface XYYEplanFactoryView ()<UITextFieldDelegate,UITableViewDelegate,UITableViewDataSource>
@property (nonatomic ,strong) UITableView *tableview;
@property (nonatomic ,strong) NSMutableArray <NSString *>*arrSelectFacotry;


@property (nonatomic ,strong) NSArray <XYYFactoryM *>* arrData;
@property (nonatomic ,strong) NSMutableArray *indexArray;
@property (nonatomic ,strong)NSMutableArray *letterResultArr;


@property (nonatomic ,copy) NSString *productName;
@property (nonatomic ,copy) NSString *strSize;
@property (nonatomic ,copy) NSString *type;

@property (nonatomic ,strong) XYYFactoryIndexView *indexView;
@property (nonatomic ,strong) UILabel *labtittle;
@property (nonatomic ,strong)UITextField *searchFiled;

@property (nonatomic ,assign) BOOL isSearchModel;
@property (nonatomic ,strong) NSMutableArray <XYYFactoryM *>*arrSearch;
@property (nonatomic ,copy) NSString *strSearch;

@property (nonatomic ,assign) NSInteger showType;//0 侧滑样式  1置顶到下样式
@property (nonatomic ,strong) UIButton *btnok;//1样式下的

@end

@implementation XYYEplanFactoryView

- (void)updateParams:(NSString *)productname size:(NSString *)size type:(NSUInteger)type
{
    if ([productname isEqualToString: _productName] && type== [_type integerValue] &&([size isEqualToString:_strSize]) && self.arrData.count >0) {
        return;
    }
    self.productName = productname;
    self.strSize = size;
    self.type = [NSString stringWithFormat:@"%@",@(type)];
    [self requestData];
}

- (void)requestData{
    WEAKSELF
    [self.contentView showSpinner:@"加载中..."];
    [InterfaceManager searchFactoryForEplan:self.productName specSize:self.strSize type:self.type Completion:^(BOOL isSuccess, NSString *msg, id data) {
        if (isSuccess) {
            weakSelf.arrData = data;
            dispatch_async(dispatch_get_global_queue(0, 0), ^{
                weakSelf.indexArray = [BMChineseSort IndexWithArray:weakSelf.arrData Key:@"manufacturerName"];
                weakSelf.letterResultArr = [BMChineseSort sortObjectArray:weakSelf.arrData Key:@"manufacturerName"];
                dispatch_async(dispatch_get_main_queue(), ^{
                    [weakSelf.tableview reloadData];
                    [weakSelf.contentView hideSpinner];
                });
                
            });
        }
        else {
            [weakSelf.contentView hideSpinner];
            weakSelf.arrData = nil;
            [weakSelf.tableview reloadData];
        }
    }];
}

#pragma mark -
- (instancetype)initWithType:(NSInteger)type{
    if (self = [super init] ) {
        _showType = type;
        [self initTable];
    }
    return self;
}

- (void)willMoveToSuperview:(nullable UIView *)newSuperview{
    [super willMoveToSuperview:newSuperview];
    //[self.tableview reloadData];
    if (self.arrSelectFacotry.count == 0) {
        self.labtittle.textColor = ColorGreen_00B377;
    }
    else {
        self.labtittle.textColor = [UIColor colorByByteWithRed:102 green:102 blue:102 andFloatAlpha:1];
    }
    if (newSuperview != nil) {
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(serachNotificationChang:) name:UITextFieldTextDidChangeNotification object:_searchFiled];
    }
    else {
        [self.recVC sendData:self.arrSelectFacotry Type:XYY_FACTORYONE_TO_ALL];
        self.isSearchModel = NO;
        self.searchFiled.text = @"";
        [self.tableview reloadData];
        [[NSNotificationCenter defaultCenter] removeObserver:self];
    }
}

- (void)initTable{
    self.contentView.layer.masksToBounds = YES;
    UIView  *topView = [[UIView alloc] init];
    topView.backgroundColor = [UIColor whiteColor];
    [self.contentView addSubview:topView];
    [topView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView.mas_left);
        make.top.equalTo(self.contentView.mas_top);
        make.right.equalTo(self.contentView.mas_right);
        make.height.equalTo(self.showType==0? @((kNavBarHeight)):@0);
    }];
    if (self.showType == 1) {
        topView.hidden = YES;
        UIButton *btnReset = [UIButton buttonWithType:UIButtonTypeCustom];
        [btnReset setTitle:@"重置" forState:UIControlStateNormal];
        [btnReset setTitleColor:ColorText_292933 forState:UIControlStateNormal];
        btnReset.titleLabel.font = [UIFont fontWithName:FontTypePingFangRe size:16];
        btnReset.backgroundColor = [UIColor whiteColor];
        btnReset.layer.borderWidth = 1;
        btnReset.layer.borderColor = [UIColor colorWithHexString:@"#E7E7E7"].CGColor;
        [btnReset addTarget:self action:@selector(btnResetClick:) forControlEvents:UIControlEventTouchUpInside];
        
        UIButton *btnOK = [UIButton buttonWithType:UIButtonTypeCustom];
        _btnok = btnOK;
        [btnOK setTitle:@"确定" forState:UIControlStateNormal];
        [btnOK setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        btnOK.titleLabel.font = [UIFont fontWithName:FontTypePingFangRe size:16];
        btnOK.backgroundColor =ColorGreen_00B377;
        [btnOK addTarget:self action:@selector(btnbtnOKClick:) forControlEvents:UIControlEventTouchUpInside];
        
        [self.contentView addSubview:btnReset];
        [self.contentView addSubview:btnOK];
        [btnReset mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.contentView.mas_bottom);
            make.left.equalTo(self.contentView.mas_left);
            make.width.equalTo(self.contentView.mas_width).multipliedBy(0.5);
            make.height.equalTo(@50);
        }];
        [btnOK mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.contentView.mas_bottom);
            make.right.equalTo(self.contentView.mas_right);
            make.width.equalTo(self.contentView.mas_width).multipliedBy(0.5);
            make.height.equalTo(@50);
        }];
    }
    
    
    UIButton *btnback = [UIButton buttonWithType:UIButtonTypeCustom];
    [btnback setImage:[UIImage imageNamed:@"nav_back_gray"] forState:UIControlStateNormal];
    [btnback addTarget:self action:@selector(hiddenAnimation) forControlEvents:UIControlEventTouchUpInside];
    [topView addSubview:btnback];
    [btnback mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(topView.mas_left).offset(10.f);
        make.bottom.equalTo(topView.mas_bottom).offset(-4);
        make.width.equalTo(@35);
        make.height.equalTo(@35);
    }];
    
    UIButton *btnYes = [UIButton buttonWithType:UIButtonTypeCustom];
    [btnYes addTarget:self action:@selector(btnYesClick:) forControlEvents:UIControlEventTouchUpInside];
    [btnYes setTitle:@"确定" forState:UIControlStateNormal];
    [btnYes setTitleColor:ColorGreen_00B377 forState:UIControlStateNormal];
    btnYes.titleLabel.font = [UIFont fontWithName:FontTypePingFangRe size:14];
    [topView addSubview:btnYes];
    [btnYes mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(topView.mas_right).offset(-10);
        make.centerY.equalTo(btnback);
        make.width.equalTo(@45);
        make.height.equalTo(@40);
    }];
    
    UILabel *labtittle = [[UILabel alloc] init];
    labtittle.text = @"生产厂家";
    labtittle.textAlignment = NSTextAlignmentCenter;
    labtittle.textColor = ColorText_292933;
    labtittle.font = [UIFont systemFontOfSize:17];
    [topView addSubview:labtittle];
    [labtittle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(topView.mas_centerX);
        make.centerY.equalTo(btnback.mas_centerY);
    }];
    
    
    self.tableview = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, 0, 0) style:UITableViewStylePlain];
    self.tableview.showsHorizontalScrollIndicator = NO;
    self.tableview.showsVerticalScrollIndicator = NO;
    [self.tableview setExtraCellLineHidden];
    self.tableview.separatorColor =  [UIColor colorWithHexString:@"#F5F5F5" alpha:0.5];
    [self.tableview registerClass:[XYYFactoryCell class] forCellReuseIdentifier:@"XYYFactoryCell"];
    self.tableview.sectionIndexColor = ColorGreen_00B377;
    self.tableview.delegate = self;
    self.tableview.dataSource = self;
    [self.contentView addSubview:self.tableview];
    [self.tableview mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView.mas_left);
        make.right.equalTo(self.contentView.mas_right);
        make.top.equalTo(topView.mas_bottom);
        if (self.showType == 1) {
            make.bottom.equalTo(self.btnok.mas_top);
        }
        else {
            make.bottom.equalTo(self.contentView.mas_bottom);
        }
        
    }];
    
    UIView *headview = [[UIView alloc] init];
    headview.frame = CGRectMake(0, 0, XYYScreenW,self.showType ==0?kNavBarHeight+66 :66);
    
    UIView *searchView = [[UIView alloc] init];
    searchView.backgroundColor = [UIColor whiteColor];
    [headview addSubview:searchView];
    [searchView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(headview.mas_top);
        make.left.equalTo(headview.mas_left);
        make.right.equalTo(headview.mas_right);
        make.height.equalTo(@66);
    }];
    
    UIView *backSearchView = [[UIView alloc] init];
    
    UIImageView *imageSearch = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"Search"]];
    UITextField *fieldSearch = [[UITextField alloc] init];
    _searchFiled = fieldSearch;
    fieldSearch.delegate = self;
    fieldSearch.clearButtonMode = UITextFieldViewModeAlways;
    fieldSearch.font = [UIFont fontWithName:FontTypePingFangLi size:14];
    fieldSearch.textColor = ColorText_292933;
    fieldSearch.tintColor = ColorGreen_00B377;
    fieldSearch.placeholder = @"搜索";
    //fieldSearch.placeholderColor
    fieldSearch.textAlignment = NSTextAlignmentLeft;
    [backSearchView addSubview:imageSearch];
    [backSearchView addSubview:fieldSearch];
    [imageSearch mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(backSearchView.mas_left).offset(20);
        make.centerY.equalTo(backSearchView.mas_centerY);
    }];
    [fieldSearch mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(imageSearch.mas_right).offset(7);
        make.top.equalTo(backSearchView.mas_top);
        make.bottom.equalTo(backSearchView.mas_bottom);
        make.right.equalTo(backSearchView.mas_right).offset(-20);
    }];
    
    
    backSearchView.backgroundColor = ColorBack_F5F5F5;
    backSearchView.layer.cornerRadius = 2;
    [searchView addSubview:backSearchView];
    [backSearchView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(searchView.mas_centerY);
        make.left.equalTo(searchView.mas_left).offset(10);
        make.right.equalTo(searchView.mas_right).offset(-10);
        make.height.equalTo(@44);
    }];
    
    if (self.showType == 0) {
        UIView *topsection = [[UIView alloc] init];
        topsection.backgroundColor = [UIColor whiteColor];
        topsection.frame = CGRectMake(0, 0, XYYScreenW, 40);
        UILabel *laball = [[UILabel alloc] init];
        self.labtittle = laball;
        laball.textAlignment = NSTextAlignmentLeft;
        laball.textColor = [UIColor colorByByteWithRed:102 green:102 blue:102 andFloatAlpha:1];
        laball.text = @"全部厂家";
        [topsection addSubview:laball];
        UIButton *btnAll = [UIButton buttonWithType:UIButtonTypeCustom];
        [btnAll addTarget:self action:@selector(btnAllCIick:) forControlEvents:UIControlEventTouchUpInside];
        [topsection addSubview:btnAll];
        [btnAll mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(topsection);
        }];
        [laball mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(topsection.mas_centerY);
            make.left.equalTo(topsection.mas_left).offset(15);
        }];
        
        [headview addSubview:topsection];
        [topsection mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(headview.mas_left);
            make.bottom.equalTo(headview.mas_bottom);
            make.right.equalTo(headview.mas_right);
            make.top.equalTo(searchView.mas_bottom);
        }];
    }
    else {
        [self removeGestureRecognizer:self.tap];
        [self.backView removeGestureRecognizer:self.taphidden];
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(hiddenTypeOne)];
        [self.backView addGestureRecognizer:tap];
        self.tap = nil;
        self.contentView.layer.anchorPoint = CGPointMake(0.5, 0.5);
        [self.contentView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.mas_top);
            make.left.equalTo(self.mas_left);
            make.right.equalTo(self.mas_right);
            make.height.equalTo(@0);
            //make.bottom.equalTo(self.mas_bottom).offset(-60);
        }];
    }
    self.tableview.tableHeaderView = headview;
    [self layoutIfNeeded];
}

#pragma mark - UITableViewDelegate,UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView{
    return  _isSearchModel?1: [self.indexArray count];
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section{
    if (_isSearchModel) {
        return [self.arrSearch count];
    } else {
        return section<self.letterResultArr.count? [[self.letterResultArr objectAtIndex:section] count]:0;
    }
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    XYYFactoryCell *cell = [tableView dequeueReusableCellWithIdentifier:@"XYYFactoryCell"];
    XYYFactoryM *model = _isSearchModel?self.arrSearch[indexPath.row]: [[self.letterResultArr objectAtIndex:indexPath.section] objectAtIndex:indexPath.row];
    cell.isSelect = NO;
    
    for (NSString *name in self.arrSelectFacotry) {
        if (name!=nil && [model.manufacturerName isEqualToString:name]) {
            cell.isSelect = YES;
            break;
        }
    }
    cell.labFName.text = model.manufacturerName;
    return cell;
}

- (nullable NSArray<NSString *> *)sectionIndexTitlesForTableView:(UITableView *)tableView{
    return _isSearchModel?nil:self.indexArray;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return _isSearchModel?0: 30;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    if (_isSearchModel) {
        return nil;
    }
    UIView* customView = [[UIView alloc] initWithFrame:CGRectMake(0, 0.0, XYYScreenW, 30.0)];
    customView.backgroundColor = ColorBack_F7F7F8;
    UILabel * headerLabel = [[UILabel alloc] initWithFrame:CGRectZero];
    headerLabel.backgroundColor = [UIColor clearColor];
    headerLabel.textColor =ColorText_292933;
    headerLabel.font = [UIFont boldSystemFontOfSize:14];
    headerLabel.text =  [self.indexArray objectAtIndex:section];
    [customView addSubview:headerLabel];
    [headerLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(customView.mas_left).offset(15);
        make.centerY.equalTo(customView.mas_centerY);
    }];
    
    return customView;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 44;
}

- (NSInteger)tableView:(UITableView *)tableView sectionForSectionIndexTitle:(NSString*)title atIndex:(NSInteger)index{
    [self.indexView showOnView:self.contentView];
    self.indexView.labName.text = [self.indexArray objectAtIndex:index];
    return _isSearchModel?0:index;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    if (self.isEplan) {
        [self.arrSelectFacotry removeAllObjects];
    }
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    self.labtittle.textColor = [UIColor colorByByteWithRed:102 green:102 blue:102 andFloatAlpha:1];
    XYYFactoryM *model = _isSearchModel?self.arrSearch[indexPath.row]:[[self.letterResultArr objectAtIndex:indexPath.section] objectAtIndex:indexPath.row];
    XYYFactoryCell *cell = [tableView cellForRowAtIndexPath:indexPath];
    if (cell.isSelect) {
        cell.isSelect = NO;
        for (NSString *name in self.arrSelectFacotry) {
            if ([model.manufacturerName isEqualToString:name]) {
                [self.arrSelectFacotry removeObject:name];
                break;
            }
        }
    }
    else {
        cell.isSelect = YES;
        [self.arrSelectFacotry addObject: model.manufacturerName];
    }
    if (self.isEplan) {
        [self hiddenAnimation];
    }
}

- (void)hiddenAnimation {
    [super hiddenAnimation];
}

#pragma mark - notification

- (void)serachNotificationChang:(NSNotification *)notification{
    if (notification.object == _searchFiled) {
        if (![_searchFiled.text isEqualToString:_strSearch]) {
            _strSearch = _searchFiled.text;
            if (_searchFiled.text.length == 0) {
                self.isSearchModel = NO;
                [self.tableview reloadData];
                return;
            }
            [self searchDataByString:_searchFiled.text];
        }
    }
}


#pragma mark - btn res
- (void)btnAllCIick:(UIButton *)sender{
    [self.arrSelectFacotry removeAllObjects];
    [self.recV viewSendData:self.arrSelectFacotry Type:XYY_V_FACTORYONE_TO_ALL];
    [self.recVC sendData:self.arrSelectFacotry Type:XYY_FACTORYONE_TO_ALL];
    [self hiddenAnimation];
}

- (void)btnYesClick:(UIButton *)sender{
    [self hiddenAnimation];
    [self.recV viewSendData:self.arrSelectFacotry Type:XYY_V_FACTORYONE_TO_ALL];
    [self.recVC sendData:self.arrSelectFacotry Type:XYY_FACTORYONE_TO_ALL];
}

- (void)btnResetClick:(UIButton *)sender{
    [self.arrSelectFacotry removeAllObjects];
    [self.tableview reloadData];
}

- (void)btnbtnOKClick:(UIButton *)sender{
    [self hiddenTypeOne];
}

#pragma mark - custom api

- (void)showTypeOne
{
    [self.recVC sendData:@(1) Type:XYY_FACTORY_ANIMATIONDONE];
    [self.contentView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mas_top);
        make.left.equalTo(self.mas_left);
        make.right.equalTo(self.mas_right);
        make.bottom.equalTo(self.mas_bottom).offset(-85);
    }];
    [UIView animateWithDuration:0.25 animations:^{
        self.backView.alpha = 0.5;
        [self layoutIfNeeded];
    } completion:^(BOOL finished) {
        [self.tableview reloadData];
    }];
    
}

- (void)hiddenTypeOne
{
    [self.recVC sendData:@(0) Type:XYY_FACTORY_ANIMATIONDONE];
    [self.contentView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mas_top);
        make.left.equalTo(self.mas_left);
        make.right.equalTo(self.mas_right);
        make.height.equalTo(@0);
    }];
    [UIView animateWithDuration:0.25 animations:^{
        self.backView.alpha = 0;
        [self layoutIfNeeded];
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
    
}


- (void)searchDataByString:(NSString *)searchStr{
    @weakify(self)
    NSMutableArray *arr = [NSMutableArray array];
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        for (XYYFactoryM *model in self.arrData) {
            if ([model.manufacturerName rangeOfString:searchStr].length >0) {
                [arr addObject:model];
            }
        }
        dispatch_async(dispatch_get_main_queue(), ^{
            //重新加载table
            self_weak_.arrSearch = arr;
            self_weak_.isSearchModel = YES;
            [self_weak_.tableview reloadData];
        });
        
    });
}

#pragma mark  - set get
- (NSMutableArray <NSString *>*)arrSelectFacotry
{
    if (_arrSelectFacotry == nil) {
        _arrSelectFacotry = [NSMutableArray array];
    }
    return _arrSelectFacotry;
}

- (XYYFactoryIndexView *)indexView
{
    if (_indexView == nil) {
        _indexView = [[XYYFactoryIndexView alloc] init];
    }
    return _indexView;
}

@end
