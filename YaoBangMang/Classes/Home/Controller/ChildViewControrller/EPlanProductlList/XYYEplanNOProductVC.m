//
//  XYYEplanNOProductVC.m
//  YaoBangMang
//
//  Created by <PERSON><PERSON><PERSON> on 2017/5/8.
//  Copyright © 2017年 XiaoYaoYao.Ltd. All rights reserved.
//

#import "XYYEplanNOProductVC.h"
#import "XYYAnimationFieldView.h"
#import "XYYEPlanProductDetailM.h"
#import "XYYEplanSearchM.h"
#import "XYYEplanSearchSizeVC.h"
#import "XYYEplanFactoryView.h"
#import "XYYNavigationController.h"
#import "XYYFactoryM.h"

@interface XYYEplanNOProductVC () <UITextFieldDelegate>

@property (nonatomic ,strong) NSMutableArray <XYYAnimationFieldView *>*arrViews;
@property (nonatomic ,strong) XYYEplanSearchM *searchModel;
@property (nonatomic ,assign) BOOL cannotInput;

@property (nonatomic ,strong) XYYEplanSearchSizeVC *sepcVC;
@property (nonatomic ,strong) XYYEplanFactoryView *factoryView;

@end

@implementation XYYEplanNOProductVC

+ (void)load
{
    [[XYYRouterManager ShareRouterManager] mapClass:@{@"XYYEplanNOProductVC":@{@"className":@"XYYEplanNOProductVC",@"planid":@"planid",@"planName":@"planName",@"hiddelPromp":@"hiddelPromp"}}];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self initUI];
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    if (self.model != nil) {
        [self.arrViews[3].field becomeFirstResponder];
    }
}

- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
}

- (void)dealloc
{
    [self.recVC sendData:nil Type:XYY_INPUT_DISSPEAR];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)initUI
{
    self.title = kStringIsEmpty(self.planName) ? @"" : self.planName;
    self.view.backgroundColor = ColorBack_F7F7F8;
    UIView *topView = [[UIView alloc] init];
    topView.backgroundColor = ColorBack_FFF390;
    
    UILabel *labtitle = [[UILabel alloc] init];
    if (self.hiddelPromp) {
        topView.hidden = YES;
    }
    labtitle.text = @"无法识别该条形码,您可以手动登记该药品";
    labtitle.font = [UIFont fontWithName:FontTypePingFangRe size:14];
    labtitle.textColor = ColorText_FC6E00;
    labtitle.textAlignment = NSTextAlignmentCenter;
    labtitle.backgroundColor = [UIColor clearColor];
    [topView addSubview:labtitle];
    [labtitle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(topView);
    }];
    
    [self.view addSubview:topView];
    [topView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_top);
        make.left.equalTo(self.view.mas_left);
        make.right.equalTo(self.view.mas_right);
        if (self.hiddelPromp) {
            make.height.equalTo(@0);
        }
        else {
            make.height.equalTo(@33);
        }
        
    }];
    
    NSArray *arrstr = @[@"*名称",@"规格",@"厂家",@"采购数量",@"历史购进价"];
    for (int i = 0; i<5; i++) {
        XYYAnimationFieldView *view = [[XYYAnimationFieldView alloc] initWithPlacehodeText:arrstr[i]];
        view.field.delegate = self;
        view.backgroundColor = [UIColor whiteColor];
        view.field.returnKeyType = UIReturnKeyNext;
        [self.arrViews addObject:view];
        if (i == 3) {
            view.field.keyboardType = UIKeyboardTypeNumberPad;
        }
        if (i == 4) {
            view.field.keyboardType = UIKeyboardTypeDecimalPad;
        }
        if (i == 0) {
            UIButton *btnSearch = [UIButton buttonWithType:UIButtonTypeCustom];
            [btnSearch setTitle:@"搜索" forState:UIControlStateNormal];
            btnSearch.titleLabel.font = [UIFont fontWithName:FontTypePingFangMe size:16];
            [btnSearch setTitleColor:ColorGreen_00B377 forState:UIControlStateNormal];
            [btnSearch addTarget:self action:@selector(btnSearchClick:) forControlEvents:UIControlEventTouchUpInside];
            [view addSubview:btnSearch];
            [view.field mas_updateConstraints:^(MASConstraintMaker *make) {
                make.right.equalTo(view.mas_right).offset(-55);
            }];
            [btnSearch mas_makeConstraints:^(MASConstraintMaker *make) {
                make.right.equalTo(view.mas_right);
                make.top.equalTo(view.mas_top);
                make.bottom.equalTo(view.mas_bottom);
                make.width.equalTo(@70);
            }];
        }
        if (i == 1 ||i==2) {
            UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(changetap:)];
            [view addGestureRecognizer:tap];
        }
        [self.view addSubview:view];
        [view mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.view.mas_left);
            make.right.equalTo(self.view.mas_right);
            make.height.equalTo(@67);
            make.top.equalTo(topView.mas_bottom).offset(i*67);
        }];
        if (self.model != nil) {
            [view endInputAnimation];
            view.field.text = i==0?_model.productName:i==1?_model.spec:i==2?_model.manufacturer:_model.purchaseNumber;
            view.field.userInteractionEnabled = i==3;
        }
    }
    
    UIButton *btnBottomadd = [UIButton buttonWithType:UIButtonTypeCustom];
    if (_model != nil) {
        btnBottomadd.hidden = YES;
        [self initButtonWithTitle:@"保存" direction:NAVITEM_RIGHT];
    }
    btnBottomadd.layer.cornerRadius = 5;
    [btnBottomadd addTarget:self action:@selector(btnBottomaddClick:) forControlEvents:UIControlEventTouchUpInside];
    [btnBottomadd setBackgroundColor:ColorGreen_00B377];
    [btnBottomadd setTitle:@"添加到补货登记" forState:UIControlStateNormal];
    [btnBottomadd setTitleColor:ColorWhite_ffffff forState:UIControlStateNormal];
    btnBottomadd.titleLabel.font = [UIFont fontWithName:FontTypePingFangRe size:14];
    [self.view addSubview:btnBottomadd];
    [btnBottomadd mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.arrViews[4].mas_bottom).offset(11);
        make.left.equalTo(self.view.mas_left).offset(12);
        make.right.equalTo(self.view.mas_right).offset(-12);
        make.height.equalTo(@50);
    }];
    [self.arrViews[0].field becomeFirstResponder];
}

#pragma mark - rec data

- (void)sendData:(id)data Type:(XYY_SENDDATA_TYPE)type
{
    switch (type) {
        case XYY_EPLAN_PRODUCT_ADD:
        {
            _searchModel = data;
            self.cannotInput = YES;
            if (_searchModel != nil) {
                if (![self.arrViews[0].field hasText]) {
                    [self.arrViews[0] endInputAnimation];
                }
                if (![self.arrViews[1].field hasText]) {
                    [self.arrViews[1] endInputAnimation];
                }
                if (![self.arrViews[2].field hasText]) {
                    [self.arrViews[2] endInputAnimation];
                }
                self.arrViews[0].field.text = _searchModel.productName;
                self.arrViews[1].field.text = _searchModel.spec;
                self.arrViews[2].field.text = _searchModel.manufacturer;
                [self.arrViews[3].field becomeFirstResponder];
            }

        }
            break;
        case XYY_EPLAN_SIZE_CLICK:
        {
            self.arrViews[1].field.text = data;
        }
            break;
        case XYY_FACTORYONE_TO_ALL:
        {
            XYYFactoryM *model = data;
            self.arrViews[2].field.text = model.manufacturerName;
        }
            break;
        case XYY_FACTORYALL_TO_ALL:
        {

        }
        default:
            break;
    }
}


#pragma mark - UITextFieldDelegate

- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string
{
    if (textField == self.arrViews[0].field) {
        self.cannotInput = NO;
    }
    return YES;
}

- (BOOL)textFieldShouldReturn:(UITextField *)textField;
{
    if (textField == self.arrViews[0].field) {
        [self.arrViews[1].field becomeFirstResponder];
    }
    else if (textField == self.arrViews[1].field) {
        [self.arrViews[2].field becomeFirstResponder];
    }
    else if (textField == self.arrViews[2].field) {
        [self.arrViews[3].field becomeFirstResponder];
    }
    return YES;
}

#pragma mark - btn res

- (void)changetap:(UITapGestureRecognizer *)recognizer
{
    if (!self.cannotInput) {
        return;
    }
    [self.view endEditing:YES];
    WEAKSELF
    if (recognizer.view == self.arrViews[1]) {
        XYYNavigationController *nav = [[XYYNavigationController alloc] initWithRootViewController:self.sepcVC];
        [self presentViewController:nav animated:YES completion:^{
            [weakSelf.sepcVC updateParamsProductName:self.arrViews[0].field.text factoryName:self.arrViews[2].field.text type:[NSString stringWithFormat:@"%@",@(1)]];
        }];

    }
    else if (recognizer.view == self.arrViews[2]) {
        [self.factoryView show];
        [self.factoryView updateParams:self.arrViews[0].field.text size:self.arrViews[1].field.text type:1];
    }
}


- (void)rightOneButtonClick:(id)sender
{
//    if ([self.arrViews[3].field.text  integerValue] == [self.model.purchaseNumber integerValue]) {
//        MBShowFailureMessage(@"请修改数量后再提交！");
//        return;
//    }
    if ([self.arrViews[3].field.text integerValue] <= 0) {
        MBShowFailureMessage(@"数量不能为0");
        return;
    }
    if (self.arrViews[3].field.text.length >7) {
        MBShowFailureMessage(@"数量不能超过7位数");
        return;
    }
    [self.view showSpinner:@"修改中..."];
    WEAKSELF
    [InterfaceManager changeEplanProductNum:self.arrViews[3].field.text withCode:self.model.code withPlanid:self.planid price:nil Completion:^(BOOL isSuccess, NSString *msg, id data) {
        [weakSelf.view hideSpinner];
        if (isSuccess) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.8 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [weakSelf.recVC sendData:nil Type:XYY_EPLAN_NO_ADD];
                MBShowSuccessMessage(@"修改成功");
                [weakSelf.navigationController popViewControllerAnimated:YES];
            });

        }
        else {
            MBShowFailureMessage(msg);
            weakSelf.arrViews[3].field.text = weakSelf.model.purchaseNumber;
        }
    }];
}

- (void)btnSearchClick:(UIButton *)sender
{
//    if (!self.arrViews[0].field.hasText) {
//        MBShowFailureMessage(@"请输入商品名称");
//        return;
//    }
    XYYBaseVC *vc = (XYYBaseVC *)[[XYYRouterManager ShareRouterManager] mapVCFromStrUlr:[NSString stringWithFormat:@"ybmpage://XYYEplanSearchVC?strSearch=%@",self.arrViews[0].field.text]];
    vc.recVC = self;
    [self.navigationController pushViewController:vc animated:YES];
}


- (void)btnBottomaddClick:(UIButton *)sender
{
    if (!self.arrViews[0].field.hasText) {
        MBShowFailureMessage(@"请输入产品名称");
        return;
    }
//    if (!self.arrViews[1].field.hasText) {
//        MBShowFailureMessage(@"请输入产品规格");
//        return;
//    }
//    if (!self.arrViews[2].field.hasText) {
//        MBShowFailureMessage(@"请输入产品厂家");
//        return;
//    }
//    if (!self.arrViews[3].field.hasText) {
//        MBShowFailureMessage(@"请输入数量");
//        return;
//    }
    if ([self.arrViews[3].field.text integerValue] <=0 &&[self.arrViews[3].field hasText]) {
        MBShowFailureMessage(@"数量不能为0");
        return;
    }
    if (self.arrViews[3].field.text.length >7) {
        MBShowFailureMessage(@"数量不能超过7位数");
        return;
    }
    WEAKSELF
    [self.view showSpinner:@"添加中..."];
    [InterfaceManager addNotFoundProductWithName:self.arrViews[0].field.text factory:self.arrViews[2].field.text.length==0?@"暂无":self.arrViews[2].field.text spec:self.arrViews[1].field.text.length==0?@"暂无":self.arrViews[1].field.text count:self.arrViews[3].field.text.length==0?@"1":self.arrViews[3].field.text  ToPlan:self.planid code:self.searchModel.code price:self.arrViews[4].field.text.length == 0?nil:self.arrViews[4].field.text Completion:^(BOOL isSuccess, NSString *msg, id data) {
        [weakSelf.view hideSpinner];
        if (isSuccess) {
            MBShowSuccessMessage(@"添加成功");
            [weakSelf.recVC sendData:nil Type:XYY_EPLAN_NO_ADD];
            [weakSelf.navigationController popViewControllerAnimated:YES];
        }
        else {
            MBShowFailureMessage(msg);
        }
    }];
   
}

#pragma mark - set get

- (NSMutableArray <XYYAnimationFieldView *>*)arrViews
{
    if (_arrViews == nil) {
        _arrViews = [NSMutableArray array];
    }
    return _arrViews;
}

- (void)setCannotInput:(BOOL)cannotInput
{
    _cannotInput = cannotInput;
    if (_cannotInput) {
//        self.arrViews[0].field.enabled = NO;
        self.arrViews[1].field.enabled = NO;
        self.arrViews[2].field.enabled = NO;
    }
    else {
//        self.arrViews[0].field.enabled = YES;
        self.arrViews[1].field.enabled = YES;
        self.arrViews[2].field.enabled = YES;
    }
}


- (XYYEplanFactoryView *)factoryView
{
    if (_factoryView == nil) {
        _factoryView = [[XYYEplanFactoryView alloc] init];
        _factoryView.recVC = self;
    }
    return _factoryView;
}

- (XYYEplanSearchSizeVC *)sepcVC
{
    if (_sepcVC == nil) {
        _sepcVC = [[XYYEplanSearchSizeVC alloc] init];
        _sepcVC.recVC = self;
    }
    return _sepcVC;
}



@end
