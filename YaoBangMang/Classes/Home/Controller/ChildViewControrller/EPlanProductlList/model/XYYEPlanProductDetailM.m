//
//  XYYEPlanProductDetailM.m
//  YaoBangMang
//
//  Created by <PERSON><PERSON><PERSON> on 2017/4/11.
//  Copyright © 2017年 XiaoYaoYao.Ltd. All rights reserved.
//

#import "XYYEPlanProductDetailM.h"

@implementation XYYEPlanProductDetailM

+ (NSMutableArray <XYYEPlanProductDetailM *>*)testArr
{
    NSMutableArray *arr = [NSMutableArray array];
    for ( int i = 0; i<10; i++) {
        XYYEPlanProductDetailM *model = [[XYYEPlanProductDetailM alloc] init];
        model.manufacturer = @"哈尔滨制药六厂";
        model.id = @"1";
        model.productName = @"感康感康";
        model.purchaseNumber = @"5";
        model.spec = @"1.2克*5";
        model.status = @"2";
        [arr addObject:model];
    }
    return arr;
}

@end
