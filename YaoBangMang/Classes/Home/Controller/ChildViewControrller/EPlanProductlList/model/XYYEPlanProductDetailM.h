//
//  XYYEPlanProductDetailM.h
//  YaoBangMang
//
//  Created by <PERSON><PERSON><PERSON> on 2017/4/11.
//  Copyright © 2017年 XiaoYaoYao.Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface XYYEPlanProductDetailM : NSObject
@property (nonatomic, assign) NSInteger isShow806;
@property (nonatomic, assign) NSInteger gift;
@property (nonatomic ,copy) NSString *manufacturer;//药品厂家
@property (nonatomic ,copy) NSString *id;//药品id
@property (nonatomic ,copy) NSString *productName;//药品名称
@property (nonatomic ,copy) NSString *purchaseNumber;//采购数量
@property (nonatomic ,copy) NSString *spec;//药品规格
@property (nonatomic ,copy) NSString *status;//销售状态 1 全部 2在售
@property (nonatomic ,copy) NSString *photo;
@property (nonatomic ,copy) NSString *fob;
@property (nonatomic ,copy) NSString *code;
@property (nonatomic ,copy) NSString *productStatus;//2 售罄 4 下架
@property (nonatomic,strong) NSString *price;
@property (nonatomic,assign) BOOL isChecked;
@property (nonatomic,strong) NSString *planningScheduleId;
@property (nonatomic,strong) NSString *productId;
@property (nonatomic,strong) NSString *zjm;
@property (nonatomic,assign) BOOL isCart;

+ (NSMutableArray <XYYEPlanProductDetailM *>*)testArr;

@end
