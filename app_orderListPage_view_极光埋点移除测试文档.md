# app_orderListPage_view 极光埋点移除测试文档

## 概述
本文档记录了移除 `app_orderListPage_view` 极光埋点事件的详细信息，包括修改位置、影响范围和测试建议。

## 修改详情

### 1. 修改文件列表

#### 1.1 YBMNewMeVC.m
**文件路径：** `YaoBangMang/Classes/Me/YBMNewMeVC.m`  
**修改位置：** 第 904-905 行  
**方法名：** `auroraAnalysisOrderListPageExposure`  
**修改内容：** 注释掉极光埋点调用 `[AnalysysAgent track:kApp_orderListPage_view properties:mutD];`

#### 1.2 XYYTabBarController.m
**文件路径：** `YaoBangMang/BaseNavTabController/XYYTabBarController.m`  
**修改位置：** 第 707-708 行  
**方法名：** `auroraAnalysisOrderListPageExposure`  
**修改内容：** 注释掉极光埋点调用 `[AnalysysAgent track:kApp_orderListPage_view properties:mutD];`

#### 1.3 YBMTabBarController.m
**文件路径：** `YaoBangMang/BaseNavTabController/YBMTabBarController.m`  
**修改位置：** 第 508-509 行  
**方法名：** `auroraAnalysisOrderListPageExposure`  
**修改内容：** 注释掉极光埋点调用 `[AnalysysAgent track:kApp_orderListPage_view properties:mutD];`

#### 1.4 YBMAnalyticsEvents.h
**文件路径：** `YaoBangMang/Classes/Vendor/Analytics/YBMAnalyticsEvents.h`  
**修改位置：** 第 563-564 行  
**修改内容：** 注释掉事件常量定义 `#define kApp_orderListPage_view @"app_orderListPage_view"`

### 2. 触发场景分析

#### 2.1 主要触发场景
- **场景1：** 用户点击底部导航栏的"订单"tab
- **场景2：** 用户从"我的"页面点击订单相关入口

#### 2.2 具体触发路径

##### 场景1：底部导航栏订单tab点击
- **触发路径：** TabBar点击 → `tabBarController:shouldSelectViewController:` → `auroraAnalysisOrderListPageExposure`
- **影响控制器：** 
  - YBMTabBarController（新版TabBar控制器）
  - XYYTabBarController（旧版TabBar控制器）
- **埋点参数：** `jgspid_orderlist_tab_exposure` (4201)

##### 场景2：我的页面订单入口点击
- **触发路径：** 我的页面订单点击 → `ybm_meViewDidSelectIndex:` → `auroraAnalysisOrderListPageExposure`
- **影响控制器：** YBMNewMeVC
- **埋点参数：** `jgspid_orderlist_mine_exposure` (4202)

### 3. 影响范围评估

#### 3.1 页面影响
- **订单列表页：** 所有通过底部导航栏和我的页面进入的订单列表页面
- **我的页面：** 点击订单相关入口的埋点逻辑
- **底部导航栏：** 订单tab的点击埋点逻辑

#### 3.2 功能影响
- **✅ 无业务功能影响：** 移除的是埋点代码，不影响页面正常功能
- **✅ 保留其他埋点：** 雪地埋点和QT埋点继续正常工作
- **✅ 保留方法结构：** `auroraAnalysisOrderListPageExposure` 方法保留，只注释埋点调用

#### 3.3 兼容性处理
- **方法保留：** 所有相关方法都保留，确保调用链不会断裂
- **参数构建保留：** 埋点参数构建逻辑保留，便于后续恢复或调试
- **注释标记：** 所有修改都添加了明确的注释标记，便于识别和管理

## 测试建议

### 4. 功能测试

#### 4.1 基础功能验证
- [ ] 底部导航栏"订单"tab点击正常跳转
- [ ] 我的页面订单入口点击正常跳转
- [ ] 订单列表页面正常显示和操作
- [ ] 页面切换和返回正常

#### 4.2 埋点验证
- [ ] 雪地埋点正常工作
- [ ] QT埋点正常工作
- [ ] 极光埋点 `app_orderListPage_view` 已停止发送
- [ ] 其他极光埋点事件正常

#### 4.3 回归验证
- [ ] 相关页面无异常
- [ ] 性能无明显影响
- [ ] 其他埋点系统正常

### 5. 测试用例

#### 5.1 底部导航栏测试
| 测试步骤 | 预期结果 | 实际结果 | 状态 |
|---------|---------|---------|------|
| 点击底部"订单"tab | 正常跳转到订单列表页 |  | ⏳ |
| 检查极光埋点日志 | 无 `app_orderListPage_view` 事件 |  | ⏳ |
| 检查其他埋点 | 雪地、QT埋点正常 |  | ⏳ |

#### 5.2 我的页面测试
| 测试步骤 | 预期结果 | 实际结果 | 状态 |
|---------|---------|---------|------|
| 点击我的页面订单入口 | 正常跳转到订单列表页 |  | ⏳ |
| 检查极光埋点日志 | 无 `app_orderListPage_view` 事件 |  | ⏳ |
| 检查其他埋点 | 雪地、QT埋点正常 |  | ⏳ |

## 风险评估

### 6. 风险等级：低

#### 6.1 技术风险
- **风险：** 无，只是注释埋点代码
- **缓解措施：** 保留完整的方法结构和参数构建逻辑

#### 6.2 业务风险
- **风险：** 数据分析团队可能需要调整相关报表
- **缓解措施：** 提前通知数据分析团队

#### 6.3 回滚方案
- **简单回滚：** 取消注释即可恢复埋点功能
- **验证方法：** 检查埋点日志中是否重新出现 `app_orderListPage_view` 事件

## 沟通确认

### 7. 相关团队通知
- [ ] 通知数据分析团队
- [ ] 通知测试团队
- [ ] 更新相关文档

## 联系信息
如有问题，请联系：
- **开发团队：** 负责代码修改和技术支持
- **数据团队：** 负责埋点数据分析和报表调整  
- **测试团队：** 负责功能验证和回归测试

## 附录

### A. 修改前后对比

#### A.1 YBMNewMeVC.m
```objective-c
// 修改前
[AnalysysAgent track:kApp_orderListPage_view properties:mutD];

// 修改后
// 极光埋点已移除 - app_orderListPage_view
// [AnalysysAgent track:kApp_orderListPage_view properties:mutD];
```

#### A.2 事件常量定义
```objective-c
// 修改前
#define kApp_orderListPage_view @"app_orderListPage_view"

// 修改后  
// #define kApp_orderListPage_view @"app_orderListPage_view"
```

### B. 相关埋点事件
- **移除：** `kApp_orderListPage_view` (订单页面曝光)
- **保留：** 雪地埋点相关事件
- **保留：** QT埋点相关事件
- **保留：** 其他极光埋点事件

### C. 相关常量
- `jgspid_orderlist_tab_exposure` = "4201"（底部tab入口）
- `jgspid_orderlist_mine_exposure` = "4202"（我的页入口）

---

**文档版本：** 1.0  
**创建日期：** 2025-06-19  
**最后更新：** 2025-06-19
