# app_tab_order_click 极光埋点移除测试文档

## 概述
本文档记录了移除 `app_tab_order_click` 极光埋点事件的详细信息，包括修改位置、影响范围和测试建议。

## 修改详情

### 1. 修改文件列表

#### 1.1 YBMTabBarController.m
**文件路径：** `YaoBangMang/BaseNavTabController/YBMTabBarController.m`  
**修改位置：** 第 493-500 行  
**方法名：** `auroraAnalysisTabClickEvent`  
**修改内容：** 注释掉极光埋点调用 `[AnalysysAgent track:kApp_tab_order_click properties:mutD];`

#### 1.2 XYYTabBarController.m
**文件路径：** `YaoBangMang/BaseNavTabController/XYYTabBarController.m`  
**修改位置：** 第 692-699 行  
**方法名：** `auroraAnalysisTabClickEvent`  
**修改内容：** 注释掉极光埋点调用 `[AnalysysAgent track:kApp_tab_order_click properties:mutD];`

#### 1.3 YBMCodeViewController.m
**文件路径：** `YaoBangMang/BaseNavTabController/YBMCodeViewController.m`  
**修改位置：** 第 292-298 行  
**方法名：** `auroraAnalysisTabClickEvent`  
**修改内容：** 注释掉极光埋点调用 `[AnalysysAgent track:kApp_tab_order_click properties:mutD];`

#### 1.4 YBMAnalyticsEvents.h
**文件路径：** `YaoBangMang/Classes/Vendor/Analytics/YBMAnalyticsEvents.h`  
**修改位置：** 第 561-562 行  
**修改内容：** 注释掉事件常量定义 `#define kApp_tab_order_click @"app_tab_order_click"`

### 2. 触发场景分析

#### 2.1 主要触发场景
- **场景：** 用户点击底部导航栏的"订单"tab
- **触发路径：** TabBar点击 → `tabBarController:shouldSelectViewController:` → `auroraAnalysisTabClickEvent`
- **影响页面：** 所有包含底部导航栏的页面

#### 2.2 相关控制器
- **YBMTabBarController：** 主要的TabBar控制器
- **XYYTabBarController：** 备用的TabBar控制器  
- **YBMCodeViewController：** 特殊场景下的控制器

### 3. 兼容性处理

#### 3.1 保留的功能
- ✅ 雪地埋点系统完全保留
- ✅ QT埋点系统完全保留
- ✅ 订单页面曝光埋点保留（`auroraAnalysisOrderListPageExposure`）
- ✅ 业务逻辑完全不受影响

#### 3.2 移除的功能
- ❌ 极光埋点的 `app_tab_order_click` 事件
- ❌ 相关的事件常量定义

## 测试建议

### 1. 功能测试

#### 1.1 基础功能验证
**测试步骤：**
1. 启动应用
2. 点击底部导航栏的"订单"tab
3. 验证页面正常跳转到订单列表页
4. 验证订单列表数据正常加载
5. 验证页面交互功能正常

**预期结果：**
- 页面跳转正常
- 数据加载正常
- 交互功能正常
- 无崩溃或异常

#### 1.2 多场景测试
**测试场景：**
- 从首页点击订单tab
- 从购物车页面点击订单tab
- 从我的页面点击订单tab
- 从店铺列表页面点击订单tab

### 2. 埋点测试

#### 2.1 雪地埋点验证
**测试步骤：**
1. 开启调试模式
2. 执行上述功能测试步骤
3. 查看控制台日志
4. 确认 `YBMAnalyticsUtil.track` 调用正常

**验证点：**
- 雪地埋点事件正常发送
- 事件参数正确
- 时序正常

#### 2.2 极光埋点验证
**测试步骤：**
1. 开启极光调试模式
2. 执行功能测试
3. 查看控制台日志
4. 确认不再有 `app_tab_order_click` 事件

**验证点：**
- 控制台不再输出 `AnalysysAgent.track` 相关的 `app_tab_order_click` 日志
- 极光平台不再接收该事件数据
- 其他极光埋点事件正常（如订单页面曝光事件）

#### 2.3 QT埋点验证
**测试步骤：**
1. 开启QT埋点调试模式
2. 执行功能测试
3. 验证QT埋点正常工作

### 3. 回归测试

#### 3.1 相关功能测试
**测试范围：**
- 订单列表页面所有功能
- 订单详情页面
- 订单搜索功能
- 订单筛选功能
- 订单状态切换

#### 3.2 其他埋点系统测试
**测试范围：**
- 验证其他极光埋点事件正常
- 验证雪地埋点系统正常
- 验证QT埋点系统正常

### 4. 性能测试
**测试项目：**
- 验证移除埋点后页面响应时间
- 确认内存使用无异常
- 验证网络请求正常

## 风险评估

### 低风险项
- ✅ 代码修改量小，只是注释掉极光埋点调用
- ✅ 保留了雪地埋点和QT埋点，数据收集不完全中断
- ✅ 业务逻辑完全不受影响
- ✅ 可以随时恢复（取消注释即可）

### 注意事项
- ⚠️ 需要通知数据分析团队该变更
- ⚠️ 相关报表和监控需要调整
- ⚠️ 建议在测试环境充分验证后再发布到生产环境

## 验证清单

### 开发验证
- [x] 代码编译无错误
- [x] 语法检查通过
- [x] 注释清晰明确

### 功能验证
- [ ] 订单tab点击功能正常
- [ ] 页面跳转正常
- [ ] 数据加载正常
- [ ] 交互功能正常

### 埋点验证
- [ ] 雪地埋点正常工作
- [ ] QT埋点正常工作
- [ ] 极光埋点 `app_tab_order_click` 已停止发送
- [ ] 其他极光埋点事件正常

### 回归验证
- [ ] 相关页面无异常
- [ ] 性能无明显影响
- [ ] 其他埋点系统正常

### 沟通确认
- [ ] 通知数据分析团队
- [ ] 通知测试团队
- [ ] 更新相关文档

## 联系信息
如有问题，请联系：
- **开发团队：** 负责代码修改和技术支持
- **数据团队：** 负责埋点数据分析和报表调整  
- **测试团队：** 负责功能验证和回归测试

## 附录

### A. 修改前后对比

#### A.1 YBMTabBarController.m
```objective-c
// 修改前
[AnalysysAgent track:kApp_tab_order_click properties:mutD];

// 修改后
// 极光埋点已移除 - app_tab_order_click
// [AnalysysAgent track:kApp_tab_order_click properties:mutD];
```

#### A.2 事件常量定义
```objective-c
// 修改前
#define kApp_tab_order_click @"app_tab_order_click"

// 修改后  
// #define kApp_tab_order_click @"app_tab_order_click"
```

### B. 相关埋点事件
- **保留：** `kApp_orderListPage_view` (订单页面曝光)
- **保留：** 雪地埋点相关事件
- **保留：** QT埋点相关事件
- **移除：** `kApp_tab_order_click` (订单tab点击)
