# action_product_button_click 极光埋点移除测试文档

## 概述

本文档记录了移除项目中事件ID为`action_product_button_click`的极光埋点的详细信息，包括修改的文件、方法位置、影响范围以及测试建议。

## 修改内容

### 1. 移除的极光埋点

#### 1.1 商品详情页相关埋点

**文件**: `YaoBangMang/Classes/Modules/Product/Detail/View/Custom/YBMProductAddBuyView.m`

**修改方法**:
- `trackProductButtonClick:` (第732-748行)
- `auroraAnalysisClickWithBtnName:` (第786-791行)

**修改内容**:
```objective-c
// 修改前：
[AnalysysAgent track:kAction_product_button_click properties:mutD];

// 修改后：
// 极光埋点已移除 - action_product_button_click (商品详情页)
// [AnalysysAgent track:kAction_product_button_click properties:mutD];
```

#### 1.2 商品加购弹窗相关埋点

**文件**: `YaoBangMang/Classes/Modules/Product/Detail/View/Other/YBMProductAddView.m`

**修改方法**: `trackProductButtonClick:` (第450-486行)

**修改逻辑**:
- 保留搜索页面（YBMSearchVC、XYYNewSearchVC）的极光埋点
- 移除商品详情页（YBMProductDetailController）的极光埋点
- 移除其他页面的极光埋点

**修改内容**:
```objective-c
if ([targetVc isKindOfClass:[YBMProductDetailController class]]) {
    // 极光埋点已移除 - action_product_button_click (商品详情页底部弹窗)
    // [AnalysysAgent track:kAction_product_button_click properties:mutD];
    return;
} else if([targetVc isKindOfClass:[YBMSearchVC class]] || [targetVc isKindOfClass:[XYYNewSearchVC class]]){
    // 保留搜索页面的极光埋点
    [AnalysysAgent track:kAction_product_button_click properties:mutD];
} else {
    // 极光埋点已移除 - action_product_button_click (其他页面)
    // [AnalysysAgent track:kAction_product_button_click properties:mutD];
    return;
}
```

#### 1.3 拼团结算视图相关埋点

**文件**: `YaoBangMang/Classes/Modules/Product/Detail/View/Other/YBMProductGroupSettleView.m`

**修改方法**: `trackProductButtonClick:` (第947-982行)

**修改逻辑**: 与YBMProductAddView.m保持一致的逻辑

### 2. 保留的搜索页面埋点

#### 2.1 搜索列表商品Cell

**文件**: `YaoBangMang/Classes/Search/Cell/YBMProductCell.m`

**方法**: `trackProductButtonClick:` (第1640-1649行)

**状态**: 保持不变，继续发送极光埋点

#### 2.2 搜索页面运营位Cell

**文件**: `YaoBangMang/Classes/Search/Cell/YBMOperationCollectionViewCell.m`

**方法**: `trackProductButtonClick:` (第524-533行)

**状态**: 保持不变，继续发送极光埋点

### 3. 保留的相关代码

#### 3.1 保留的属性和方法
- **埋点常量定义**: `kAction_product_button_click` 在 `YBMAnalyticsEvents.h` 中保留
- **参数构建逻辑**: 所有参数构建逻辑保持不变，只是不发送极光埋点
- **QT埋点**: 所有QT埋点功能正常工作
- **雪地埋点**: 其他雪地埋点保持正常

#### 3.2 保留的埋点系统
- **QT埋点**: `kQTActionProductButtonClick` 事件正常工作
- **雪地埋点**: `YBMAnalyticsUtil` 相关埋点保持正常
- **其他极光埋点**: 加购、商品点击等其他事件正常工作

## 影响范围分析

### 4.1 直接影响
- **商品详情页按钮点击统计**: 不再上报极光埋点的 `action_product_button_click` 事件
- **商品详情页底部弹窗**: 不再上报极光埋点
- **拼团结算弹窗**: 不再上报极光埋点
- **其他非搜索页面**: 不再上报极光埋点

### 4.2 无影响的功能
- **搜索页面埋点**: 继续正常工作，使用 `action_product_button_click` 事件
- **QT埋点**: 继续正常工作，使用 `kQTActionProductButtonClick` 事件
- **商品加购埋点**: 继续正常工作，使用 `kAdd_to_cart` 事件
- **商品点击埋点**: 继续正常工作，使用 `kAction_list_product_click` 事件
- **其他页面的极光埋点**: 不受影响

### 4.3 兼容性处理
- **代码注释**: 所有移除的埋点调用都添加了详细注释说明
- **参数构建**: 参数构建逻辑保持完整，便于后续恢复
- **方法调用**: 其他组件调用相关方法不受影响
- **业务逻辑**: 所有业务逻辑完全不受影响

## 测试建议

### 5.1 功能测试
1. **商品详情页测试**
   - 测试页面: 任意商品详情页
   - 测试操作: 点击加购、立即购买、立即参团等按钮
   - 验证点: 页面功能正常，业务逻辑无异常
   - 预期结果: 所有功能完全正常

2. **搜索页面测试**
   - 测试页面: 大搜页面、专区搜索页面
   - 测试操作: 点击商品按钮（加购、参团、抢购等）
   - 验证点: 极光埋点正常上报
   - 预期结果: `action_product_button_click` 事件正常触发

3. **底部弹窗测试**
   - 测试页面: 商品详情页、拼团页面
   - 测试操作: 弹出底部弹窗，点击确定、加购等按钮
   - 验证点: 弹窗功能正常，业务逻辑无异常
   - 预期结果: 功能完全正常，不影响用户体验

### 5.2 埋点验证
1. **极光埋点控制台检查**
   - 确认商品详情页不再上报 `action_product_button_click` 事件
   - 确认搜索页面继续上报 `action_product_button_click` 事件
   - 确认其他极光埋点事件正常上报

2. **QT埋点控制台检查**
   - 确认 `action_product_button_click` 事件正常上报
   - 确认商品相关的QT埋点事件正常

3. **雪地埋点控制台检查**
   - 确认相关雪地埋点事件正常上报

### 5.3 回归测试
1. **商品详情页核心流程**
   - 从搜索列表进入详情页
   - 从首页推荐进入详情页
   - 从购物车进入详情页
   - 验证所有入口的详情页功能正常

2. **搜索页面核心流程**
   - 大搜功能测试
   - 专区搜索功能测试
   - 常购常搜功能测试
   - 验证搜索相关埋点正常

3. **加购流程测试**
   - 列表页加购
   - 详情页加购
   - 底部弹窗加购
   - 验证加购功能和相关埋点正常

## 风险评估

### 6.1 低风险
- 代码修改量适中，主要是注释掉埋点调用
- 保留了搜索页面的埋点，核心数据收集不完全中断
- 业务逻辑完全不受影响
- 所有参数构建逻辑保持完整

### 6.2 注意事项
- 需要通知数据分析团队该变更
- 相关报表和监控需要调整数据源
- 如果后续需要恢复，只需取消注释即可
- 建议观察一段时间确保无异常

## 验证清单

- [ ] 代码编译无错误
- [ ] 商品详情页功能测试通过
- [ ] 搜索页面功能测试通过
- [ ] 底部弹窗功能测试通过
- [ ] 搜索页面极光埋点正常工作
- [ ] QT埋点正常工作
- [ ] 雪地埋点正常工作
- [ ] 商品详情页极光埋点已停止发送
- [ ] 其他页面功能无异常
- [ ] 性能无明显影响
- [ ] 通知相关团队

## 联系信息
如有问题，请联系：
- 开发团队：负责代码修改和技术支持
- 数据团队：负责埋点数据分析和报表调整
- 测试团队：负责功能验证和回归测试
