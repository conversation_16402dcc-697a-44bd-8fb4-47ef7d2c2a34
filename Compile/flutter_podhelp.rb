require 'json'

def install_flutter_module(flutter_pod_version = '0.9.0.10')
	# 导入本地配置环境
	application_path = export_config
	puts "#{application_path}"
	if !application_path.nil? && !application_path.empty?
		load File.join(application_path, '.ios', 'Flutter', 'podhelper.rb')
		install_all_flutter_pods(application_path)
		puts "pod 引用了本地路径为：#{application_path} 的Flutter项目"
	else
		pod 'YBM-Flutter-Private', "#{flutter_pod_version}"
		puts "pod 引用了远端版本为#{flutter_pod_version}的项目"
	end
end

def export_config()
	flutter_config_export_path = File.join('.', 'Compile', 'flutter_config_export.json')
	if File.exist?(flutter_config_export_path)
		json_string = File.read(flutter_config_export_path)
		if !json_string.nil? && !json_string.empty?
			config = JSON.parse(json_string)
			if config 
				return config["FLUTTER_APPLICATION_PATH"]
			end
		end
	end
end
