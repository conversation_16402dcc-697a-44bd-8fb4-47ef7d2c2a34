# 极光埋点 page_product_detail_exposure 移除测试文档

## 概述
本文档记录了移除项目中事件为 `page_product_detail_exposure` 的极光埋点的详细信息，包括修改的文件、方法位置、影响范围和测试建议。

## 修改内容

### 1. 移除的极光埋点事件
- **事件名称**: `page_product_detail_exposure`
- **事件常量**: `kPage_product_detail_exposure`
- **定义位置**: `YaoBangMang/Classes/Vendor/Analytics/YBMAnalyticsEvents.h:545`
- **事件值**: `@"page_product_detail_exposure"`

### 2. 修改的文件和方法

#### 2.1 YBMProductDetailController.m
**文件路径**: `YaoBangMang/Classes/Modules/Product/Detail/Controller/YBMProductDetailController.m`

**修改内容**:
1. **移除方法调用** (第242行)
   - 原代码: `[self trackProductDetailExposure:self.detailModel];`
   - 修改后: 已移除该行

2. **移除方法实现** (第988-1000行)
   - 移除了整个 `trackProductDetailExposure:` 方法
   - 该方法负责调用极光埋点 `kPage_product_detail_exposure` 事件

3. **清理注释代码** (第1140-1205行)
   - 移除了大段被注释的极光埋点相关代码
   - 包括参数构建和 `AnalysysAgent track:kPage_product_detail_exposure` 调用

### 3. 保留的相关代码

#### 3.1 保留的属性和方法
- **`detailHasTrack` 属性**: 保留，因为QT埋点仍在使用
- **`getTrackCommonParams:` 方法**: 保留，因为其他极光埋点事件仍在使用
- **`productDetailTrackDict` 属性**: 保留，用于商品按钮点击等其他埋点事件

#### 3.2 保留的埋点系统
- **QT埋点**: `QTTrackPageExposureWithModel:` 方法正常工作
- **雪地埋点**: `snowAnalysisAction:` 方法保持注释状态（原本就被注释）
- **其他极光埋点**: 商品按钮点击、加购等事件正常工作

## 影响范围分析

### 4.1 直接影响
- **商品详情页曝光统计**: 不再上报极光埋点的 `page_product_detail_exposure` 事件
- **页面**: 所有商品详情页 (`YBMProductDetailController`)

### 4.2 无影响的功能
- **QT埋点页面曝光**: 继续正常工作，使用 `kQTPageExposure` 事件
- **商品按钮点击埋点**: 继续正常工作，使用 `kAction_product_button_click` 事件
- **商品加购埋点**: 继续正常工作，使用 `kAdd_to_cart` 事件
- **其他页面的极光埋点**: 不受影响

### 4.3 兼容性处理
- **控制标志**: `detailHasTrack` 标志被QT埋点复用，确保页面曝光只上报一次
- **参数字典**: `productDetailTrackDict` 继续为其他埋点事件提供参数
- **方法调用**: 其他组件调用 `getTrackCommonParams:` 方法不受影响

## 测试建议

### 5.1 功能测试
1. **商品详情页访问测试**
   - 测试页面: 任意商品详情页
   - 验证点: 页面正常加载，功能无异常
   - 预期结果: 页面功能完全正常

2. **QT埋点验证**
   - 测试页面: 商品详情页
   - 验证点: QT埋点的页面曝光事件正常上报
   - 预期结果: `kQTPageExposure` 事件正常触发

3. **商品操作埋点验证**
   - 测试页面: 商品详情页
   - 测试操作: 点击加购、立即购买等按钮
   - 验证点: 相关极光埋点事件正常上报
   - 预期结果: `kAction_product_button_click`、`kAdd_to_cart` 等事件正常

### 5.2 埋点验证
1. **极光埋点控制台检查**
   - 确认 `page_product_detail_exposure` 事件不再上报
   - 确认其他极光埋点事件正常上报

2. **QT埋点控制台检查**
   - 确认 `page_exposure` 事件正常上报
   - 确认商品相关的QT埋点事件正常

### 5.3 回归测试
1. **商品详情页核心流程**
   - 从搜索列表进入详情页
   - 从首页推荐进入详情页
   - 从购物车进入详情页
   - 验证所有入口的详情页功能正常

2. **埋点数据完整性**
   - 验证商品点击、曝光、加购等关键埋点数据完整
   - 确认数据分析不受影响

## 风险评估

### 6.1 低风险
- **功能影响**: 无，仅移除特定埋点事件
- **性能影响**: 无，减少了一次埋点调用
- **兼容性**: 良好，保留了其他埋点系统

### 6.2 注意事项
- **数据分析**: 需要通知数据分析团队，`page_product_detail_exposure` 事件已停止上报
- **监控调整**: 如有基于该事件的监控告警，需要相应调整

## 总结
本次修改成功移除了极光埋点的 `page_product_detail_exposure` 事件，同时保持了其他埋点系统的正常工作。修改范围明确，影响可控，兼容性良好。建议按照测试建议进行验证后发布。
