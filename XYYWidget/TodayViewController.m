//
//  TodayViewController.m
//  TodayViewController
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/6/14.
//  Copyright © 2019 XiaoYaoYao.Ltd. All rights reserved.
//

#import "TodayViewController.h"
#import <NotificationCenter/NotificationCenter.h>

//widget 宽
#define kWidgetWidth ([UIScreen mainScreen].bounds.size.width - 56.f)

//view的宽\高
#define THIRD_ViewW kWidgetWidth/4
#define THIRD_ViewH 110.f

//按钮宽高一样
#define THIRD_ButtonWH 40.f

@interface TodayViewController () <NCWidgetProviding>

@property (nonatomic, strong) UIView *homeView;
@property (nonatomic, strong) UIView *orderView;
@property (nonatomic, strong) UIView *msgView;
@property (nonatomic, strong) UIView *scanView;

@end

@implementation TodayViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self.view addSubview:self.homeView];
    [self.view addSubview:self.scanView];
    [self.view addSubview:self.msgView];
    [self.view addSubview:self.orderView];
}

- (void)makeButtonStyle:(UIButton*)btn isScan:(BOOL)scan {
    CGFloat spacing = 10;//图片和文字的上下间距
    CGSize imageSize = btn.imageView.frame.size;
    CGSize titleSize = btn.titleLabel.frame.size;
    CGSize textSize = [btn.titleLabel.text sizeWithAttributes:@{NSFontAttributeName : btn.titleLabel.font}];
    CGSize frameSize = CGSizeMake(ceilf(textSize.width), ceilf(textSize.height));
    if (titleSize.width + 0.5 < frameSize.width) {
        titleSize.width = frameSize.width;
    }
    CGFloat totalHeight = (imageSize.height + titleSize.height + spacing);
    btn.imageEdgeInsets = UIEdgeInsetsMake(- (totalHeight - imageSize.height), 0.0, 0.0, - titleSize.width);
    btn.titleEdgeInsets = UIEdgeInsetsMake(0, scan ? - (imageSize.width+20.f) : - imageSize.width, - (totalHeight - titleSize.height), scan ? -20.f : 0);
}

#pragma mark - Lazy
- (UIView *)homeView {
    if (!_homeView) {
        _homeView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, THIRD_ViewW, THIRD_ViewH)];
        UIImage *img = [UIImage imageNamed:@"ic_widget_home"];
        UIButton *homeButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [homeButton setImage:img forState:UIControlStateNormal];
        [homeButton setImage:img forState:UIControlStateHighlighted];
        homeButton.frame = CGRectMake((THIRD_ViewW-THIRD_ButtonWH)/2, (THIRD_ViewH-THIRD_ButtonWH)/2, THIRD_ButtonWH, THIRD_ButtonWH);
        [homeButton addTarget:self action:@selector(homeClick) forControlEvents:UIControlEventTouchUpInside];
        [homeButton setTitle:@"首页" forState:UIControlStateNormal];
        [homeButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
        homeButton.titleLabel.font = [UIFont systemFontOfSize:14.f];
        [_homeView addSubview:homeButton];
        [self makeButtonStyle:homeButton isScan:NO];
    }
    return _homeView;
}

- (UIView *)scanView {
    if (!_scanView) {
        _scanView = [[UIView alloc] initWithFrame:CGRectMake(THIRD_ViewW, 0, THIRD_ViewW, THIRD_ViewH)];
        UIButton *scanButton = [UIButton buttonWithType:UIButtonTypeCustom];
        UIImage *img = [UIImage imageNamed:@"ic_widget_scan"];
        scanButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [scanButton setImage:img forState:UIControlStateNormal];
        [scanButton setImage:img forState:UIControlStateHighlighted];
        scanButton.frame = CGRectMake((THIRD_ViewW-THIRD_ButtonWH)/2, (THIRD_ViewH-THIRD_ButtonWH)/2, THIRD_ButtonWH, THIRD_ButtonWH);
        [scanButton addTarget:self action:@selector(scanClick) forControlEvents:UIControlEventTouchUpInside];
        [scanButton setTitle:@"扫描进货" forState:UIControlStateNormal];
        [scanButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
        scanButton.titleLabel.font = [UIFont systemFontOfSize:14.f];
        [_scanView addSubview:scanButton];
        [self makeButtonStyle:scanButton isScan:YES];
    }
    return _scanView;
}

- (UIView *)msgView {
    if (!_msgView) {
        _msgView = [[UIView alloc] initWithFrame:CGRectMake(THIRD_ViewW*2, 0, THIRD_ViewW, THIRD_ViewH)];
        UIImage *img = [UIImage imageNamed:@"ic_widget_message"];
        UIButton *messageButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [messageButton setImage:img forState:UIControlStateNormal];
        [messageButton setImage:img forState:UIControlStateHighlighted];
        messageButton.frame = CGRectMake((THIRD_ViewW-THIRD_ButtonWH)/2, (THIRD_ViewH-THIRD_ButtonWH)/2, THIRD_ButtonWH, THIRD_ButtonWH);
        [messageButton addTarget:self action:@selector(messageClick) forControlEvents:UIControlEventTouchUpInside];
        [messageButton setTitle:@"消息" forState:UIControlStateNormal];
        [messageButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
        messageButton.titleLabel.font = [UIFont systemFontOfSize:14.f];
        [_msgView addSubview:messageButton];
        [self makeButtonStyle:messageButton isScan:NO];
    }
    return _msgView;
}

- (UIView *)orderView {
    if (!_orderView) {
        _orderView = [[UIView alloc] initWithFrame:CGRectMake(THIRD_ViewW*3, 0, THIRD_ViewW, THIRD_ViewH)];
        UIImage *img = [UIImage imageNamed:@"ic_widget_order"];
        UIButton *orderButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [orderButton setImage:img forState:UIControlStateNormal];
        [orderButton setImage:img forState:UIControlStateHighlighted];
        orderButton.frame = CGRectMake((THIRD_ViewW-THIRD_ButtonWH)/2, (THIRD_ViewH-THIRD_ButtonWH)/2, THIRD_ButtonWH, THIRD_ButtonWH);
        [orderButton addTarget:self action:@selector(orderClick) forControlEvents:UIControlEventTouchUpInside];
        [orderButton setTitle:@"订单" forState:UIControlStateNormal];
        [orderButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
        orderButton.titleLabel.font = [UIFont systemFontOfSize:14.f];
        [_orderView addSubview:orderButton];
        [self makeButtonStyle:orderButton isScan:NO];
    }
    return _orderView;
}

#pragma mark - Actions
- (void)homeClick {
    [self.extensionContext openURL:[NSURL URLWithString:@"ybm100://ybmmarket20.com/widget?home"] completionHandler:nil];
}

- (void)scanClick {
    [self.extensionContext openURL:[NSURL URLWithString:@"ybm100://ybmmarket20.com/widget?scan"] completionHandler:nil];
}

- (void)messageClick {
    [self.extensionContext openURL:[NSURL URLWithString:@"ybm100://ybmmarket20.com/widget?message"] completionHandler:nil];
}

- (void)orderClick {
    [self.extensionContext openURL:[NSURL URLWithString:@"ybm100://ybmmarket20.com/widget?order"] completionHandler:nil];
}

- (void)widgetPerformUpdateWithCompletionHandler:(void (^)(NCUpdateResult))completionHandler {
    // Perform any setup necessary in order to update the view.
    
    // If an error is encountered, use NCUpdateResultFailed
    // If there's no update required, use NCUpdateResultNoData
    // If there's an update, use NCUpdateResultNewData
    
    completionHandler(NCUpdateResultNewData);
}

@end
